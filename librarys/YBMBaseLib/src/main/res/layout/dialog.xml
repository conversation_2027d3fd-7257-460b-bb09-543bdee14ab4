<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_bg"
    android:orientation="vertical"
    >

    <TextView
        android:id="@+id/tv_state"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="App更新"
        android:textColor="#666"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:lineSpacingMultiplier="1.2"
        android:minHeight="90dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:textColor="#333"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/ll_btn"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/line_bg_top_radius"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="0.5dp"
            android:layout_weight="1"
            android:background="@drawable/common_btn_gray_color_left_bg"
            android:gravity="center"
            android:text="取消安装"
            android:visibility="gone"
            android:textColor="#999"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/btn_ok"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/common_btn_base_color_rigth_bg"
            android:gravity="center"
            android:text="立即安装"
            android:visibility="gone"
            android:textColor="@color/back_white"
            android:textSize="16sp" />
    </LinearLayout>
</LinearLayout>