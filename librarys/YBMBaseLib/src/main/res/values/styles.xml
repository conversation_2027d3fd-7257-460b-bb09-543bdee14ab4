<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">

    </style>

    <style name="Dialog" parent="android:Theme.Dialog">
        <!-- 边框 -->
        <item name="android:windowFrame">@null</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <!--背景不透明度-->
        <item name="android:backgroundDimAmount">0.55</item>

        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--弹出底部  -->
    <style name="CleanToastAnimation" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>
</resources>
