package com.ybm.app.common.download;

import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;


import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 基于okhttp3.2的一个下载工具类支持多任务、断点、多监听，不支持多线程下载。
 * 使用方法
 * DownloadTask task = new DownloadTask();
 * task.id = "1101";
 * task.saveDirPath = getExternalCacheDir().getPath() + "/";
 * task.fileName = counter +url.substring(url.lastIndexOf('/')+1,url.lastIndexOf('.'))+url.substring(url.lastIndexOf('.'));
 * task.url = "http://**********/ybm/download/ybm.apk";
 * DownloadManager.getInstance().addTask(task,new DownloadTaskListener(){});
 */
public class DownloadManager {
    private static final String TAG = "DownloadManager";
    private static DownloadManager downloadManager;
    private int mPoolSize = 6;
    private ExecutorService executorService;
    private Map<String, Future> futureMap;
    private OkHttpClient client;
    private final static int MAXTIMEOUT = 40;//最大超时时间 单位秒
    private Map<String, DownloadTask> currentTaskList = new HashMap<>();

    public void init() {
        executorService = Executors.newFixedThreadPool(mPoolSize);
        futureMap = new HashMap<>();
        client = new OkHttpClient.Builder().connectTimeout(MAXTIMEOUT, TimeUnit.SECONDS)
//                .sslSocketFactory(TrustManager.getUnsafeOkHttpClient())
                .hostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                })
                .readTimeout(MAXTIMEOUT, TimeUnit.SECONDS).followRedirects(false).followSslRedirects(false).retryOnConnectionFailure(true).build();
        client.dispatcher().setMaxRequestsPerHost(50);
    }

    public OkHttpClient getClient() {
        return client;
    }

    public Response getResponse(Request request) throws IOException {
        return getClient().newCall(request).execute();
    }

    private DownloadManager() {
        init();
    }

    private void initTaskList(SharedPreferences sp) {
        String tasks = sp.getString(TAG, "");
        List<DownloadTask> taskList = new ArrayList<>();
        try {
            taskList = new Gson().fromJson(tasks, new TypeToken<ArrayList<DownloadTask>>() {
            }.getType());
        } catch (Exception e) {

        }
        for (DownloadTask task : taskList) {
            if (task == null || task.isEmpty()) {
                continue;
            }
            if (currentTaskList == null) {
                currentTaskList = new HashMap<>();
            }
            currentTaskList.put(task.id, task);
        }
    }

    private void saveTaskList(SharedPreferences sp) {
        List<DownloadTask> taskList = new ArrayList<>();
        DownloadTask task;
        for (String id : currentTaskList.keySet()) {
            task = currentTaskList.get(id);
            if (task == null || task.downloadStatus == DownloadStatus.DOWNLOAD_STATUS_COMPLETED) {
                continue;
            }
            taskList.add(task.copy());
        }

        try {
            String str = new Gson().toJson(taskList, new TypeToken<ArrayList<DownloadTask>>() {
            }.getType());
            if (TextUtils.isEmpty(str)) {
                str = "";
            }
            sp.edit().putString(TAG, str).apply();
        } catch (Exception e) {

        }
    }

    public static DownloadManager getInstance() {
        if (downloadManager == null) {
            synchronized (DownloadManager.class) {
                if (downloadManager == null) {
                    downloadManager = new DownloadManager();
                }
            }
        }
        return downloadManager;
    }

    public void addTask(DownloadTask task) {
        if (null != currentTaskList.get(task.id) && task.downloadStatus != DownloadStatus.DOWNLOAD_STATUS_INIT) {
            Log.d(TAG, "task already exist");
            return;
        }
        currentTaskList.put(task.id, task);
        Future future = executorService.submit(task);
        futureMap.put(task.id, future);
    }

    public void addTask(String id, String url, String saveDir, String fileName, DownloadTaskListener listener) {
        DownloadTask task = new DownloadTask(id, url, saveDir, fileName);
        if (task.isEmpty()) {
            return;
        }
        task.addDownloadListener(listener);
        addTask(task);
    }

    public void addTask(String id, String url, String saveDir, String fileName, Object tag, DownloadTaskListener listener) {
        DownloadTask task = new DownloadTask(id, url, saveDir, fileName, tag);
        if (task.isEmpty()) {
            return;
        }
        task.addDownloadListener(listener);
        addTask(task);
    }

    /**
     * if return null,the task does not exist
     *
     * @param taskId
     * @return
     */
    public DownloadTask reStart(String taskId) {
        DownloadTask downloadTask = getCurrentTaskById(taskId);
        if (downloadTask != null) {
            if (downloadTask.downloadStatus == DownloadStatus.DOWNLOAD_STATUS_PAUSE) {
                Future future = executorService.submit(downloadTask);
                futureMap.put(downloadTask.id, future);
            }
        }
        return downloadTask;
    }


    /**
     * 重新下载
     *
     * @return
     */
    public DownloadTask reDownload(DownloadTask downloadTask) {
        if (downloadTask != null) {
            if (downloadTask.downloadStatus != DownloadStatus.DOWNLOAD_STATUS_DOWNLOADING) {
                pause(downloadTask);
            }
            currentTaskList.remove(downloadTask.id);
            futureMap.remove(downloadTask.id);
            downloadTask.downloadStatus = DownloadStatus.DOWNLOAD_STATUS_INIT;
            downloadTask.completedSize = 0;
            downloadTask.toolSize = 0;
            Future future = executorService.submit(downloadTask);
            futureMap.put(downloadTask.id, future);
        }
        return downloadTask;
    }

    public void addDownloadListener(DownloadTask task, DownloadTaskListener listener) {
        if (task != null)
            task.addDownloadListener(listener);
    }

    public void removeDownloadListener(DownloadTask task, DownloadTaskListener listener) {
        if (task != null)
            task.removeDownloadListener(listener);
    }

    public void cancel(DownloadTask task) {
        if (task == null) {
            return;
        }
        task.cancel();
        removeTask(task.id);
    }

    public void cancel(String taskId) {
        DownloadTask task = getTaskById(taskId);
        if (task != null) {
            cancel(task);
        }
    }

    public void finish(String taskId) {
        removeTask(taskId);
    }

    //完成与取消都可以调用
    private void removeTask(String taskId) {
        currentTaskList.remove(taskId);
        futureMap.remove(taskId);
        clear();
    }

    private void clear() {
        if (currentTaskList == null || currentTaskList.isEmpty()) {
            if (futureMap == null || futureMap.isEmpty()) {
                futureMap = null;
                currentTaskList = null;
                executorService = null;
                downloadManager = null;
            }
        }
    }

    public void pause(DownloadTask task) {
        task.pause();
    }

    public void pause(String taskId) {
        DownloadTask task = getTaskById(taskId);
        if (task != null) {
            pause(task);
        }
    }


    public DownloadTask getCurrentTaskById(String taskId) {
        return currentTaskList.get(taskId);
    }

    public DownloadTask getTaskById(String taskId) {
        return getCurrentTaskById(taskId);
    }

}
