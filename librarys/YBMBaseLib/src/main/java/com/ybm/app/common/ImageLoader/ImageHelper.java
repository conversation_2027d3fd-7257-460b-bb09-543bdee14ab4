package com.ybm.app.common.ImageLoader;

import android.content.Context;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;


public class ImageHelper {

    private static Glide glide;

    /**
     * No instances.
     */
    private ImageHelper() {
    }

    public static RequestManager with(Context context) {
        if (glide == null) {
            synchronized (Glide.class) {
                if (glide == null) {
                    glide = Glide.get(context.getApplicationContext());
                    return glide.with(context);
                }
            }
        }
        return Glide.with(context);
    }


}