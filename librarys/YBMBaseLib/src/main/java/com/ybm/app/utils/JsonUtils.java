package com.ybm.app.utils;

import android.util.Log;

import com.apkfuns.logutils.LogUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

/**
 * json 解析工具类
 */

public class JsonUtils {
    private static Gson gson ;

    public static Gson getGson(){
        if(gson == null){
            synchronized (JsonUtils.class){
                if(gson == null){
                    gson = new Gson();
                }
            }
        }
        return gson;
    }

    public static <T> T fromJson(String json, Class<T> classOfT) {
        try {
            return getGson().fromJson(json,classOfT);
        }catch (Throwable e){
            return null;
        }

    }

    /**
     * @desc HashMap 使用主动传入TypeToken方式，否则不会讲LinkedTreeMap转为HashMap
     * @param json
     * @return
     * @param <T>
     */
    public static <T> T fromJson(String json) {
        try {
            return getGson().fromJson(json,new TypeToken<T>(){}.getType());
        }catch (Throwable e){
            return null;
        }

    }

    public static <T> T fromJson(String json, Type typeOfT){
        try {
            return getGson().fromJson(json,typeOfT);
        }catch (Throwable e){
            // Log.e("xyd","解析错误信息： " + e.toString());
            LogUtils.e("解析错误信息： " + e.toString());
            e.printStackTrace();
            return null;
        }
    }

    public static String toJson(Object src){
        try {
            return getGson().toJson(src);
        }catch (Throwable e){
            return "";
        }
    }

    public static String toJson(Object src,Type typeOfT){
        try {
            return getGson().toJson(src,typeOfT);
        }catch (Throwable e){
            return "";
        }
    }
}
