package com.ybm.app.common

import android.util.Log
import com.google.gson.Gson
import com.tencent.tddiag.logger.TDLog
import com.xyyio.analysis.stat.XyyIoSDK
import com.ybm.app.utils.SpUtil
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.waf.XyyWafManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody
import okio.Buffer
import org.json.JSONObject
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.nio.charset.UnsupportedCharsetException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * WAF拦截器
 */
class WafInterceptor : Interceptor {

    val logParams = mutableMapOf<String, String?>()

    override fun intercept(chain: Interceptor.Chain): Response {
        //设置唯一标识，使用merchantId
        val merchantId = SpUtil.readString("MERCHANTID", "")
        if (!merchantId.isNullOrEmpty()) {
            XyyWafManager.instance?.setAccount(merchantId)
        }
        val request = chain.request()
//        val requestBody = request.body
//        //获取参数
//        val paramList = mutableListOf<String>()
//        if (requestBody is FormBody) {
//            for (i in 0 until requestBody.size) {
//                paramList.add("${requestBody.name(i)}=${URLEncoder.encode(requestBody.value(i), "utf-8")}")
//            }
//        }
//        val input = paramList.joinToString("&")
//        //生成vmpHash
//        val wHash = XyyWafManager.instance?.getPostWHASH(input)
//        val wToken = XyyWafManager.instance?.getWToken(wHash)
        val wafParams = getWafParams(request)
        val finalRequest = request.newBuilder()
            .addHeader("wToken", wafParams.second)
            .addHeader("ali_sign_whash", wafParams.first)
            .build()
        val response = chain.proceed(finalRequest)
        val responseCookie = StringBuilder()
        for (item in response.headers("Set-Cookie")) {
            responseCookie.append(item).append(";")
        }
        val responseBody = try {
            if (response.body == null) "" else getResponseBody(response.body!!)
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
        val recheck = XyyWafManager.instance?.checkResponse(responseCookie.toString(), responseBody?: "")
        if (recheck == 1) {
            logForResponse(responseCookie.toString(), responseBody)
            runBlocking {
                withContext(Dispatchers.Main) {
                    doShow()
                }
            }
            response.close()
            return intercept(chain)
        }
        return response
    }

    /**
     * 获取wHash和wToken
     */
    private fun getWafParams(request: Request): Pair<String, String> {
        var wHash = ""
        var wToken = ""
        var logInput = ""
        Log.i("waf_ecapp_url" , request.url.toString())
        if (request.method == "POST") {
            val requestBody = request.body
            //获取参数
            val buffer = Buffer()
            requestBody?.writeTo(buffer)
            val input = buffer.readUtf8()
            logInput = input
            //生成vmpHash
            if (!input.isNullOrEmpty()) {
                wHash = XyyWafManager.instance?.getPostWHASH(input)?: ""
            }
            Log.i("waf_ecapp_input", input)
        } else {
            val input = request.url.toString()
            wHash = XyyWafManager.instance?.getGetWHASH(input)?: ""
        }
        wToken = XyyWafManager.instance?.getWToken(wHash)?: ""
        Log.i("waf_ecapp_wHash", wHash)
        Log.i("waf_ecapp_wToken", wToken)
        logForRequest(wHash, wToken, logInput, request)
        return wHash to wToken
    }

    private suspend fun doShow() = suspendCoroutine { con ->
        val activity = Abase.getCurrentActivity()
        activity?.runOnUiThread {
            XyyWafManager.instance?.showCaptchaAlert(activity, object: XyyWafManager.CaptchaCallback{
                override fun success(str: String?) {
                    XyyWafManager.instance?.dismissCaptcha()
                    con.resume(Any())
                }

                override fun failed(str: String?) {
                    Log.i("doShow_failed", str?: "")
                }

                override fun error(code: Int, str: String?) {
                    Log.i("doShow_error", str?: "")
                }

            })
        }
    }

    @Throws(java.lang.Exception::class)
    private fun getResponseBody(responseBody: ResponseBody): String? {
        val source = responseBody.source()
        source.request(Long.MAX_VALUE)
        val buffer: Buffer = source.buffer
        var charset = StandardCharsets.UTF_8
        val contentType = responseBody.contentType()
        if (contentType != null) {
            try {
                charset = contentType.charset(StandardCharsets.UTF_8)
            } catch (e: UnsupportedCharsetException) {

            }
        }
        if (responseBody.contentLength() != 0L) {
            assert(charset != null)
            return buffer.clone().readString(charset)
        }
        return ""
    }

    private fun logForRequest(wHash: String?, wToken: String?, input: String?, request: Request?) {
        try {
            logParams["wHash"] = wHash
            logParams["wToken"] = wToken
            logParams["input"] = input
            logParams["requestMethod"] = request?.method
            logParams["request_time"] = "${System.currentTimeMillis()}"
            logParams["url"] = request?.url.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun logForResponse(cookie: String?, responseBody: String?) {
        try {
            logParams["cookie"] = cookie
            logParams["responseBody"] = responseBody
            logParams["merchantId"] = SpUtil.readString("MERCHANTID", "")
            logParams["response_time"] = "${System.currentTimeMillis()}"
            val trackStr = Gson().toJson(logParams)
            val obj = JSONObject()
            obj.put("ecception_content string", trackStr)
            XyyIoSDK.instance.track("check_android_waf_exception", obj)
            TDLog.i("check_android_waf_exception_bugly",trackStr)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}