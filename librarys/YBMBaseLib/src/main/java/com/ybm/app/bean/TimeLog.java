package com.ybm.app.bean;

import android.text.TextUtils;
import android.util.Log;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.BuildConfig;
import com.ybm.app.common.apicache.MD5Utils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class TimeLog {
    public long start;//开始建立network时间 组装数据时间 应用层
    public long step1;//开始发送请求到网络层 应用层
    public long step2;//开始发送请求到网络 网络层
    public long step3;//开始读取数据 网络层
    public long step4;//完成请求 网络层
    public long step5;//解析数据  应用层
    public long stop;//填充数据  应用层
    public String url = "";
    public boolean isStoreNetData = false;
    public String cacheKey ="";
    public String cacheMD5 ="";
    public int[] time = new int[7];

    public TimeLog() {

    }

    public String toStringLog() {
        int temp = 0;
        boolean isShow = true;
        StringBuffer sb = new StringBuffer();
        if (start > 0 && step1 > 0) {
            temp = (int) (step1 - start);
            time[0] = temp;
            if (isShow) {
                sb.append("时间:").append("1:").append(temp).append("ms\t");
            }
            if (step2 > 0) {
                temp = (int) (step2 - step1);
                time[1] = temp;
                if (isShow) {
                    sb.append("2:").append(temp).append("ms\t");
                }
                if (step3 > 0) {
                    temp = (int) (step3 - step2);
                    time[2] = temp;
                    if (isShow) {
                        sb.append("3:").append(temp).append("ms\t");
                    }
                    if (step4 > 0) {
                        temp = (int) (step4 - step3);
                        time[3] = temp;
                        if (isShow) {
                            sb.append("4:").append(temp).append("ms\t");
                        }
                        if (step5 > 0) {
                            temp = (int) (step5 - step4);
                            time[4] = temp;
                            if (isShow) {
                                sb.append("5:").append(temp).append("ms\t");
                            }
                            if (stop > 0) {
                                temp = (int) (stop - step5);
                                time[5] = temp;
                                time[6] = (int) (stop - start);
                                if (isShow) {
                                    sb.append("6:").append(temp).append("ms\t");
                                    sb.append("总:").append(stop - start).append("ms\t");
                                }
                            }
                        }
                    }
                }
            }
        }
       return sb.toString();
    }

    private String format(long time){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new Date(time));
    }

    public String getCacheKey(){
        return MD5Utils.md5(cacheKey);
    }

    /**
     * 保存缓存数据的md值
     * @param cache
     */
    public void setCachMd5(String cache){
        if(TextUtils.isEmpty(cache)){
            cacheMD5 = "";
        }else {
            cacheMD5 = MD5Utils.md5(cache);
        }
    }

    /**
     * 判断网络数据与缓存数据是否一样
     * @param content
     * @return
     */
    public boolean isEmptyCache(String content){
        if(TextUtils.isEmpty(cacheMD5)){
            return false;
        }
        if(TextUtils.isEmpty(content)){
            return false;
        }
        return cacheMD5.equals(MD5Utils.md5(content));
    }

}