package com.ybm.app.common;

import android.os.Handler;
import android.os.Looper;

import com.ybm.app.bean.HttpResponse;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.JsonUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

import okhttp3.Response;

/**
 * 支持泛型
 * @param <T>
 */
public abstract class BeanCallback<T> extends BaseCallback {
    protected Type type;//

    public BeanCallback() {
        this.type = getGenericType(0,getClass());
    }

    public  T json(String str,Type type){
        return JsonUtils.fromJson(str,type);
    }

    public abstract void onSuccess(T result,HttpResponse response);//200 就会调用

    @Override
    public final void onSuccess(final HttpResponse response) {
        T t = null;
        if(response !=null && response.content!= null){
            try {
                t = json(response.content,type);
            }catch (Throwable e){
                t = null;
            }
        }
        final T finalT = t;
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if(needCancel()){
                    return;
                }
                onSuccess(finalT,response);
            }
        });
    }

    //获取泛型
    private  Type getGenericType(int index, Class<?> subclass) {
        Type superclass = subclass.getGenericSuperclass();
        if (!(superclass instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) superclass).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            throw new RuntimeException("Index outof bounds");
        }

        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return params[index];
    }
}
