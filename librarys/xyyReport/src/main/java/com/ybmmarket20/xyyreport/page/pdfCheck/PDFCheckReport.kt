package com.ybmmarket20.xyyreport.page.pdfCheck

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PDFCheckReport {

    @JvmStatic
    fun pvTrack(context: Context, fileType: String?, fileId: String?) {
        if (fileId == null) return
        val spm = SpmUtil.getSpmPv("fileView_$fileType#$fileId-0_0")
        SpmLogUtil.print("PV-PDF查看")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }

    @JvmStatic
    private fun pdfCheckClick(context: Context, componentName: String?, btnPosition: Int, btnText: String?, orderNo: String?, isOrder: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = componentName
                spmD = "btn@$btnPosition"
            }
            val scm = ScmBean().apply {
                scmA = "appFE"
                scmB = "0"
                scmC = "all_0"
                scmD = if (orderNo != null && isOrder == "1") "text-${btnText}_order-$orderNo" else "text-$btnText"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    @JvmStatic
    fun pdfCheckShareClick(context: Context, orderNo: String?, isOrder: String?) {
        SpmLogUtil.print("PV-PDF查看-分享点击")
        pdfCheckClick(context, "fileViewHeader@Z", 2, "分享", orderNo, isOrder)
    }

    @JvmStatic
    fun pdfCheckDownloadClick(context: Context, orderNo: String?, isOrder: String?) {
        SpmLogUtil.print("PV-PDF查看-下载到邮箱")
        pdfCheckClick(context, "ftDownload@Z", 1, "下载到邮箱", orderNo, isOrder)
    }
}