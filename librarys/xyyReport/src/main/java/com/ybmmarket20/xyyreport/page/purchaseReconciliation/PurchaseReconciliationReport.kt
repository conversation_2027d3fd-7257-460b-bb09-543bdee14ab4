package com.ybmmarket20.xyyreport.page.purchaseReconciliation

import android.content.Context
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PurchaseReconciliationReport {

    @JvmStatic
    fun pvTrack(context: Context) {
        val spm = SpmUtil.getSpmPv("purchaseStatement_0-0_0")
        SpmLogUtil.print("采购对账单-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }

}