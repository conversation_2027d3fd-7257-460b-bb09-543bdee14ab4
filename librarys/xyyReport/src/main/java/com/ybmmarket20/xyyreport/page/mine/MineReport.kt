package com.ybmmarket20.xyyreport.page.mine

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.report.ReportPageSubModuleExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object MineReport {

    @JvmStatic
    fun pvTrack(context: Context) {
        val spm = SpmUtil.getSpmPv("me_0-0_0")
        SpmLogUtil.print("我的-PV")
        ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
    }

    private fun clickSubModule(context: Context, businessType: String?, moduleType: String?, subModuleType: String?, position: Int, text: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "$moduleType"
                spmD = "$subModuleType@$position"
            }
            val scm = ScmBean().apply {
                scmA = "$businessType"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-$text"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    /**
     * 我的财富子模块点击
     */
    private fun clickSubModuleMyWealth(context: Context, position: Int, text: String?) {
        SpmLogUtil.print("我的-财富栏子模块点击-$text")
        clickSubModule(context, "appFE", "wealth@2", "btn", position, text)
    }

    fun clickSubModuleMyWealthShoppingGold(context: Context) {
        clickSubModuleMyWealth(context, 1, "购物金")
    }

    fun clickSubModuleMyWealthCoupon(context: Context) {
        clickSubModuleMyWealth(context, 2, "优惠券")
    }

    fun clickSubModuleMyWealthRedEnvelope(context: Context) {
        clickSubModuleMyWealth(context, 3, "红包")
    }

    fun clickSubModuleMyWealthMyWealth(context: Context) {
        clickSubModuleMyWealth(context, 4, "我的财富")
    }

    fun clickSubModuleMyWealthGoRecharge(context: Context) {
        clickSubModuleMyWealth(context, 5, "去充值")
    }

    /**
     * 订单子模块点击
     */
    fun clickSubModuleOrder(context: Context, position: Int, text: String?) {
        SpmLogUtil.print("我的-订单栏子模块点击-$text")
        clickSubModule(context, "order", "orderIcon@3", "icon", position + 1, text)
    }

    /**
     * 订单子模块点击-全部
     */
    fun clickSubModuleOrderAll(context: Context) {
        SpmLogUtil.print("我的-订单栏子模块点击-全部")
        clickSubModule(context, "order", "orderIcon@3","subTitle", 1, "全部")
    }

    fun clickCommonToolItem(context: Context, position: Int, text: String?) {
        SpmLogUtil.print("我的-工具栏按钮点击-$text-$position")
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "functionIcon@5"
                spmD = "icon@${position+1}"
            }
            val scm = ScmBean().apply {
                scmA = "admin"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-$text"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }

    fun trackMineBindWechatExposure(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            SpmLogUtil.print("我的-绑定微信领红包-组件曝光")
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "meHeader@1"
                spmD = "btn@4"
            }
            val scm = ScmBean().apply {
                scmA = "appFE"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-绑定微信领红包"
            }
            ReportUtil.clickTrack(context, ReportPageSubModuleExposureBean(), spm, scm)
        }
    }

    fun trackMineBindWechatClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            SpmLogUtil.print("我的-绑定微信领红包-组件点击")
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "meHeader@1"
                spmD = "btn@4"
            }
            val scm = ScmBean().apply {
                scmA = "appFE"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-绑定微信领红包"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }
}