package com.ybmmarket20.xyyreport.page.payResult

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

object PayResultOrderInfoReport {
    fun getBaseSpm(context: Context): SpmBean {
        return SpmUtil.checkAnalysisContextReturn(context) { con ->
            val spm = con.getSpmCtn()?.newInstance()?: SpmBean()
            spm.spmC = "orderInfo@2"
            return@checkAnalysisContextReturn spm
        }!!
    }

    fun getBaseScm(context: Context): ScmBean {
        return SpmUtil.checkAnalysisContextReturn(context) { con ->
            val scm = con.getScmCnt()?.newInstance()?: ScmBean()
            scm.scmA = "order"
            scm.scmB = "0"
            scm.scmC = "all_0"
            return@checkAnalysisContextReturn scm
        }!!
    }

    fun trackClickSubModuleOrder(context: Context,btnPos:Int,text:String) {
        SpmLogUtil.print("在线支付-支付结果页-订单信息-子模块点击")
        SpmUtil.checkAnalysisContext(context) {
            val spm = getBaseSpm(context).apply {
                spmD = "btn@${btnPos}"
            }
            val scm = getBaseScm(context).apply {
                scmD = "text-${text}"
            }
            ReportUtil.clickTrack(context, ReportActionSubModuleClickBean(), spm, scm)
        }
    }
}