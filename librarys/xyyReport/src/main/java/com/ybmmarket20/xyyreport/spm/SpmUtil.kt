package com.ybmmarket20.xyyreport.spm

import android.content.Context
import com.ybmmarket20.xyyreport.session.SessionManager

object SpmUtil {

    /**
     * 获取pv的spm
     */
    @JvmStatic
    fun getSpmPv(pageCode: String?): SpmBean {
        val spmE = "${SessionManager.get().getSession()}${SessionManager.get().newPageSession()}"
        val spm = SpmBean(SpmConstant.SPM_A, pageCode, SpmConstant.SPM_DEFAULT, SpmConstant.SPM_DEFAULT, spmE)
        return spm
    }

    /**
     * 获取pv的spm
     */
    @JvmStatic
    fun getSpmPvFromStr(spm: String?): SpmBean {
        return getSpmBeanFromStr(spm).apply {
            spmC = SpmConstant.SPM_DEFAULT
            spmD = SpmConstant.SPM_DEFAULT
        }
    }

    /**
     * 获取组件曝光的spm
     */
    fun getSpmComponentExposure(context: Context, pageCode: String?, componentName: String?): SpmBean? {
        if (context is XyyReportActivity) {
            val spmE = context.getSpmCtn()?.spmE
            val spm = SpmBean(SpmConstant.SPM_A, pageCode, componentName, SpmConstant.SPM_DEFAULT, spmE)
            return spm
        }
        return null
    }

    /**
     * 获取组件曝光的spm
     */
    fun getSpmComponentExposureFromStr(spm: String?): SpmBean {
        return getSpmBeanFromStr(spm).apply {
            spmD = SpmConstant.SPM_DEFAULT
        }
    }

    @JvmStatic
    fun getSpmBeanFromStr(spmStr: String?): SpmBean {
        return try {
            val spmArr = spmStr?.split(".")
            if (spmArr == null || spmArr.size < 4) SpmBean()
            else SpmBean(spmArr[0], spmArr[1], spmArr[2], spmArr[3], null)
        } catch (e: Exception) {
            e.printStackTrace()
            SpmBean()
        }
    }

    @JvmStatic
    fun getScmBeanFromStr(scmStr: String?): ScmBean {
        return try {
            val scmArr = scmStr?.split(".")
            if (scmArr == null || scmArr.size < 4) ScmBean()
            else ScmBean(scmArr[0], scmArr[1], scmArr[2], scmArr[3], null)
        } catch (e: Exception) {
            e.printStackTrace()
            ScmBean()
        }
    }

    @JvmStatic
    fun setSpmE(context: Context, spm: ISpm?) {
        if (spm is SpmBean && context is XyyReportActivity) {
            spm.spmE = context.getSpmCtn()?.spmE
        }
    }

    @JvmStatic
    fun setNewSpmE(spm: ISpm?) {
        if (spm is SpmBean) {
            spm.spmE = "${SessionManager.get().getSession()}${SessionManager.get().newPageSession()}"
        }
    }

    @JvmStatic
    fun setScmE(scm: ISpm?) {
        if (scm is ScmBean) {
            scm.scmE = SessionManager.get().newModuleSession()
        }
    }

    @JvmStatic
    fun fixSpmESession(spm: ISpm?) {
        if (spm !is SpmBean) return
        if (spm.spmE.isNullOrEmpty()) return
        val spmE = spm.spmE
        spmE?.apply {
            if (!this.startsWith(SessionManager.get().getSession())) {
                //session有变化
                val randomStr = if (this.length == 14) {
                    this.substring(8, 14)
                } else SessionManager.get().newPageSession()
                spm.spmE = "${SessionManager.get().getSession()}$randomStr"
            }
        }
    }

    fun createScmE(scmId: String?): String? = "$scmId${SessionManager.get().newGoodsScmRandom()}"

    fun containsNull(iSpm: ISpm?): Boolean {
        if (iSpm == null) return false
        if (iSpm is SpmBean) {
            if (iSpm.spmA == null || iSpm.spmB == null || iSpm.spmC == null || iSpm.spmD == null || iSpm.spmE == null) return true
        } else if (iSpm is ScmBean) {
            if (iSpm.scmA == null || iSpm.scmB == null || iSpm.scmC == null || iSpm.scmD == null || iSpm.scmE == null) return true
        }
        return false
    }

    @JvmStatic
    fun checkAnalysisContext(context: Context, block: (XyyReportActivity)->Unit) {
        if (context is XyyReportActivity) block(context)
    }

    fun <T> checkAnalysisContextReturn(context: Context, block: (XyyReportActivity)->T?): T? {
        return if (context is XyyReportActivity) block(context) else null
    }

    fun checkAnalysisContextAndEnable(context: Context, block: (XyyReportActivity)->Unit) {
        if (context is XyyReportActivity && context.getEnableAnalysis()) block(context)
    }

    fun checkReportSpmFormat(str: String?): String? {
        var result = str
        val symbolList = listOf(".", "_", "-", "~", "|", "@")
        symbolList.forEach {
            result = result?.replace(it, "")
        }
        return result
    }
}