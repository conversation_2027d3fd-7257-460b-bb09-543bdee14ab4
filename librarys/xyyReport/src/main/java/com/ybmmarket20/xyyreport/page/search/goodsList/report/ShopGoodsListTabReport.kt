package com.ybmmarket20.xyyreport.page.search.goodsList.report

import android.content.Context
import com.ybmmarket20.report.ReportActionProductButtonClickBean
import com.ybmmarket20.report.ReportActionSubModuleGoodsClickBean
import com.ybmmarket20.report.ReportPageSubModuleGoodsExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.page.common.addCart.AddCartPopupWindowReport
import com.ybmmarket20.xyyreport.page.search.goodsList.BaseGoodsListReport
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData

class ShopGoodsListTabReport: BaseGoodsListReport() {

    override fun getBaseSpm(context: Context): SpmBean {
        return SpmUtil.checkAnalysisContextReturn(context) { con ->
            val spm = con.getSpmCtn()?.newInstance()?: SpmBean()
            spm.spmC = "shopProdList@Z"
            return@checkAnalysisContextReturn spm
        }!!
    }

    override fun getBaseScm(context: Context): ScmBean {
        return SpmUtil.checkAnalysisContextReturn(context) { con ->
            return@checkAnalysisContextReturn ScmBean().apply {
                scmA = "shop"
                scmB = "0"
                scmC = "all_0"
            }
        }!!
    }

    override fun onGoodsExposure(context: Context, rowsBeanInfo: IRowsBeanInfo?, position: Int) {
        val shopTabInfo = rowsBeanInfo?.getShopTabInfo()?: return
        val spm = getBaseSpm(context).apply {
            spmD = if (shopTabInfo.isShowFilter){
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_sortNav@${shopTabInfo.shopSortNavIndex}_prod@${position + 1}"
            } else {
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_prod@${position + 1}"
            }
        }
        val scm = getBaseScm(context).apply {
            scmD = if (shopTabInfo.isShowFilter){
                "leftNavText-${shopTabInfo.shopLeftNavText}_sortNavText-${shopTabInfo.shopSortNavText}_prod-${rowsBeanInfo.getSpmProductId()}"
            } else {
                "leftNavText-${shopTabInfo.shopLeftNavText}_prod-${rowsBeanInfo.getSpmProductId()}"
            }
            scmE = rowsBeanInfo.getScmId()
        }
        val goodsExposure = ReportPageSubModuleGoodsExposureBean().apply {
            this.productId = rowsBeanInfo.getSpmProductId()
            this.productName = rowsBeanInfo.getSpmProductName()
        }
        ReportUtil.goodsExposureTrack(context, goodsExposure, spm, scm)
    }

    override fun onGoodsClick(context: Context, rowsBeanInfo: IRowsBeanInfo?, position: Int): String {
        val scmE = "${rowsBeanInfo?.getScmId()?: ""}${SessionManager.get().newGoodsScmRandom()}"
        val shopTabInfo = rowsBeanInfo?.getShopTabInfo()?: return scmE
        val spm = getBaseSpm(context).apply {
            spmD = if (shopTabInfo.isShowFilter) {
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_sortNav@${shopTabInfo.shopSortNavIndex}_prod@${position + 1}"
            } else {
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_prod@${position + 1}"
            }
        }
        val scm = getBaseScm(context).apply {
            scmD = if (shopTabInfo.isShowFilter) {
                "leftNavText-${shopTabInfo.shopLeftNavText}_sortNavText-${shopTabInfo.shopSortNavText}_prod-${rowsBeanInfo.getSpmProductId()}"
            } else {
                "leftNavText-${shopTabInfo.shopLeftNavText}_prod-${rowsBeanInfo.getSpmProductId()}"
            }
            this.scmE = scmE
        }
        val goodsClick = ReportActionSubModuleGoodsClickBean().apply {
            this.productId = rowsBeanInfo.getSpmProductId()
            this.productName = rowsBeanInfo.getSpmProductName()
        }
        ReportUtil.track(context, goodsClick, spm, scm)
        return scmE
    }

    override fun onGoodsBtnClick(context: Context, rowsBeanInfo: IRowsBeanInfo?, position: Int, btnNum: String?, content: String?, trackCallback: ((context: Context, trackData: TrackData?)->Unit)?, scmE: String?) {
        val shopTabInfo = rowsBeanInfo?.getShopTabInfo()?: return
        val spm = getBaseSpm(context).apply {
            spmD = if (shopTabInfo.isShowFilter) {
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_sortNav@${shopTabInfo.shopSortNavIndex}_prod@${position + 1}_btn@$btnNum"
            } else {
                "leftNav@${shopTabInfo.shopLeftNavIndex + 1}_prod@${position + 1}_btn@$btnNum"
            }
        }
        val scm = getBaseScm(context).apply {
            scmD = if (shopTabInfo.isShowFilter) {
                "leftNavText-${shopTabInfo.shopLeftNavText}_sortNavText-${shopTabInfo.shopSortNavText}_prod-${rowsBeanInfo.getSpmProductId()}_text-$content"
            } else {
                "leftNavText-${shopTabInfo.shopLeftNavText}_prod-${rowsBeanInfo.getSpmProductId()}_text-$content"
            }
            this.scmE = scmE
        }
        val goodsBtnClick = ReportActionProductButtonClickBean().apply {
            this.productId = rowsBeanInfo.getSpmProductId()
            this.productName = rowsBeanInfo.getSpmProductName()
        }
        ReportUtil.track(context, goodsBtnClick, spm, scm)
        AddCartPopupWindowReport.addExtensionForAddCartPopupWindow(context, spm, scm, true)
        AddCartPopupWindowReport.addCartActionTrack(context, TrackData(spm, scm))
    }

    override fun getLogTag(): String = "店铺"

    override fun getSpellGroupText(): String = "参团"

    override fun getWholeSaleText(): String = "抢购"
}