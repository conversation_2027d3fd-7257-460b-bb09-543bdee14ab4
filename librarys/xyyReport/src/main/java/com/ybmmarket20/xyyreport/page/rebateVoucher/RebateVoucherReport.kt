package com.ybmmarket20.xyyreport.page.rebateVoucher

import android.content.Context
import com.xyy.xyyreport.IReport
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageSubModuleExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

/**
 * 消费返弹窗
 */
object RebateVoucherReport {

    private const val TRACK_REBATE_VOUCHER_COMPONENT_POS_ORDER_LIST = 2
    private const val TRACK_REBATE_VOUCHER_COMPONENT_POS_MINE = 3

    private fun trackRebateVoucherComponent(context: Context, iReport: IReport, componentPos: Int) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtnNewInstance()?.apply {
                this.spmC = "cashBack@$componentPos"
                this.spmD = "btn@1"
                SpmUtil.setSpmE(it, this)
            }
            val scm = ScmBean().apply {
                scmA = "order"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-下单返"
                SpmUtil.setScmE(this)
            }
            ReportUtil.track(it, iReport, spm, scm)
        }
    }

    private fun trackRebateVoucherComponentExposure(context: Context, componentPos: Int) {
        trackRebateVoucherComponent(context, ReportPageSubModuleExposureBean(), componentPos)
    }

    private fun trackRebateVoucherComponentClick(context: Context, componentPos: Int) {
        trackRebateVoucherComponent(context, ReportActionSubModuleClickBean(), componentPos)
    }

    fun trackRebateVoucherComponentExposureForOrderList(context: Context) {
        SpmLogUtil.print("订单列表-充值消费返-曝光")
        trackRebateVoucherComponentExposure(context, TRACK_REBATE_VOUCHER_COMPONENT_POS_ORDER_LIST)
    }

    fun trackRebateVoucherComponentExposureForMine(context: Context) {
        SpmLogUtil.print("我的页面-充值消费返-曝光")
        trackRebateVoucherComponentExposure(context, TRACK_REBATE_VOUCHER_COMPONENT_POS_MINE)
    }

    fun trackRebateVoucherComponentClickForOrderList(context: Context) {
        SpmLogUtil.print("订单列表-充值消费返-点击")
        trackRebateVoucherComponentClick(context, TRACK_REBATE_VOUCHER_COMPONENT_POS_ORDER_LIST)
    }

    fun trackRebateVoucherComponentClickForMine(context: Context) {
        SpmLogUtil.print("我的页面-充值消费返-点击")
        trackRebateVoucherComponentClick(context, TRACK_REBATE_VOUCHER_COMPONENT_POS_MINE)
    }
}