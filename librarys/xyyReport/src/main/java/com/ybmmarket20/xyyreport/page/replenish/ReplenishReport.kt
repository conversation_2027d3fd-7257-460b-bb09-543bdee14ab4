package com.ybmmarket20.xyyreport.page.replenish

import android.content.Context
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageComponentExposureBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil

/**
 * 一键补货
 */
object ReplenishReport {

    /**
     * 一键补货弹窗曝光
     */
    fun replenishDialogPV(context: Context): String? {
        return SpmUtil.checkAnalysisContextReturn(context) {
            val spmE = "${SessionManager.get().getSession()}${SessionManager.get().newPageSession()}"
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmB = "diaChannelSpecial_0-0_0"
                spmC = "0"
                spmD = "0"
                this.spmE = spmE
            }
            SpmLogUtil.print("一键补货-弹窗-PV")
            ReportUtil.track(it, ReportPageExposureBean(), spm, null)
            spmE
        }
    }

    /**
     * 一键补货弹窗曝光
     */
    fun replenishDialogExposure(context: Context, spmE: String?) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmB = "diaChannelSpecial_0-0_0"
                spmC = "popUpCoupon@1"
                spmD = "0"
                this.spmE = spmE
            }
            SpmLogUtil.print("一键补货-弹窗-曝光")
            ReportUtil.track(it, ReportPageComponentExposureBean(), spm, null)
        }
    }

    private fun pv(context: Context, tabType: String) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = SpmUtil.getSpmPv("keyReplenish_0-0_$tabType")
            SpmLogUtil.print("一键补货列表-PV")
            ReportUtil.pvTrack(context, ReportPageExposureBean(), spm)
        }
    }

    /**
     * pv-拼团商品
     */
    fun pvPTSP(context: Context) {
        pv(context, "PTSP")
    }

    /**
     * pv-拼团凑单商品
     */
    fun pvCDSP(context: Context) {
        pv(context, "CDSP")
    }

    /**
     * 搜索框点击
     */
    fun replenishSearchClick(context: Context) {
        SpmUtil.checkAnalysisContext(context) {
            val spm = it.getSpmCtn()?.newInstance()?.apply {
                spmC = "keyReplenishHeader@1"
                spmD = "btn@1"
                SpmUtil.setSpmE(it, this@apply)
            }
            val scm = ScmBean().apply {
                scmA = "recommend"
                scmB = "0"
                scmC = "all_0"
                scmD = "text-搜索"
                SpmUtil.setScmE(this@apply)
            }
            SpmLogUtil.print("一键补货列表-搜索框点击")
            ReportUtil.track(it, ReportActionSubModuleClickBean(), spm, scm)
        }
    }


}