<?xml version="1.0" encoding="UTF-8"?>
<issues format="5" by="lint 3.6.3" client="gradle" variant="release" version="3.6.3">

    <issue
        id="ObsoleteLintCustomCheck"
        message="<PERSON><PERSON> found an issue registry (`androidx.appcompat.AppCompatIssueRegistry`) which requires a newer API level. That means that the custom lint checks are intended for a newer lint version; please upgrade">
        <location
            file="../../../../../.gradle/caches/transforms-2/files-2.1/4bcbea224e1f5a97ce3a89a5d02de9cd/appcompat-1.2.0/jars/lint.jar"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        char[] hexBytes = hexString.toUpperCase().toCharArray();"
        errorLine2="                                    ~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/ConvertUtils.java"
            line="75"
            column="37"/>
    </issue>

    <issue
        id="DiscouragedPrivateApi"
        message="Reflective access to getService, which is not part of the public SDK and therefore likely to change in future Android releases"
        errorLine1="            Method declaredMethod = Toast.class.getDeclaredMethod(&quot;getService&quot;);"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/ToastUtils.java"
            line="286"
            column="37"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="                Class mINotificationManagerClass = Class.forName(&quot;android.app.INotificationManager&quot;);"
        errorLine2="                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/ToastUtils.java"
            line="290"
            column="52"/>
    </issue>

    <issue
        id="ShowToast"
        message="Toast created but not shown: did you forget to call `show()` ?"
        errorLine1="                sToast = Toast.makeText(Abase.getContext(), text, duration);"
        errorLine2="                         ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/ToastUtils.java"
            line="214"
            column="26"/>
    </issue>

    <issue
        id="Recycle"
        message="This `TypedArray` should be recycled after use with `#recycle()`"
        errorLine1="        TypedArray ta = attrs == null ? null : getContext().obtainStyledAttributes(attrs, R.styleable.SwitchButton);"
        errorLine2="                                                            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/widget/switchbutton/SwitchButton.java"
            line="158"
            column="61"/>
    </issue>

    <issue
        id="Recycle"
        message="This `TypedArray` should be recycled after use with `#recycle()`"
        errorLine1="        ta = attrs == null ? null : getContext().obtainStyledAttributes(attrs, new int[]{android.R.attr.focusable, android.R.attr.clickable});"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/widget/switchbutton/SwitchButton.java"
            line="186"
            column="50"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 21"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {//16"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/widget/RoundViewDelegate.java"
            line="291"
            column="17"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    private static Context sContext;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/Abase.java"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields; this is a memory leak"
        errorLine1="    private static View customView;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/ybmmarket20/common/util/ToastUtils.java"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="        &lt;LinearLayout"
        errorLine2="         ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/defaulthead_layout.xml"
            line="30"
            column="10"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@android:style/Theme.Holo`)"
        errorLine1="    android:background=&quot;@color/white&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/defaulthead_layout.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#F7F7F8` with a theme that also paints a background (inferred theme is `@android:style/Theme.Holo`)"
        errorLine1="    android:background=&quot;#F7F7F8&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/status_view_layout_empty.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#F7F7F8` with a theme that also paints a background (inferred theme is `@android:style/Theme.Holo`)"
        errorLine1="    android:background=&quot;#F7F7F8&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/status_view_layout_error.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#F7F7F8` with a theme that also paints a background (inferred theme is `@android:style/Theme.Holo`)"
        errorLine1="    android:background=&quot;#F7F7F8&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/status_view_layout_error_no_network.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#F7F7F8` with a theme that also paints a background (inferred theme is `@android:style/Theme.Holo`)"
        errorLine1="    android:background=&quot;#F7F7F8&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/status_view_layout_loading.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="TypographyEllipsis"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?"
        errorLine1="    &lt;string name=&quot;status_view_error_text&quot;>加载失败...&lt;/string>"
        errorLine2="                                          ^">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="43"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_back.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_back.png"/>
    </issue>

    <issue
        id="IconMissingDensityFolder"
        message="Missing density variation folders in `src/main/res`: drawable-hdpi, drawable-mdpi, drawable-xhdpi">
        <location
            file="src/main/res"/>
    </issue>

</issues>
