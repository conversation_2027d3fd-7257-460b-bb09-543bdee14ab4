<?xml version="1.0" encoding="utf-8"?>
<com.luck.picture.lib.widget.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_picture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/image_placeholder" />

    <LinearLayout
        android:id="@+id/ll_check"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_alignParentRight="true"
        android:gravity="right"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/picture.checked.style"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_isGif"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:background="@drawable/gif_tag"
        android:text="@string/gif_tag"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_long_chart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:background="@drawable/gif_tag"
        android:text="@string/picture_long_chart"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_duration"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_alignParentBottom="true"
        android:background="@drawable/buybuybuy"
        android:drawableLeft="@drawable/video_icon"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:paddingTop="8dp"
        android:text="00:00"
        android:textColor="@color/white"
        android:textSize="11sp"
        android:visibility="gone" />
</com.luck.picture.lib.widget.SquareRelativeLayout>