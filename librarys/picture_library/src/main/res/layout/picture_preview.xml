<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_title"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="?attr/picture.ac_preview.title.bg">

        <ImageView
            android:id="@+id/picture_left_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="15dp"
            android:src="?attr/picture.preview.leftBack.icon" />

        <TextView
            android:id="@+id/picture_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_centerVertical="true"
            android:ellipsize="end"
            android:maxEms="11"
            android:textColor="?attr/picture.ac_preview.title.textColor"
            android:textSize="18sp" />

        <LinearLayout
            android:id="@+id/ll_check"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <TextView
                android:id="@+id/check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/picture.checked.style"
                android:gravity="center"
                android:textColor="@color/white"
                android:textSize="12sp" />
        </LinearLayout>
    </RelativeLayout>


    <com.luck.picture.lib.widget.PreviewViewPager
        android:id="@+id/preview_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <RelativeLayout
        android:id="@+id/select_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="?attr/picture.ac_preview.bottom.bg"
        android:gravity="center_vertical">

        <LinearLayout
            android:id="@+id/id_ll_ok"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_img_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="5dp"
                android:background="?attr/picture.num.style"
                android:gravity="center"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/tv_ok"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:enabled="false"
                android:gravity="center|left"
                android:paddingRight="12dp"
                android:text="请选择"
                android:textColor="?attr/picture.ac_preview.complete.textColor"
                android:textSize="14sp" />

        </LinearLayout>
    </RelativeLayout>
</LinearLayout>