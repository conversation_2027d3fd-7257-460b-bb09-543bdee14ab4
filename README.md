﻿### 药帮忙分支管理    

1. 开发分支暂以分支创建日期命名，例如dev-2019-3-28，每条开发分支的建立从上一版发布的版本上创建，可以是master，也可以是tag  

2. 开发分支创建完成以后，从开发分支再创建子分支进行功能开发，子分支名称为feature+需求点名称，比如cms的需求feature-cms  

3. 每次提测按子功能单独提测，提测包名称要有提测内容的描述，例如V5.0.9_CMS。方便测试下载对应安装包  

4. 对于已经确定上线的功能可以合并到开发分支，  

5. 为了避免分支过多造成的影响，当项目上线后先拉TAG，然后及时删除开发分支。


 * dev-2019-3-28  开发分支
 * feature-cms  CMS功能
 * feature-h5-recommend  为你推荐替换H5
 * feature-member-gift  会员礼包
 * feature-oem OEM
 * feature-pop POP需求
 * feature-refund-msg 退款消息
 * master 主线


### 分支作用与注意事项  

1. **开发分支主要用于集成测试，主线用于线上发包**
2. 主线和TAG不允许做任何代码变更及提交操作，主线只做合并操作



