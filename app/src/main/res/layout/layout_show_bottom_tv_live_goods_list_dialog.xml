<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:background="@color/transparent"
    android:orientation="vertical"
    app:rv_cornerRadius_TL="10dp"
    app:rv_cornerRadius_TR="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">


            <TextView
                android:id="@+id/tv_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:text=""
                android:textColor="@color/text_9494A6"
                android:textSize="13sp"
                tools:text="共12件商品" />


            <ImageView
                android:id="@+id/product_detail_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:src="@drawable/icon_detail_service_close" />
        </RelativeLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:overScrollMode="never" />

    </LinearLayout>

</com.ybmmarket20.common.widget.RoundLinearLayout>