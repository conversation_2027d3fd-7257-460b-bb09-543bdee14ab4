<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fg"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:minHeight="44dp"
    android:orientation="horizontal"
    android:visibility="visible"
    app:rv_backgroundColor="@color/white">

    <TextView
        android:id="@+id/cart_new_rl_tv01"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="7dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:textColor="@color/color_292933"
        android:textSize="12sp"
        android:textStyle="bold"
        tools:text="已满500.00元，可返10元优惠券一张；再购500.00元，可返30元优惠券一张" />

    <TextView
        android:id="@+id/cart_new_tv_title_url"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:drawableRight="@drawable/icon_cart_drawable_right"
        android:paddingRight="3dp"
        android:text="去凑单"
        android:textColor="@color/color_00b377"
        android:textSize="13sp" />

</com.ybmmarket20.common.widget.RoundLinearLayout>