<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <TextView
        android:id="@+id/tv_discount_result_one"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:textColor="@color/color_30303c"
        android:textSize="@dimen/dimen_dp_14"
        android:textStyle="bold"
        app:layout_constraintRight_toLeftOf="@id/tv_discount_result_two"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="预估买入120件可享折后价约 " />

    <TextView
        android:id="@+id/tv_discount_result_two"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:layout_marginRight="@dimen/dimen_dp_15"
        android:textColor="@color/color_ff2121"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="¥24.5 " />


    <TextView
        android:id="@+id/tv_notice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginRight="@dimen/dimen_dp_15"
        android:layout_marginBottom="@dimen/dimen_dp_38"
        tools:text="购买时，记得先领券哦"
        android:textColor="#FB9B1F"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_discount_result_two" />
</androidx.constraintlayout.widget.ConstraintLayout>