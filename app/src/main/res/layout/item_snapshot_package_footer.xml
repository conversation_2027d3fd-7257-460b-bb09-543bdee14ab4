<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_marginTop="0.5dp"
    android:layout_marginBottom="5dp"
    android:background="@color/white"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/packagePriceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snapshot_package_price"
        android:textColor="#676773"
        android:textSize="13sp"
        android:layout_marginRight="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/packageTotalTv"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/packageTotalTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/snapshot_package_total"
        android:textColor="#ffff2121"
        android:layout_marginRight="8dp"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/packagePriceTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/packagePriceTv" />

</androidx.constraintlayout.widget.ConstraintLayout>