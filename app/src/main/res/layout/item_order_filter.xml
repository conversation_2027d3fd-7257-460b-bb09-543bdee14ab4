<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:paddingHorizontal="@dimen/pabr_dimen8dp">

    <TextView
        android:id="@+id/text_view_time_range"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginBottom="@dimen/dimen_dp_12"
        android:background="@drawable/selector_order_filter_bg"
        android:gravity="center"
        android:textColor="@color/selector_222222_00b955"
        android:textSize="14sp" />
</FrameLayout>