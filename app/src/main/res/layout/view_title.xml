<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp">

    <TextView
        android:id="@+id/menu_return"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="10dp"
        android:drawableLeft="@drawable/btn_back_gray"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/h8" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/h10" />

    <TextView
        android:id="@+id/menu_more"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/h10" />
</RelativeLayout>