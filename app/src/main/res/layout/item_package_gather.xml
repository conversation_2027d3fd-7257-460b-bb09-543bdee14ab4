<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_72"
        android:paddingLeft="@dimen/dimen_dp_15"
        android:paddingRight="@dimen/dimen_dp_15">

        <TextView
            android:id="@+id/tv_shop_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_15"
            tools:drawableLeft="@drawable/icon_shop_style"
            android:drawablePadding="5dp"
            android:textStyle="bold"
            android:textColor="@color/color_292933"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="武汉市久之润医疗器械责任有限公司" />

        <TextView
            android:id="@+id/tv_pacakge_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_dp_15"
            tools:text="满 2000.00 元起送，还差 1999.80 元"
            android:textColor="@color/color_676763"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />


        <TextView
            android:id="@+id/tv_pacakge_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:paddingLeft="@dimen/dimen_dp_5"
            android:paddingRight="@dimen/dimen_dp_5"
            android:textColor="@color/detail_tv_F96A25"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="gone"
            tools:visibility="visible"
            android:background="@drawable/bg_package_add"
            app:layout_constraintBottom_toBottomOf="@+id/tv_pacakge_content"
            app:layout_constraintLeft_toRightOf="@+id/tv_pacakge_content"
            app:layout_constraintTop_toTopOf="@+id/tv_pacakge_content"
            tools:text="需另付运费x元" />

        <TextView
            android:id="@+id/tv_go_shoping"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/icon_package_right_arrow"
            android:drawablePadding="3dp"
            android:padding="@dimen/dimen_dp_2"
            android:text="去凑单"
            android:textColor="@color/color_FF2121"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_shop_name"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_shop_name" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/divider_line_color_eeeeee"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</RelativeLayout>