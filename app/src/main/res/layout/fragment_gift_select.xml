<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="6dp"
            android:textColor="@color/color_292933"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="可选 1 件赠品，当前已选 0 件"/>

        <ImageView
            android:id="@+id/iv_select"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="7dp"
            android:src="@drawable/icon_gift_no_select"
            app:layout_constraintBottom_toBottomOf="@id/tv_select"
            app:layout_constraintEnd_toStartOf="@id/tv_give_up"
            app:layout_constraintTop_toTopOf="@id/tv_select"/>

        <TextView
            android:id="@+id/tv_give_up"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:text="放弃当前赠品"
            android:textColor="@color/color_292933"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_select"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_select"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_gift_select"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:overScrollMode="never"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_select" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
