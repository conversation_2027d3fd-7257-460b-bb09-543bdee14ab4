<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="@dimen/dimen_dp_10"
    android:paddingEnd="@dimen/dimen_dp_10"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:layout_marginTop="@dimen/dimen_dp_10"
    android:orientation="vertical"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="售后申请详情"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_15"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvCopyReceive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="平台客服"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/color_00b377"
            android:paddingTop="@dimen/dimen_dp_4"
            android:paddingBottom="@dimen/dimen_dp_4"
            android:paddingStart="@dimen/dimen_dp_9"
            android:paddingEnd="@dimen/dimen_dp_9"
            android:drawablePadding="@dimen/dimen_dp_5"
            android:drawableStart="@drawable/icon_platform_customer_service"
            app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/llOrderNo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_23"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="订单编号"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvOrderNo"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
        
        <ImageView
            android:layout_width="@dimen/dimen_dp_15"
            android:layout_height="@dimen/dimen_dp_15"
            android:src="@drawable/icon_arrow_right_gray"
            android:layout_gravity="center_vertical"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="申请时间"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvApplyTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="售后类型"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvAfterSaleType"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llErrorInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="有误信息"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvErrorInfo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llSpecialInvoiceInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="专票信息"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvSpecialInvoiceInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="补充说明"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <TextView
            android:id="@+id/tvTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="小药药XXXXXXX"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_13"
        android:orientation="horizontal">

        <TextView
            android:layout_width="@dimen/dimen_dp_70"
            android:layout_height="wrap_content"
            android:text="上传凭据"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_13" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvUploadImage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="4"
            tools:listitem="@layout/item_after_sales_image" />

        <TextView
            android:id="@+id/tvUploadImageEmpty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="--"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_13" />

    </LinearLayout>


</LinearLayout>