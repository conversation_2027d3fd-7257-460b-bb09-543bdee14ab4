<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:visibility="gone"
        android:id="@+id/rl_layout"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!--为了里面的TextView动画的起止位置在可控，所以这个地方加一层布局，删除慎重！-->
        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:layout_gravity="center">

            <TextView
                android:id="@+id/tv_number"
                android:layout_width="85dp"
                android:layout_height="match_parent"
                android:background="@drawable/bg_product_edit_def_02"
                android:gravity="center"
                android:hint="0"
                android:imeOptions="actionGo"
                android:inputType="phone"
                android:minWidth="85dp"
                android:paddingLeft="12dp"
                android:paddingRight="12dp"
                android:textColor="@color/coupon_limit_tv01"
                android:textSize="4sp" />
        </FrameLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_numSub"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="left"
            android:src="@drawable/page_minus" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_numAdd"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="right"
            android:src="@drawable/page_add" />
    </FrameLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvAddCart"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="36.5dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        app:rv_backgroundColor="@color/color_00b955"
        app:rv_cornerRadius="@dimen/dimen_dp_5"
        android:gravity="center"
        android:text="加入购物车"
        android:textColor="@color/white"
        android:textSize="@dimen/detail_btn" />

</androidx.constraintlayout.widget.ConstraintLayout>
