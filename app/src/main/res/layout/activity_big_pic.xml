<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_pic"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:background="#000000" />

        <ImageView
            android:id="@+id/iv_watermark"
            android:layout_width="266dp"
            android:layout_height="266dp"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            android:src="@drawable/icon_watermark" />

        <LinearLayout
            android:id="@+id/ll_pb"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:background="@android:color/transparent"
            android:gravity="center"
            android:orientation="vertical">

            <ProgressBar
                style="@style/Widget.AppCompat.ProgressBar"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginBottom="10dip"
                android:layout_marginTop="10dip"
                android:indeterminate="true" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>