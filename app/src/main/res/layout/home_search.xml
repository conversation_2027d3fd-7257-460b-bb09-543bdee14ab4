<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/header_search_height"
    android:orientation="horizontal"
    android:paddingTop="@dimen/header_height_padding_top">

    <LinearLayout
        android:id="@+id/ll_search"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_search_height"
        android:orientation="horizontal"
        android:paddingTop="@dimen/header_height_padding_top">

        <ImageView
            android:id="@+id/iv_code"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_gravity="center_vertical"
            android:scaleType="center"
            android:src="@drawable/icon_search_scan" />

        <RelativeLayout
            android:id="@+id/home_search_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginBottom="8dp"
            android:layout_marginTop="8dp"
            android:layout_weight="1"
            android:background="@drawable/search_round_corner_gray_bg"
            android:padding="4dp">

            <TextView
                android:id="@+id/title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:background="@null"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/search_hint"
                android:textColor="#adaba8"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_voice"
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="2dp"
                android:src="@drawable/nav_voice_01" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/title_left"
            android:layout_width="54dp"
            android:layout_height="54dp">

            <ImageView
                android:id="@+id/iv_msg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginLeft="15dp"
                android:src="@drawable/icon_search_message" />

            <TextView
                android:id="@+id/tv_smg_num"
                style="@style/more_msg_tip_style"
                android:layout_margin="10dp"
                tools:text="1"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_smg_num_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignRight="@id/tv_smg_num"
                android:layout_alignTop="@id/tv_smg_num"
                android:layout_marginRight="1.5dp"
                android:layout_marginTop="-4dp"
                android:text="+"
                android:textColor="@color/white"
                android:textSize="9sp"
                android:visibility="gone"
                tools:visibility="visible" />


        </RelativeLayout>

    </LinearLayout>

</merge>