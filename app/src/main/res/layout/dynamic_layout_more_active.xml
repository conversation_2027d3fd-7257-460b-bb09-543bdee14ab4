<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        style="@style/dynamic_layout_text"
        android:text="动态布局标题" />

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/cl_more_active"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <TextView
            android:id="@+id/tv_more_active_title"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:textColor="#292933"
            android:textSize="18dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="更多活动" />

        <TextView
            android:id="@+id/tv_more"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:drawableRight="@drawable/icon_more_cms"
            android:drawablePadding="5dp"
            android:gravity="center_vertical"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:text="更多"
            android:visibility="gone"
            android:textColor="#9494A6"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>