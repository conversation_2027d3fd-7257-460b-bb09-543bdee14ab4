<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:paddingLeft="10dp"
    android:paddingRight="10dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/packageNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="10dp"
        android:text="套餐搭配"
        android:textColor="#ff292933"
        android:textSize="@dimen/sp_14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/packageNumTv"
        android:layout_width="wrap_content"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:layout_height="wrap_content"
        android:text="@string/trading_snapshot_goods_num"
        android:textColor="#ff676773"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>