<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F7F7F7"
    android:orientation="vertical">

    <include layout="@layout/common_header_items"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:layout_marginBottom="-2dp" />

    <TextView
        android:id="@+id/tv_depreciate_hint"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="当该商品在45天内降价，您将收到推送消息"
        android:textColor="@color/text_9494A6"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/tv_present_price"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="当前价格：¥10.00"
        android:textColor="@color/text_9494A6"
        android:textSize="15sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:background="@color/white"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="您的渠道价格：¥"
            android:textColor="@color/color_292933"
            android:textSize="15sp" />

        <EditText
            android:id="@+id/tv_ditch_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:hint="低于此价格将会通知您"
            android:maxLength="8"
            android:maxLines="1"
            android:numeric="decimal"
            android:paddingLeft="1dp"
            android:paddingRight="11dp"
            android:singleLine="true"
            android:textColor="@color/color_292933"
            android:textColorHint="@color/text_9494A6"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="15sp" />

    </LinearLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/btn_confirm"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="35dp"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:rv_backgroundColor="@color/base_colors_new"
        app:rv_cornerRadius="2dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="确认后，同时将收藏该商品，您可以在我的收藏夹查看您订阅过的所有商品"
        android:textColor="@color/text_9494A6"
        android:textSize="12sp" />

</LinearLayout>