<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_87"
    android:background="@drawable/shape_red_envelope_bg"
    android:layout_marginTop="@dimen/dimen_dp_10">

    <ImageView
        android:id="@+id/iv_red_envelope"
        android:layout_width="@dimen/dimen_dp_33"
        android:layout_height="@dimen/dimen_dp_45"
        android:layout_marginStart="@dimen/dimen_dp_13"
        android:src="@drawable/icon_red_envelope_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_red_envelope_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_14"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#B54A02"
        android:textSize="@dimen/dimen_dp_13"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_red_envelope"
        app:layout_constraintTop_toTopOf="@+id/iv_red_envelope"
        android:layout_marginEnd="@dimen/dimen_dp_86"
        tools:text="618采购节浏览的红包活奖618采购节浏览的红包活奖励动励动618采购节浏览的红包活奖励动" />

    <TextView
        android:id="@+id/tv_red_envelope_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_14"
        android:textColor="#C34F02"
        android:textSize="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/iv_red_envelope"
        app:layout_constraintStart_toEndOf="@+id/iv_red_envelope"
        tools:text="2022/05/30-2022/06/30" />

    <TextView
        android:id="@+id/tv_red_envelope_gold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:text="￥12.00"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:textSize="@dimen/dimen_dp_19"
        android:textColor="#FF0803" />

</androidx.constraintlayout.widget.ConstraintLayout>