<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_aptitude_tip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_fff7ef"
    android:visibility="gone"
    tools:visibility="visible">


    <TextView
        android:id="@+id/tv_aptitude_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dimen_dp_10"
        android:textColor="@color/text_color_333333"
        android:background="#FFF7E9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="您存在资质/证件过期，为不影响发货，请您在15天内更新 点击去更新资质" />

</androidx.constraintlayout.widget.ConstraintLayout>