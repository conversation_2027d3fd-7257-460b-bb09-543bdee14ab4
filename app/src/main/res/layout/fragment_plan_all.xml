<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">

            <!--筛选-->
            <LinearLayout
                android:id="@+id/ll_filter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll|enterAlways">

                <RadioGroup
                    android:id="@+id/rg_product_tab"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:background="@drawable/base_header_default_bg"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <FrameLayout
                        android:id="@+id/fl_all_product"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1">

                        <RadioButton
                            android:id="@+id/rb_all_product"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:button="@null"
                            android:checked="true"
                            android:clickable="false"
                            android:drawableBottom="@drawable/divider_line_2dp"
                            android:gravity="center"
                            android:text="全部商品"
                            android:textColor="@color/text_292933"
                            android:textSize="17dp" />

                        <View style="@style/plan_tab_line" />
                    </FrameLayout>

                    <FrameLayout
                        android:id="@+id/fl_sale_product"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1">

                        <RadioButton
                            android:id="@+id/rb_sale_product"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:button="@null"
                            android:clickable="false"
                            android:gravity="center"
                            android:text="只看有货商品"
                            android:textColor="@color/text_676773"
                            android:textSize="15dp" />

                        <View
                            android:layout_width="51dp"
                            android:layout_height="4dp"
                            android:layout_gravity="center_horizontal|bottom" />

                    </FrameLayout>
                </RadioGroup>

                <RadioGroup
                    android:id="@+id/rb_filter"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingLeft="4dp"
                    android:paddingRight="4dp"
                    android:visibility="gone">

                    <RadioButton
                        android:id="@+id/rb_all"
                        style="@style/drug_list_tv_03"
                        android:checked="true"
                        android:text="全部商品" />

                    <RadioButton
                        android:id="@+id/rb_yh"
                        style="@style/drug_list_tv_03"
                        android:text="有货品种" />

                    <RadioButton
                        android:id="@+id/rb_qh"
                        style="@style/drug_list_tv_03"
                        android:text="缺货商品" />

                    <RadioButton
                        android:id="@+id/rb_wh"
                        style="@style/drug_list_tv_03"
                        android:text="无货商品" />

                </RadioGroup>

                <RelativeLayout
                    android:id="@+id/rl_total_and_filtrate"
                    android:layout_width="match_parent"
                    android:layout_height="42dp"
                    android:background="@color/white"
                    android:paddingLeft="8dp"
                    android:paddingRight="8dp">

                    <TextView
                        android:id="@+id/tv_total_count"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="数量:2456种"
                        android:textColor="@color/color_292933"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/tv_plan_filtrate"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentRight="true"
                        android:drawableRight="@drawable/icon_plan_screen_new"
                        android:gravity="center_vertical"
                        android:text="筛选"
                        android:textColor="@color/color_292933"
                        android:textSize="14dp" />

                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/colors_f5f5f5" />

            </LinearLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_product"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/ll_bottom_add"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:paddingBottom="10dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_manual_record"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="手动登记"
            android:textColor="@color/color_676773"
            android:textSize="16dp"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="2dp"
            app:rv_strokeColor="@color/colors_E4E4EB"
            app:rv_strokeWidth="1dp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_scan_code"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="扫描药品条码"
            android:textColor="@color/white"
            android:textSize="16dp"
            app:rv_backgroundColor="@color/base_colors_new"
            app:rv_cornerRadius="2dp" />
    </LinearLayout>
</LinearLayout>