<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_red_envelope_record_sub_tab_row"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_red_envelope_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_12"
        android:ellipsize="end"
        android:maxLines="2"
        android:textColor="#292933"
        android:textSize="@dimen/dimen_dp_15"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="618采购节浏览的红包活奖励动" />

    <TextView
        android:id="@+id/tv_instance_remark"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_red_envelope_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="5dp"
        android:textAlignment="viewStart"
        android:textColor="#676773"
        tools:text="备注：618采购节浏览的红包活奖励动"
        android:textSize="@dimen/dimen_dp_12" />



    <TextView
        android:id="@+id/tv_red_envelope_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/tv_red_envelope_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textSize="@dimen/dimen_dp_16"
        android:textStyle="bold"
        android:text="+6.18" />

    <TextView
        android:id="@+id/tv_red_envelope_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_instance_remark"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginTop="5dp"
        tools:text="2022/05/30-2022/06/30"
        android:textColor="#676773"
        android:layout_marginBottom="@dimen/dimen_dp_12"
        android:textSize="@dimen/dimen_dp_12" />

    <TextView
        android:id="@+id/tv_red_envelope_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/tv_red_envelope_time"
        app:layout_constraintEnd_toEndOf="parent"
        android:text="交易成功"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="#676773"
        android:layout_marginEnd="@dimen/dimen_dp_20" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginTop="@dimen/dimen_dp_12"
        android:background="@color/divider_line_color_eeeeee"
        app:layout_constraintTop_toBottomOf="@+id/tv_red_envelope_time" />

</androidx.constraintlayout.widget.ConstraintLayout>