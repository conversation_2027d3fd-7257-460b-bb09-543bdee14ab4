<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <include
        android:id="@+id/ll_title"
        layout="@layout/common_header_items"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_vertification_tip"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_40"
        android:background="#FFF7EF"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dimen_dp_20"
        android:text="为了您的账号安全，需要进行手机验证后才可登录"
        android:textColor="#99664D"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_title" />

    <TextView
        android:id="@+id/tv_login_account"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_20"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dimen_dp_20"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_vertification_tip"
        tools:text="账号：***********" />


    <EditText
        android:id="@+id/et_vetification_code"
        android:layout_width="225dp"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginLeft="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:background="@drawable/bg_edittext"
        android:gravity="center_vertical"
        android:hint="请输入验证码"
        android:inputType="number"
        android:paddingLeft="@dimen/dimen_dp_15"
        android:textColor="@color/color_292933"
        android:textColorHint="@color/colors_9595A6"
        android:textSize="@dimen/dimen_dp_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_login_account" />

    <TextView
        android:id="@+id/btn_send_vertifycode"
        android:layout_width="@dimen/dimen_dp_100"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginRight="@dimen/dimen_dp_20"
        android:background="@drawable/selector_common_btn"
        android:gravity="center"
        android:text="获取验证码"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/et_vetification_code" />


    <TextView
        android:id="@+id/btn_confirm"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginLeft="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_42"
        android:layout_marginRight="@dimen/dimen_dp_20"
        android:background="@drawable/selector_common_btn"
        android:gravity="center"
        android:text="提交"
        android:textColor="@color/white"
        android:enabled="false"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_vetification_code" />


</androidx.constraintlayout.widget.ConstraintLayout>