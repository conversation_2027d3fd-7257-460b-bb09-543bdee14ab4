<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

   <ScrollView
       android:layout_width="match_parent"
       android:layout_height="wrap_content">
       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:background="@color/white"
           android:padding="@dimen/dp_10">

           <androidx.appcompat.widget.AppCompatTextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:text="当前价格"
               android:textColor="@color/black"
               android:textSize="@dimen/sp_14"
               app:layout_constraintLeft_toLeftOf="parent"
               app:layout_constraintTop_toTopOf="parent" />


           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/currentPriceTv"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:textColor="@color/correction_require"
               android:textSize="15sp"
               app:layout_constraintRight_toRightOf="parent"
               app:layout_constraintTop_toTopOf="parent" />

           <View
               android:id="@+id/divider1"
               android:layout_width="match_parent"
               android:layout_height="1px"
               android:layout_marginTop="14dp"
               android:background="#eeeeee"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/currentPriceTv" />

           <androidx.constraintlayout.widget.ConstraintLayout
               android:id="@+id/correctionReasonCl"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_marginTop="14dp"
               app:layout_constraintLeft_toLeftOf="parent"
               app:layout_constraintTop_toTopOf="@id/divider1">

               <androidx.appcompat.widget.AppCompatTextView
                   android:id="@+id/correctionReasonTipsTv"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   tools:text="@string/correction_reason"
                   android:textColor="@color/black"
                   android:textSize="@dimen/sp_14"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintLeft_toLeftOf="parent"
                   app:layout_constraintTop_toTopOf="parent" />

               <androidx.appcompat.widget.AppCompatTextView
                   android:id="@+id/correctionReasonTv"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:drawableRight="@drawable/ic_arrow"
                   android:drawablePadding="8dp"
                   android:text="售价偏高"
                   android:textColor="@color/black"
                   android:textSize="@dimen/sp_14"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintRight_toRightOf="parent"
                   app:layout_constraintTop_toTopOf="parent" />


           </androidx.constraintlayout.widget.ConstraintLayout>


           <View
               android:id="@+id/divider2"
               android:layout_width="match_parent"
               android:layout_height="1px"
               android:layout_marginTop="14dp"
               android:background="#eeeeee"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/correctionReasonCl" />

           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/suggestedPriceTipsTv"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginTop="14dp"
               tools:text="@string/correction_suggest_price"
               android:textColor="@color/black"
               android:textSize="@dimen/sp_14"
               app:layout_constraintLeft_toLeftOf="parent"
               app:layout_constraintTop_toTopOf="@id/divider2" />

           <androidx.appcompat.widget.AppCompatEditText
               android:id="@+id/discountsEt"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginTop="14dp"
               android:background="@null"
               android:gravity="right|center_vertical"
               android:hint="请输入更优惠价格"
               android:inputType="numberDecimal"
               android:maxLength="30"
               android:maxLines="1"
               android:textColor="#292933"
               android:textColorHint="#ff9494a6"
               android:textSize="@dimen/sp_14"
               app:layout_constraintRight_toRightOf="parent"
               app:layout_constraintTop_toTopOf="@id/divider2" />

           <View
               android:id="@+id/divider3"
               android:layout_width="match_parent"
               android:layout_height="1px"
               android:layout_marginTop="14dp"
               android:background="#eeeeee"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/discountsEt" />

           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/platformNameTipsTv"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginTop="14dp"
               android:singleLine="true"
               tools:text="@string/correction_refer_platform"
               android:textColor="@color/black"
               android:textSize="@dimen/sp_14"
               app:layout_constraintLeft_toLeftOf="parent"
               app:layout_constraintTop_toTopOf="@id/divider3" />


           <androidx.appcompat.widget.AppCompatEditText
               android:id="@+id/platformNameEt"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:layout_marginTop="14dp"
               android:background="@null"
               android:gravity="right|center_vertical"
               android:hint="请输入平台名称"
               android:maxLength="15"
               android:singleLine="true"
               android:textColor="#292933"
               android:textColorHint="#ff9494a6"
               android:textSize="@dimen/sp_14"
               app:layout_constraintRight_toRightOf="parent"
               app:layout_constraintTop_toTopOf="@id/divider3" />

           <View
               android:id="@+id/divider4"
               android:layout_width="match_parent"
               android:layout_height="1px"
               android:layout_marginTop="14dp"
               android:background="#eeeeee"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/platformNameEt" />

           <fragment
               android:id="@+id/commonCorrection"
               android:name="com.ybmmarket20.business.correction.ui.fragment.CommonCorrectionFragment"
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               app:layout_constraintTop_toBottomOf="@id/divider4" />
       </androidx.constraintlayout.widget.ConstraintLayout>
   </ScrollView>

</LinearLayout>