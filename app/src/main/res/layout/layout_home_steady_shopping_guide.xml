<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_shopping_guide"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_370"
    android:background="@drawable/shape_home_steady_shopping_guide"
    app:layout_constraintTop_toBottomOf="@+id/iv_capsule1"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_370"
        android:background="@drawable/shape_home_steady_shopping_guide"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_divider_horizontal1"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginTop="@dimen/dimen_dp_120"
        android:background="@color/color_EFEFEF"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_divider_horizontal2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:layout_marginTop="@dimen/dimen_dp_240"
        android:background="@color/color_EFEFEF"
        android:visibility="invisible"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_vertical_middle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <View
        android:id="@+id/v_divider_vertical_middle"
        android:layout_width="@dimen/dimen_dp_1"
        android:layout_height="@dimen/dimen_dp_360"
        android:background="@color/color_EFEFEF"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="@+id/gl_vertical_middle"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 整点秒杀   -->
    <ImageView
        android:id="@+id/iv_fast_entry_bg"
        style="@style/fast_entry_bg"
        android:layout_marginEnd="@dimen/dimen_dp_2_5"
        android:src="@drawable/icon_home_steady_fast_entry_kill"
        app:layout_constraintBottom_toBottomOf="@+id/v_divider_horizontal1"
        app:layout_constraintEnd_toEndOf="@+id/gl_vertical_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_major_title_kill"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_kill"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_kill1"
        style="@style/shopping_guide_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_kill"
        tools:src="@color/gray_eef2f5" />

    <ImageView
        android:id="@+id/iv_daily_kill2"
        style="@style/shopping_guide_img"
        android:layout_marginStart="@dimen/dimen_dp_22"
        app:layout_constraintStart_toEndOf="@+id/iv_kill1"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_kill"
        tools:src="@color/gray_eef2f5" />

    <!-- 排行榜   -->
    <ImageView
        android:id="@+id/iv_fast_entry_sort_bg"
        style="@style/fast_entry_bg"
        android:layout_marginStart="@dimen/dimen_dp_2_5"
        android:src="@drawable/icon_home_steady_fast_entry_sort"
        app:layout_constraintBottom_toBottomOf="@+id/v_divider_horizontal1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/gl_vertical_middle"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_major_title_sort"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_sort"
        app:layout_constraintStart_toStartOf="@id/iv_fast_entry_sort_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_minor_title_sort"
        style="@style/shopping_guide_minor_title"
        android:text="@string/shopping_guide_minor_title_sort"
        android:textColor="@color/color_158a50"
        app:layout_constraintStart_toStartOf="@id/iv_fast_entry_sort_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_sort" />

    <ImageView
        android:id="@+id/iv_tag_sort1"
        style="@style/shopping_guide_tag"
        app:layout_constraintStart_toStartOf="@+id/iv_fast_entry_sort_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_sort" />

    <ImageView
        android:id="@+id/iv_shopping_guide_tag1"
        android:layout_width="@dimen/dimen_dp_13"
        android:layout_height="@dimen/dimen_dp_14"
        android:src="@drawable/icon_home_steady_sort1"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort1"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort1" />

    <View
        android:id="@+id/v_shopping_guide_tag_border1"
        style="@style/shopping_guide_tag"
        android:layout_marginStart="@dimen/dimen_dp_0"
        android:layout_marginTop="@dimen/dimen_dp_0"
        android:background="@drawable/shape_home_steady_tag_bg1"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort1"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort1" />


    <ImageView
        android:id="@+id/iv_tag_sort2"
        style="@style/shopping_guide_tag"
        app:layout_constraintStart_toEndOf="@+id/iv_tag_sort1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_sort" />

    <ImageView
        android:id="@+id/iv_shopping_guide_tag2"
        android:layout_width="@dimen/dimen_dp_13"
        android:layout_height="@dimen/dimen_dp_14"
        android:src="@drawable/icon_home_steady_sort2"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort2"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort2" />

    <View
        android:id="@+id/v_shopping_guide_tag_border2"
        style="@style/shopping_guide_tag"
        android:layout_marginStart="@dimen/dimen_dp_0"
        android:layout_marginTop="@dimen/dimen_dp_0"
        android:background="@drawable/shape_home_steady_tag_bg2"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort2"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort2" />

    <ImageView
        android:id="@+id/iv_tag_sort3"
        style="@style/shopping_guide_tag"
        app:layout_constraintStart_toEndOf="@+id/iv_tag_sort2"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_sort" />

    <ImageView
        android:id="@+id/iv_shopping_guide_tag3"
        android:layout_width="@dimen/dimen_dp_13"
        android:layout_height="@dimen/dimen_dp_14"
        android:src="@drawable/icon_home_steady_sort3"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort3"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort3" />

    <View
        android:id="@+id/v_shopping_guide_tag_border3"
        style="@style/shopping_guide_tag"
        android:layout_marginStart="@dimen/dimen_dp_0"
        android:layout_marginTop="@dimen/dimen_dp_0"
        android:background="@drawable/shape_home_steady_tag_bg3"
        app:layout_constraintStart_toStartOf="@+id/iv_tag_sort3"
        app:layout_constraintTop_toTopOf="@+id/iv_tag_sort3" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_sort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_shopping_guide_tag1,iv_shopping_guide_tag2,iv_shopping_guide_tag3,v_shopping_guide_tag_border1,v_shopping_guide_tag_border2,v_shopping_guide_tag_border3"
        tools:visibility="visible" />

    <!-- 每日特惠   -->
    <ImageView
        android:id="@+id/iv_fast_entry_daily_discount_bg"
        style="@style/fast_entry_bg"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_2_5"
        android:src="@drawable/icon_home_steady_fast_entry_daily_discount"
        app:layout_constraintBottom_toBottomOf="@+id/v_divider_horizontal2"
        app:layout_constraintEnd_toEndOf="@+id/gl_vertical_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/v_divider_horizontal1" />

    <TextView
        android:id="@+id/tv_major_daily_discount"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_daily_discount"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_fast_entry_daily_discount_bg" />

    <TextView
        android:id="@+id/tv_minor_daily_discount"
        style="@style/shopping_guide_minor_title"
        android:text="@string/shopping_guide_minor_title_daily_discount"
        android:textColor="@color/color_ea303d"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_daily_discount" />

    <ImageView
        android:id="@+id/iv_daily_discount1"
        style="@style/shopping_guide_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_daily_discount"
        tools:src="@color/gray_eef2f5" />

    <ImageView
        android:id="@+id/iv_daily_discount2"
        style="@style/shopping_guide_img"
        android:layout_marginStart="@dimen/dimen_dp_22"
        app:layout_constraintStart_toEndOf="@+id/iv_daily_discount1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_daily_discount"
        tools:src="@color/gray_eef2f5" />

    <!-- 高毛专区   -->

    <ImageView
        android:id="@+id/iv_fast_entry_gross_bg"
        style="@style/fast_entry_bg"
        android:layout_marginStart="@dimen/dimen_dp_2_5"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:src="@drawable/icon_home_steady_fast_entry_gross"
        app:layout_constraintBottom_toBottomOf="@+id/v_divider_horizontal2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/gl_vertical_middle"
        app:layout_constraintTop_toTopOf="@+id/v_divider_horizontal1" />

    <TextView
        android:id="@+id/tv_major_title_gross"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_gross"
        app:layout_constraintStart_toStartOf="@id/v_divider_vertical_middle"
        app:layout_constraintTop_toTopOf="@id/iv_fast_entry_gross_bg" />

    <TextView
        android:id="@+id/tv_minor_title_gross"
        style="@style/shopping_guide_minor_title"
        android:text="@string/shopping_guide_minor_title_gross"
        android:textColor="@color/color_cd6e08"
        app:layout_constraintStart_toStartOf="@id/v_divider_vertical_middle"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_gross" />

    <ImageView
        android:id="@+id/iv_gross1"
        style="@style/shopping_guide_img"
        app:layout_constraintStart_toStartOf="@id/gl_vertical_middle"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_gross"
        tools:src="@color/gray_eef2f5" />

    <ImageView
        android:id="@+id/iv_gross2"
        style="@style/shopping_guide_img"
        android:layout_marginStart="@dimen/dimen_dp_22"
        app:layout_constraintStart_toEndOf="@+id/iv_gross1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_gross"
        tools:src="@color/gray_eef2f5" />

    <!-- 大牌闪购   -->

    <ImageView
        android:id="@+id/iv_fast_entry_brand_purchase_bg"
        style="@style/fast_entry_bg"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_2_5"
        android:src="@drawable/icon_home_steady_fast_entry_brand_purchase"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/gl_vertical_middle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/v_divider_horizontal2" />

    <TextView
        android:id="@+id/tv_major_title_brand_purchase"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_brand_purchase"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_fast_entry_brand_purchase_bg" />

    <TextView
        android:id="@+id/tv_minor_title_brand_purchase"
        style="@style/shopping_guide_minor_title"
        android:text="@string/shopping_guide_minor_title_brand_purchase"
        android:textColor="@color/color_8628D9"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_brand_purchase" />

    <ImageView
        android:id="@+id/iv_brand_purchase1"
        style="@style/shopping_guide_img"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_brand_purchase"
        tools:src="@color/gray_eef2f5" />

    <ImageView
        android:id="@+id/iv_brand_purchase2"
        style="@style/shopping_guide_img"
        android:layout_marginStart="@dimen/dimen_dp_22"
        app:layout_constraintStart_toEndOf="@+id/iv_brand_purchase1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_brand_purchase"
        tools:src="@color/gray_eef2f5" />

    <!-- 新品首推   -->

    <ImageView
        android:id="@+id/iv_fast_entry_new_recommend_bg"
        style="@style/fast_entry_bg"
        android:layout_marginStart="@dimen/dimen_dp_2_5"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:src="@drawable/icon_home_steady_fast_entry_new_recommend"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/gl_vertical_middle"
        app:layout_constraintTop_toTopOf="@+id/v_divider_horizontal2" />

    <TextView
        android:id="@+id/tv_major_title_new_recommend"
        style="@style/shopping_guide_major_title"
        android:text="@string/shopping_guide_major_title_new_recommend"
        app:layout_constraintStart_toStartOf="@id/v_divider_vertical_middle"
        app:layout_constraintTop_toTopOf="@id/iv_fast_entry_new_recommend_bg" />

    <TextView
        android:id="@+id/tv_minor_title_new_recommend"
        style="@style/shopping_guide_minor_title"
        android:text="@string/shopping_guide_minor_title_sort"
        android:textColor="@color/color_158a50"
        app:layout_constraintStart_toStartOf="@id/v_divider_vertical_middle"
        app:layout_constraintTop_toBottomOf="@+id/tv_major_title_new_recommend" />

    <ImageView
        android:id="@+id/iv_new_recommend1"
        style="@style/shopping_guide_img"
        app:layout_constraintStart_toStartOf="@id/gl_vertical_middle"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_new_recommend"
        tools:src="@color/gray_eef2f5" />

    <ImageView
        android:id="@+id/new_recommend2"
        style="@style/shopping_guide_img"
        android:layout_marginStart="@dimen/dimen_dp_22"
        app:layout_constraintStart_toEndOf="@+id/iv_new_recommend1"
        app:layout_constraintTop_toBottomOf="@+id/tv_minor_title_new_recommend"
        tools:src="@color/gray_eef2f5" />

</androidx.constraintlayout.widget.ConstraintLayout>