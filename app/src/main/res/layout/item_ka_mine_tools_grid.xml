<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_grid"
        style="@style/more_text_layout_style" >

        <ImageView
            android:id="@+id/iv_icon"
            style="@style/more_image_layout_style"
            android:src="@drawable/icon_shop_college"
            app:layout_constraintBottom_toTopOf="@+id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/tv_dot"
            style="@style/more_msg_tip_style"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintCircle="@id/iv_icon"
            app:layout_constraintCircleAngle="45"
            app:layout_constraintCircleRadius="@dimen/dimen_dp_17"
            tools:text="12" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/more_text_gray_size_12"
            tools:text="药学院"
            android:textColor="@color/color_292933"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_icon" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
