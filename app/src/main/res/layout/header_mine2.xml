<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ybmmarket20.home.mine.Mine2HeaderView
            android:id="@+id/mine2_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/mine2_header_gl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:layout_marginBottom="@dimen/dimen_dp_35"
            app:layout_constraintBottom_toBottomOf="@+id/mine2_header"
            app:layout_constraintStart_toStartOf="parent" />

        <com.ybmmarket20.home.mine.Mine2AmountView
            android:id="@+id/mine2_amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/mine2_header_gl" />

        <ImageView
            android:id="@+id/ivPingAnTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@+id/mine2_amount"
            app:layout_constraintBottom_toBottomOf="@+id/mine2_amount"
            android:src="@drawable/icon_ping_an_tag"
            android:visibility="gone"
            android:layout_marginBottom="@dimen/dimen_dp_43"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.ybmmarket20.view.RebateVoucherView
        android:id="@+id/rebateView"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <!-- 订单和待支付轮播 -->
    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/ll2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:orientation="vertical"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp">

        <com.ybmmarket20.home.mine.Mine2OrderViewV2
            android:id="@+id/mine2_order"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_10" />

        <LinearLayout
            android:id="@+id/ll_marqueue_pay_notice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_dp_15"
            android:layout_marginLeft="@dimen/dimen_dp_12"
            android:layout_marginRight="@dimen/dimen_dp_12"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:orientation="vertical">

            <com.ybmmarket20.view.cms.MarqueeViewCms
                android:id="@+id/marquee_pay_notice"
                android:layout_width="match_parent"
                android:layout_height="50dp" />

        </LinearLayout>

    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <!-- banner和横幅 -->
    <LinearLayout
        android:id="@+id/ll_m"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants"
        android:orientation="vertical"
        tools:visibility="visible">

        <com.ybmmarket20.view.homesteady.HomeSteadyBannerView
            android:id="@+id/hsbv_minebanner"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_120"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:scaleType="centerCrop"
            android:visibility="gone"
            app:AutoPlayTime="5000"
            app:bannerBottomMargin="@dimen/dimen_dp_6"
            tools:visibility="visible" />

        <com.ybmmarket20.view.homesteady.HomeSteadyStreamerView
            android:id="@+id/hssv_minestreamer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_117"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <!-- 常用工具 -->
    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/ll3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:orientation="vertical"
        android:paddingBottom="5dp"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="2dp">

        <com.ybmmarket20.home.mine.Mine2CommonToolsView
            android:id="@+id/mine2_common_tools"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </com.ybmmarket20.common.widget.RoundLinearLayout>

    <ImageView
        android:layout_width="127dp"
        android:layout_height="@dimen/dimen_dp_16"
        android:src="@drawable/icon_mine2_recommend"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_10" />
</LinearLayout>