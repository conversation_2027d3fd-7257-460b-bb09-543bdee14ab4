<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/cl_root"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_top_tab"
        android:layout_width="0dp"
        android:layout_marginTop="180dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/guide_page_pic_center_tab"
        app:layout_constraintDimensionRatio="349:95.5"
        android:layout_height="0dp"/>

    <ImageView
        android:id="@+id/iv_guide_page_bg_third"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="329.84:104.5"
        android:layout_marginStart="38dp"
        android:layout_marginTop="19dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/guide_page_bg_third"
        app:layout_constraintEnd_toEndOf="parent"
        android:scaleType="centerCrop"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_top_tab" />

    <TextView
        android:id="@+id/tv_guide_page_third_next"
        android:layout_width="wrap_content"
        android:text="下一步 3/4"
        android:gravity="center"
        android:textSize="18dp"
        android:textColor="@color/white"
        android:paddingHorizontal="23dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_guide_page_bg_third"
        app:layout_constraintStart_toStartOf="@id/iv_guide_page_bg_third"
        app:layout_constraintEnd_toEndOf="@id/iv_guide_page_bg_third"
        android:layout_marginBottom="13dp"
        android:background="@drawable/shape_20_00be57"
        android:layout_height="39dp"/>

    <TextView
        android:id="@+id/tv_skip"
        android:layout_width="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/iv_guide_page_bg_third"
        android:padding="16dp"
        android:textColor="@color/color_00BE57"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@id/iv_guide_page_bg_third"
        android:layout_marginEnd="32dp"
        android:text="跳过"
        android:layout_height="wrap_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>