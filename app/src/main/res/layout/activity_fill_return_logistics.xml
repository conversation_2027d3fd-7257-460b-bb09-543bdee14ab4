<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colors_fff7ef"
            android:gravity="center_vertical"
            android:lineSpacingExtra="2dp"
            android:minHeight="50dp"
            android:paddingBottom="6dp"
            android:paddingLeft="10dp"
            android:paddingRight="6dp"
            android:paddingTop="8dp"
            android:text="温馨提示：退货物流信息便于我们更快处理您的退货退款单，请您准确填写。药帮忙竭诚为您服务！"
            android:textColor="@color/colors_99664D"
            android:textSize="12sp" />

        <com.ybmmarket20.view.MyScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_amount"
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="0dp"
                        android:layout_marginLeft="8dp"
                        android:layout_weight="1"
                        android:text="退款金额:" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="8dp"
                        android:text="（不含活动优惠金额）"
                        android:textColor="@color/text_9494A6"
                        android:textSize="14sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_balance"
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="0dp"
                        android:layout_marginLeft="8dp"
                        android:layout_weight="1"
                        android:text="退回余额:" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="8dp"
                        android:text="（退款成功可退余额）"
                        android:textColor="@color/text_9494A6"
                        android:textSize="14sp" />
                </LinearLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:minHeight="38dp"
                    android:paddingLeft="8dp"
                    android:text="请填写您的退货物流信息"
                    android:textColor="@color/text_292933"
                    android:textSize="12sp" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="90dp"
                        android:text="快递名称：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_express_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:hint="请填写快递名称，例如：顺丰快递"
                        android:imeOptions="actionGo"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="40dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:text="*"
                        android:textColor="#FA5741" />

                    <TextView
                        style="@style/apply_refund_activiy_title"
                        android:layout_width="90dp"
                        android:text="快递单号：" />

                    <com.ybmmarket20.common.widget.RoundEditText
                        android:id="@+id/et_express_no"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        android:hint="扫描快递单号或手动填写"
                        android:imeOptions="actionGo"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/text_292933"
                        android:textCursorDrawable="@drawable/color_cursor"
                        android:textSize="15sp" />

                    <ImageView
                        android:id="@+id/iv_code"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center_vertical"
                        android:scaleType="center"
                        android:src="@drawable/icon_nav_scan" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:background="#eeeeee" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingBottom="10dp"
                    android:paddingLeft="8dp"
                    android:paddingTop="10dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="运单截图："
                        android:textColor="@color/text_292933"
                        android:textSize="15sp" />

                    <com.ybmmarket20.common.widget.RoundRelativeLayout
                        android:id="@+id/fragment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="7dp"
                        android:layout_marginRight="7dp"
                        app:rv_backgroundColor="@color/white"
                        app:rv_cornerRadius="4dp" />

                </LinearLayout>

            </LinearLayout>

        </com.ybmmarket20.view.MyScrollView>

    </LinearLayout>

    <com.ybmmarket20.view.ButtonObserver
        android:id="@+id/btn_ok"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_gravity="bottom|center_vertical"
        android:layout_marginBottom="35dp"
        android:layout_marginLeft="35dp"
        android:layout_marginRight="35dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/bg_image_apply_for_convoy_btn_2"
        android:gravity="center"
        android:text="确定"
        android:textColor="@color/white"
        android:textSize="17sp" />

</LinearLayout>