<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_45"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginTop="@dimen/dimen_dp_2"
    android:background="@drawable/bg_corner_all">


    <TextView
        android:id="@+id/tv_shop_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dimen_dp_10"
        android:text="5种 共20件"
        android:textColor="@color/text_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_payAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/dimen_dp_10"
        tools:text="合计: 2121.00"
        android:textColor="@color/color_FF2121"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_payAmount_loading"
        android:layout_width="@dimen/dimen_dp_100"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dimen_dp_13"
            android:textColor="@color/color_ff2121"
            android:text="合计：" />

        <TextView
            android:id="@+id/tv_payAmount_loading"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="计算中..."
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            android:visibility="visible"
            app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>