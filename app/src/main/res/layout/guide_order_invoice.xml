<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#80000000"
    android:clickable="true"
    android:fitsSystemWindows="true"
    android:focusable="true">
    <ImageView
        android:id="@+id/ivOrderVoice1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_marginTop="37dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="43dp"
        android:src="@drawable/guide_order_invoice_1"/>
    <ImageView
        android:id="@+id/ivOrderVoice2"
        app:layout_constraintTop_toBottomOf="@id/ivOrderVoice1"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="43dp"
        android:layout_marginTop="@dimen/dimen_dp_24"
        android:src="@drawable/guide_order_invoice_2"/>
</androidx.constraintlayout.widget.ConstraintLayout>