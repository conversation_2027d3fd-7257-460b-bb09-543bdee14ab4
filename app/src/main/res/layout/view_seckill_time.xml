<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="14dp"
    android:paddingTop="0.5dp">

    <TextView
        android:id="@+id/tv_time_prefix"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="距结束"
        android:textColor="#59483F"
        android:textSize="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_lightning"
        android:layout_width="4dp"
        android:layout_height="@dimen/dimen_dp_6"
        android:layout_marginLeft="@dimen/dimen_dp_3"
        android:visibility="gone"
        android:src="@drawable/icon_seckill_time_lightning"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:text="14:30:59"
        android:textColor="#FF2121"
        android:textSize="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/tv_time_prefix"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>