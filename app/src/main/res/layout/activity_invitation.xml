<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ybmmarket20.view.MyScrollView
        android:id="@+id/sv"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_share_posters_bg"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="37dp"
                    android:layout_marginTop="132dp"
                    android:src="@drawable/icon_share_posters_punctuation_01" />

                <TextView
                    android:id="@+id/tv_describe"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="42dp"
                    android:layout_marginRight="42dp"
                    android:text="Hi~我是药帮忙, 我已经来到药帮忙 100天, 总计节省了 10000 元"
                    android:textColor="#957D64"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:layout_marginLeft="37dp"
                    android:layout_marginRight="37dp"
                    android:layout_marginTop="2dp"
                    android:src="@drawable/icon_share_posters_punctuation_02" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="42dp"
                    android:layout_marginRight="42dp"
                    android:layout_marginTop="23dp"
                    android:background="@drawable/icon_share_posters_coupons"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="2"
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:paddingLeft="10dp">

                        <TextView
                            android:id="@+id/tv_coupons_describe"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="送你100000000元优惠券"
                            android:textColor="#BC7D4D"
                            android:textSize="19sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="邀请你和我一起便宜买好药！"
                            android:textColor="#BC7D4D"
                            android:textSize="13sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥"
                            android:textColor="#FF9E28"
                            android:textSize="19sp" />

                        <TextView
                            android:id="@+id/tv_coupons_num"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1000"
                            android:textColor="#FF9E28"
                            android:textSize="30sp" />

                    </LinearLayout>

                </LinearLayout>

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_invite_code"
                    android:layout_width="match_parent"
                    android:layout_height="49dp"
                    android:layout_marginLeft="42dp"
                    android:layout_marginRight="42dp"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:text="邀请码："
                    android:textColor="#99774D"
                    android:textSize="17sp"
                    app:rv_strokeColor="#CC9A70"
                    app:rv_strokeWidth="1dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:gravity="center"
                    android:text="请在注册时填写"
                    android:textColor="#99774D"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="117dp"
                android:background="@color/white"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingLeft="15sp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="长按识别二维码, 下载药帮忙APP"
                        android:textColor="@color/text_292933"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:text="便宜买好药，当然药帮忙"
                        android:textColor="@color/text_9494A6"
                        android:textSize="14sp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="90dp"
                    android:layout_height="90dp"
                    android:layout_margin="14dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/icon_share_posters_bg_code">

                </ImageView>

            </LinearLayout>

        </LinearLayout>

    </com.ybmmarket20.view.MyScrollView>

</LinearLayout>