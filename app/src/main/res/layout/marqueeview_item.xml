<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="10dp"
    android:paddingTop="5dp"
    android:paddingEnd="10dp"
    android:paddingBottom="5dp">

    <TextView
        android:id="@+id/marquee_tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="12sp"
        tools:text="王的k****DF" />

    <TextView
        android:id="@+id/marquee_tv_have_to_buy"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_weight="1"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="12sp"
        tools:text="已购20盒" />

    <TextView
        android:id="@+id/marquee_tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/color_292933"
        android:textSize="12sp"
        tools:text="2分钟前" />

</LinearLayout>