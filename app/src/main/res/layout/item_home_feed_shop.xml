<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" >

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl_start"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="@dimen/dimen_dp_10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_home_shop_feed_top"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/gl_start">

        <TextView
            android:id="@+id/tv_title_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_00b377"
            app:layout_constraintTop_toTopOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_title"
            android:layout_marginEnd="@dimen/dimen_dp_5"
            android:paddingStart="@dimen/dimen_dp_3"
            android:paddingEnd="@dimen/dimen_dp_3"
            android:paddingTop="@dimen/dimen_dp_1"
            android:paddingBottom="@dimen/dimen_dp_1"
            android:background="@drawable/shape_home_shop_feed_title_tag_green"
            android:text="自营"
            android:gravity="center"
            android:textSize="@dimen/dimen_dp_10"/>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            tools:text="湖北小药药自营旗舰店最长字段展示湖北小药药自营旗舰店最长字段展示"
            android:textSize="@dimen/dimen_dp_16"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_entry_shop"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintStart_toEndOf="@+id/tv_title_tag"
            android:layout_marginEnd="@dimen/dimen_dp_19" />

        <TextView
            android:id="@+id/tv_entry_shop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="进店"
            android:textSize="@dimen/dimen_dp_11"
            android:textColor="@color/text_292933"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="@+id/tv_title"
            android:drawableEnd="@drawable/icon_home_shop_entry_arrow"
            android:layout_marginEnd="@dimen/dimen_dp_10"/>

        <LinearLayout
            android:id="@+id/ll_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title_tag"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            android:orientation="vertical"
            android:layout_marginEnd="@dimen/dimen_dp_91"
            android:layout_marginTop="@dimen/dimen_dp_10"/>


        <TextView
            android:id="@+id/tv_home_shop_coupon"
            android:layout_width="@dimen/dimen_dp_14"
            android:layout_height="@dimen/dimen_dp_14"
            android:text="券"
            android:textSize="@dimen/dimen_dp_10"
            android:background="@drawable/shape_home_shop_coupon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title_tag"
            android:layout_marginTop="@dimen/dimen_dp_17"
            android:gravity="center"
            android:textColor="#FFF82324"/>

        <TextView
            android:id="@+id/tv_home_shop_coupon_des"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            tools:text="每满42.5减6.5；每满35件减35；满1000减50每满42.5减6.5；每满35件减35；满1000减50"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="@color/color_676773"
            android:singleLine="true"
            app:layout_constraintStart_toEndOf="@+id/tv_home_shop_coupon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_home_shop_coupon"
            app:layout_constraintBottom_toBottomOf="@+id/tv_home_shop_coupon"
            android:layout_marginStart="@dimen/dimen_dp_4"
            android:layout_marginEnd="@dimen/dimen_dp_91"/>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_coupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_home_shop_coupon, tv_home_shop_coupon_des" />

        <TextView
            android:id="@+id/tv_home_shop_promotion"
            android:layout_width="@dimen/dimen_dp_14"
            android:layout_height="@dimen/dimen_dp_14"
            android:text="促"
            android:textSize="@dimen/dimen_dp_10"
            android:background="@drawable/shape_home_shop_coupon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_home_shop_coupon"
            android:layout_marginTop="@dimen/dimen_dp_8"
            android:gravity="center"
            android:textColor="#FFF82324"/>

        <TextView
            android:id="@+id/tv_home_shop_promotion_des"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            tools:text="每满42.5减6.5；每满35件减35；满1000减50每满42.5减6.5；每满35件减35；满1000减50"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="@color/color_676773"
            android:singleLine="true"
            app:layout_constraintStart_toEndOf="@+id/tv_home_shop_promotion"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_home_shop_promotion"
            app:layout_constraintBottom_toBottomOf="@+id/tv_home_shop_promotion"
            android:layout_marginStart="@dimen/dimen_dp_4"
            android:layout_marginEnd="@dimen/dimen_dp_91"/>

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_promotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_home_shop_promotion, tv_home_shop_promotion_des" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_home_shop_feed"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@+id/gl_start"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_home_shop_feed_top"
        android:layout_marginTop="@dimen/dimen_dp_11"
        tools:itemCount="6"
        tools:orientation="horizontal"
        tools:listitem="@layout/item_home_shop_goods"
        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        app:layout_constraintTop_toBottomOf="@+id/rv_home_shop_feed"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10" />



</androidx.constraintlayout.widget.ConstraintLayout>