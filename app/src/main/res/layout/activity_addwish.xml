<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <include layout="@layout/common_header_items"/>

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/wish_drug_name_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_drug_name_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_must_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"/>

                <TextView
                    android:id="@+id/wish_drug_name_tv"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_drug_name_iv"
                    android:text="@string/wish_tv01"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>

                <EditText
                    android:id="@+id/wish_drug_name_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_toRightOf="@id/wish_drug_name_tv"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/wish_tv02"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/main_line"/>

            <RelativeLayout
                android:id="@+id/wish_manufacturer_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_manufacturer_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_must_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"/>

                <TextView
                    android:id="@+id/wish_manufacturer_tv"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_manufacturer_iv"
                    android:text="@string/wish_tv03"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>

                <EditText
                    android:id="@+id/wish_manufacturer_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_toRightOf="@id/wish_manufacturer_tv"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/wish_tv04"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/main_line"/>

            <RelativeLayout
                android:id="@+id/wish_standard_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_standard_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_must_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"/>

                <TextView
                    android:id="@+id/wish_standard_tv"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_standard_iv"
                    android:text="@string/wish_tv05"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>

                <EditText
                    android:id="@+id/wish_standard_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_toRightOf="@id/wish_standard_tv"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/wish_tv06"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/wish_number_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:layout_marginTop="10dp"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_number_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_h_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"
                    android:visibility="invisible"/>

                <TextView
                    android:id="@+id/wish_number_tv"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_number_iv"
                    android:text="@string/wish_tv010"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>

                <EditText
                    android:maxLength="9"
                    android:id="@+id/wish_number_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_toRightOf="@id/wish_number_tv"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/wish_tv011"
                    android:inputType="number"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/main_line"/>

            <RelativeLayout
                android:id="@+id/wish_price_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_price_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_must_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"
                    android:visibility="invisible"/>

                <TextView
                    android:id="@+id/wish_price_tv"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_price_iv"
                    android:text="@string/wish_tv012"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>

                <EditText
                    android:id="@+id/wish_price_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:layout_toRightOf="@id/wish_price_tv"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="@string/wish_tv013"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/main_line"/>

            <RelativeLayout
                android:id="@+id/wish_else_rl"
                android:layout_width="match_parent"
                android:layout_height="@dimen/add_wish_h_01"
                android:background="@color/white">

                <ImageView
                    android:id="@+id/wish_else_iv"
                    android:layout_width="@dimen/add_wish_must_01"
                    android:layout_height="@dimen/add_wish_must_01"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:src="@drawable/wish01"
                    android:visibility="invisible"/>

                <TextView
                    android:id="@+id/wish_else_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="@dimen/add_wish_left_01"
                    android:layout_toRightOf="@+id/wish_else_iv"
                    android:text="@string/wish_tv07"
                    android:textColor="@color/wish_tv1"
                    android:textSize="@dimen/add_wish_tv01"/>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/wish_else_rl01"
                android:layout_width="match_parent"
                android:layout_height="77dp"
                android:background="@color/white">

                <EditText
                    android:id="@+id/wish_else_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="39dp"
                    android:background="@null"
                    android:gravity="top"
                    android:hint="@string/wish_tv08"
                    android:imeOptions="actionGo"
                    android:textColor="@color/wish_tv2"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="12sp"/>
                <!--请填写您的订单留言-->
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_centerInParent="true"
                android:background="@color/main_line"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="125dp"
                android:background="@color/white">

                <RelativeLayout
                    android:id="@+id/wish_update_rl"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/add_wish_h_01"
                    android:background="@color/white">

                    <ImageView
                        android:id="@+id/wish_update_iv"
                        android:layout_width="@dimen/add_wish_must_01"
                        android:layout_height="@dimen/add_wish_must_01"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:src="@drawable/wish01"
                        android:visibility="invisible"/>

                    <TextView
                        android:id="@+id/wish_update_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="@dimen/add_wish_left_01"
                        android:layout_toRightOf="@+id/wish_update_iv"
                        android:text="@string/wish_tv09"
                        android:textColor="@color/wish_tv1"
                        android:textSize="@dimen/add_wish_tv01"/>

                    <TextView
                        android:id="@+id/wish_update_et"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="15dp"
                        android:layout_marginRight="20dp"
                        android:gravity="center_vertical"
                        android:text="只能上传一张图片"
                        android:textColor="@color/wish_tv2"
                        android:textSize="@dimen/add_wish_tv01"/>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="68dp"
                    android:layout_height="68dp"
                    android:layout_below="@+id/wish_update_rl"
                    android:layout_marginLeft="39dp">

                    <ImageView
                        android:id="@+id/wish_iv"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_alignParentBottom="true"
                        android:src="@drawable/wish_iv01"/>

                    <ImageView
                        android:id="@+id/wish_clear_iv"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/wish03"
                        android:visibility="invisible"/>
                </RelativeLayout>
            </RelativeLayout>

            <Button
                android:id="@+id/wish_commit_btn"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/round_corner_gay_bg"
                android:text="提交"
                android:textColor="@color/login_btn_tv"
                android:textSize="15sp"/>

        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>