<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f7f7f8"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_title"
        style="@style/header_layout">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="@string/authorization_detail" />
    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv_authorization_detail"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:layout_weight="1">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_head"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dimen_dp_10"
                android:background="@drawable/shape_authorization_bg"
                android:paddingBottom="@dimen/dimen_dp_16"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_authorization_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:layout_marginTop="@dimen/dimen_dp_13"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:singleLine="true"
                    android:textColor="@color/color_292933"
                    android:textSize="@dimen/dimen_dp_15"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="仁和中方客户授权申请仁和中方客户授权申请仁和中方客户授权申请仁和中方客户授权申请" />

                <TextView
                    android:id="@+id/tv_applicant"
                    style="@style/authorizationDetailTitle"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    android:text="@string/applicant"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/divider" />

                <TextView
                    android:id="@+id/tv_applicant_phone"
                    style="@style/authorizationDetailTitle"
                    android:text="@string/applicant_phone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_applicant" />

                <TextView
                    android:id="@+id/tv_submit_time"
                    style="@style/authorizationDetailTitle"
                    android:text="@string/submit_time"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_applicant_phone" />

                <TextView
                    android:id="@+id/tv_submit_content"
                    style="@style/authorizationDetailTitle"
                    android:text="@string/submit_content"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_submit_time" />

                <TextView
                    android:id="@+id/tv_applicant_text"
                    style="@style/authorizationDetailContent"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_applicant"
                    app:layout_constraintStart_toEndOf="@+id/tv_applicant_phone"
                    app:layout_constraintTop_toTopOf="@+id/tv_applicant"
                    tools:text="王华" />

                <TextView
                    android:id="@+id/tv_applicant_phone_text"
                    style="@style/authorizationDetailContent"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_applicant_phone"
                    app:layout_constraintStart_toEndOf="@+id/tv_applicant_phone"
                    app:layout_constraintTop_toTopOf="@+id/tv_applicant_phone"
                    tools:text="13657892314" />

                <TextView
                    android:id="@+id/tv_submit_time_text"
                    style="@style/authorizationDetailContent"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_submit_time"
                    app:layout_constraintStart_toEndOf="@+id/tv_applicant_phone"
                    app:layout_constraintTop_toTopOf="@+id/tv_submit_time"
                    tools:text="2019-08-09 18:23:00" />

                <TextView
                    android:id="@+id/tv_submit_content_text"
                    style="@style/authorizationDetailContent"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_14"
                    android:layout_marginEnd="@dimen/dimen_dp_14"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_submit_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/tv_submit_content"
                    app:layout_constraintTop_toTopOf="@+id/tv_submit_content"
                    tools:text="您好!\n&#12288;&#12288;我是【仁和药业医药有限公司】【XXX区域】的业务" />

                <TextView
                    android:id="@+id/tv_submit_content_text_local"
                    style="@style/authorizationDetailContent"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_14"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    android:layout_marginEnd="@dimen/dimen_dp_14"
                    android:layout_marginBottom="@dimen/dimen_dp_30"
                    android:lineSpacingMultiplier="1.2"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_submit_content"
                    tools:text="尊敬的客户：\n\n您好!\n\n&#12288;&#12288;我是【仁和药业医药有限公司】【XXX区域】的业务代表【张三】，感谢您一直以来对本人的信任和支持！\n\n&#12288;&#12288;根据公司总部战略规划，【仁和药业医药有限公司】已和【武汉小药药医药科技有限公司】达成长期合作，以下品种（见底部品种列表）已转移至药帮忙平台进行销售，不再通过其他渠道进行销售，团队成员将专注为大家提供专业的线下产品推广服务，原有的其他合作方式不变，此次销售渠道调整旨在向广大客户提供更多专业、优质、高效的营销推广服务，欢迎您在药帮忙平台指定的控销商城专区【仁和药业医药有限公司】旗舰店进行产品选购。\n\n&#12288;&#12288;为了能够给您提供更加全面的线上和线下产品营销推广服务，需要得到您配合与支持，请您对以下事项进行授权：\n\n&#12288;&#12288;1、授权本人在和您沟通后代替您制作采购订单，并将订单推送至您的药帮忙“我的”APP代下单专区；\n\n&#12288;&#12288;2、订单确认：您在收到订单信息后需要核对确认订单信息，并在48小时内（大促活动为12小时）完成订单确认操作，确认后您需要在48小时内（大促活动为12小时）完成支付，在此时间限内我们将为您锁定订单产品库存确保您能购买到订单产品；\n\n&#12288;&#12288;3、问题处理：如果此时间限内您有疑问也可及时联系我咨询后再确定订单如何操作，如以上订单信息若有误时请您及时联系我重新制作订单，您可以选择确认购买或驳回订单，订单品种仅限于品种列表所列品种；\n\n&#12288;&#12288;4、品种维护：若品种列表涉及的品种有删减或增补，或有价格信息的调整，本人也将第一时间告知于您，同时也请您配合做好产品零售价格的维护工作。\n\n&#12288;&#12288;再次感谢您一直以来对本人工作的支持！\n\n&#12288;&#12288;顺祝\n\n&#12288;&#12288;商祺！" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/dimen_dp_13"
                    android:background="@color/colors_f5f5f5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_authorization_title" />

                <ImageView
                    android:id="@+id/iv_authorization_status"
                    android:layout_width="@dimen/dimen_dp_80"
                    android:layout_height="@dimen/dimen_dp_80"
                    android:layout_marginTop="@dimen/dimen_dp_15"
                    android:layout_marginEnd="@dimen/dimen_dp_15"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/divider" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dimen_dp_10"
                android:layout_marginTop="@dimen/dimen_dp_10"
                android:layout_marginRight="@dimen/dimen_dp_10"
                android:background="@drawable/shape_authorization_bg"
                app:layout_constraintTop_toBottomOf="@+id/cl_head">

                <TextView
                    android:id="@+id/tv_authorization_count"
                    style="@style/authorizationDetailTitle"
                    android:layout_marginTop="@dimen/dimen_dp_11"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="授权申请品种（2）" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_authorization_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_authorization_count" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_authorization_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_63"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nsv_authorization_detail">

        <TextView
            android:id="@+id/tv_agree_authorization"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dimen_dp_10"
            android:background="@drawable/shape_agree_authorization"
            android:gravity="center"
            android:text="@string/agress_authorization"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>