<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_validity"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_validity_date_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:padding="5dp"
            android:text="有效期"
            android:textColor="@color/color_text_title_hint"
            android:textSize="14dp" />

        <TextView
            android:id="@+id/tv_validity_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@+id/tv_validity_date_hint"
            android:drawableRight="@drawable/aptitude_more"
            android:drawablePadding="10dp"
            android:padding="5dp"
            android:text="请选择"
            android:textColor="@color/color_text_value_hint"
            android:textSize="14dp" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_no"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_no_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:padding="5dp"
            android:text="编     号"
            android:textColor="@color/color_text_title_hint"
            android:textSize="13dp" />

        <EditText
            android:id="@+id/et_no"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp"
            android:layout_toRightOf="@+id/tv_no_hint"
            android:background="@null"
            android:gravity="left"
            android:maxLength="200"
            android:padding="5dp"
            android:textColor="#333333"
            android:textColorHint="@color/color_text_value_hint"
            android:textSize="14dp"
            android:visibility="gone"
            tools:hint="请填写"
            tools:visibility="visible" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:background="@color/color_divider_bg" />
</LinearLayout>
