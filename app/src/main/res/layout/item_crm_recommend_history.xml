<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_recommend_time"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_30"
        android:background="@color/color_f7f7f8"
        android:drawableRight="@drawable/right_new"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/dimen_dp_14"
        android:paddingRight="@dimen/dimen_dp_14"
        android:textColor="@color/text_575766"
        android:textSize="@dimen/dimen_dp_15"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="推荐时间：2020-06-11" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@id/tv_recommend_time" />

<!--    <View-->
<!--        android:id="@+id/touch_overlay"-->
<!--        android:layout_width="0dp"-->
<!--        android:layout_height="0dp"-->
<!--        app:layout_constraintBottom_toBottomOf="@+id/rv"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent" />-->
</androidx.constraintlayout.widget.ConstraintLayout>