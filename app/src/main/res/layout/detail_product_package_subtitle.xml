<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:id="@+id/ll_root"
              android:layout_width="match_parent"
              android:layout_height="40dp"
              android:layout_marginBottom="5dp"
              android:layout_marginTop="0.5dp"
              android:background="#FFFAFAFA"
              android:orientation="horizontal"
              android:paddingRight="15dp">

    <TextView
        android:id="@+id/tv_price"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical|right"
        android:text=""
        android:textColor="@color/text_676773"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_total"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="4dp"
        android:gravity="center_vertical|right"
        android:text=""
        android:textColor="@color/record_red"
        android:textSize="16sp"
        android:textStyle="bold" />

</LinearLayout>