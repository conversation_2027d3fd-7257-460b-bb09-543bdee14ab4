<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tl="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:rv_backgroundColor="#FFD8DB"
        app:rv_cornerRadius="10dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:gravity="center"
            android:text="动态布局标题"
            android:textSize="16sp" />
        <!--增加自己的固定布局-->
        <LinearLayout
            android:id="@+id/rl_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:orientation="vertical">

            <com.flyco.tablayout.SegmentTabLayout
                android:id="@+id/tabs"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                app:tl_bar_color="#F22F1E"
                app:tl_bar_stroke_width="0dp"
                app:tl_divider_width="0dp"
                app:tl_indicator_anim_enable_819="true"
                app:tl_indicator_color="#FFD8DB"
                app:tl_indicator_corner_radius="10dp"
                app:tl_textSelectColor="#C7011F"
                app:tl_textUnselectColor="@color/white" />

            <com.ybmmarket20.view.NoScrollView2Pager
                android:id="@+id/viewpager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="10dp" />

        </LinearLayout>
    </com.ybmmarket20.common.widget.RoundLinearLayout>

</merge>