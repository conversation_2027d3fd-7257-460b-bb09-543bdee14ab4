<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageView
        android:id="@+id/iv_scan"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:src="@drawable/icon_home_steady_scan"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toStartOf="@+id/v_search_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />


    <View
        android:id="@+id/v_search_bg"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_34"
        android:background="@color/colors_f5f5f5"
        app:layout_constraintEnd_toStartOf="@+id/iv_message"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/iv_scan"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginStart="@dimen/dimen_dp_6"
        android:src="@drawable/icon_home_steady_search"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintStart_toStartOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_31"
        android:src="@drawable/icon_home_steady_search"
        android:text="@string/search_hint"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_13"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintStart_toStartOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />

    <ImageView
        android:id="@+id/iv_voice"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginEnd="@dimen/dimen_dp_6"
        android:src="@drawable/icon_home_steady_voice"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toEndOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />


    <ImageView
        android:id="@+id/iv_message"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:src="@drawable/icon_home_steady_message"
        app:layout_constraintBottom_toBottomOf="@+id/v_search_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/v_search_bg"
        app:layout_constraintTop_toTopOf="@+id/v_search_bg" />

    <TextView
        android:id="@+id/tv_home_steady_message_bubble"
        android:layout_width="@dimen/dimen_dp_15"
        android:layout_height="@dimen/dimen_dp_15"
        android:background="@drawable/shape_home_steady_message_bubble"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_11"
        app:layout_constraintCircle="@id/iv_message"
        app:layout_constraintCircleAngle="45"
        app:layout_constraintCircleRadius="@dimen/dimen_dp_12"
        android:gravity="center"
        android:visibility="gone"
        tools:text="9+" />
</merge>