<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <EditText
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_90"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/icon_refund_reason_desc"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingTop="@dimen/dimen_dp_11"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:gravity="start"
        android:hint="请描述具体原因（必填）："
        app:layout_constraintStart_toStartOf="parent"
        android:inputType="text"
        tools:ignore="Autofill" />

</androidx.constraintlayout.widget.ConstraintLayout>