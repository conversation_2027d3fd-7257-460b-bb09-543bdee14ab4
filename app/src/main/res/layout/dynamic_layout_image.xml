<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:text="动态布局标题"
        style="@style/dynamic_layout_text" />
    <!--增加自己的固定布局-->
    <LinearLayout
        android:id="@+id/ll_iv_root"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll_iv_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:scaleType="fitXY" />

            <ImageView
                android:id="@+id/iv_2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:scaleType="fitXY" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_iv_2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">


            <ImageView
                android:id="@+id/iv_3"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:scaleType="fitXY" />

            <ImageView
                android:id="@+id/iv_4"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:scaleType="fitXY" />
        </LinearLayout>
    </LinearLayout>
</merge>