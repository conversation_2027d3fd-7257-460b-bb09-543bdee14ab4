<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivPayItem"
        android:layout_width="@dimen/dimen_dp_21"
        android:layout_height="@dimen/dimen_dp_21"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_11"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/pay_way_alipay" />

    <TextView
        android:id="@+id/tvPayItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_14"
        app:layout_constraintBottom_toBottomOf="@+id/ivPayItem"
        app:layout_constraintStart_toEndOf="@+id/ivPayItem"
        app:layout_constraintTop_toTopOf="@+id/ivPayItem"
        tools:text="支付宝" />

    <TextView
        android:id="@+id/tvPayItemTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:text="fdfdf"
        android:textColor="#676773"
        android:textSize="@dimen/dimen_dp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvPayItemTitle"
        app:layout_constraintStart_toEndOf="@+id/tvPayItemTitle"
        app:layout_constraintTop_toTopOf="@+id/tvPayItemTitle"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/ivPayItemTag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_16"
        android:layout_marginStart="@dimen/dimen_dp_4"
        android:scaleType="fitStart"
        app:layout_constraintBottom_toBottomOf="@+id/tvPayItemTitle"
        app:layout_constraintStart_toEndOf="@+id/tvPayItemTitle" />

    <TextView
        android:id="@+id/tvPayItemDes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:textColor="@color/color_9494A6"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintStart_toStartOf="@+id/tvPayItemTitle"
        app:layout_constraintTop_toBottomOf="@+id/ivPayItem"
        tools:text="随机立减，最高减￥99" />

    <TextView
        android:id="@+id/tvPayItemBottomTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:textColor="#676773"
        android:textSize="@dimen/dimen_dp_12"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/tvPayItemTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvPayItemDes"
        tools:text="请提单后到收银台重新选择支付渠道"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvLookTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_2"
        android:text="查看"
        android:layout_marginStart="4dp"
        android:textColor="#00B955"
        android:textSize="11sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/tvPayItemBottomTips"
        app:layout_constraintTop_toBottomOf="@+id/tvPayItemDes"
        tools:visibility="visible" />

    <RadioButton
        android:id="@+id/rbPayItem"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:background="@drawable/payway_bg_selector"
        android:button="@null"
        android:checked="false"
        app:layout_constraintBottom_toBottomOf="@+id/ivPayItem"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivPayItem" />

    <View
        android:id="@+id/clickView"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        app:layout_constraintStart_toStartOf="@+id/rbPayItem"
        app:layout_constraintTop_toTopOf="@+id/rbPayItem" />
</androidx.constraintlayout.widget.ConstraintLayout>