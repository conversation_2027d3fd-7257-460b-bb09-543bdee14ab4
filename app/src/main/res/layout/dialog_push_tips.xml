<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="12dp">
    <ImageView
        android:id="@+id/ivTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        android:adjustViewBounds="true"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/ic_push_tips_topicon"/>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:textColor="@color/color_222222"
        android:textStyle="bold"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivTop"
        android:text="订单状态、优惠促销不错过" />
    <TextView
        android:id="@+id/tvSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:textColor="@color/color_666"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        android:text="开启消息通知，可随时了解订单状态、优惠促销、商家回复等消息" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tvOpen"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:textColor="@color/white"
        android:textSize="16dp"
        android:layout_marginHorizontal="@dimen/pabr_dimen20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:rv_backgroundColor="@color/color_00b955"
        app:rv_cornerRadius="6dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSubtitle"
        android:text="开启通知" />

    <TextView
        android:id="@+id/tvSkip"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:gravity="center"
        android:textColor="@color/color_666"
        android:textSize="14dp"
        android:layout_marginHorizontal="@dimen/pabr_dimen20dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvOpen"
        android:text="跳过" />
</com.ybmmarket20.common.widget.RoundConstraintLayout>