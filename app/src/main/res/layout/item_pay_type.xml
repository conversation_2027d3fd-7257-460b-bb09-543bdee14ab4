<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_44"
    android:layout_marginStart="@dimen/dimen_dp_9"
    android:background="@drawable/selector_pay_type_bg">

    <TextView
        android:id="@+id/tvPayName"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:text="在线支付"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/color_292933"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@+id/tvTip"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvTip"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="便捷方便"
        android:layout_marginTop="@dimen/dimen_dp_3"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="@color/color_676773"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPayName" />

    <ImageView
        android:id="@+id/ivPaySelected"
        android:layout_width="@dimen/dimen_dp_9"
        android:layout_height="@dimen/dimen_dp_7"
        android:src="@drawable/icon_pay_type_selected"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/ivOverDue"
        android:layout_width="@dimen/dimen_dp_23"
        android:layout_height="@dimen/dimen_dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/icon_pay_type_over_due" />

</androidx.constraintlayout.widget.ConstraintLayout>