<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <include
        android:id="@+id/include_oder_rebate_view"
        layout="@layout/order_rebate_voucher_view"
        android:visibility="gone" />

    <include
        android:id="@+id/include_order_coupon_view"
        layout="@layout/order_coupon_view"
        android:visibility="gone" />

    <include
        android:id="@+id/include_order_red_pack_view"
        layout="@layout/order_red_pack_view"
        android:visibility="gone" />

    <include
        android:id="@+id/include_order_aptitude_overdue_view"
        layout="@layout/order_aptitude_overdue_view"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_image_view"
        android:layout_width="match_parent"
        android:visibility="gone"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />
</androidx.constraintlayout.widget.ConstraintLayout>