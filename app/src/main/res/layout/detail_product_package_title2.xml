<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout
    android:id="@+id/ll_root"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_marginBottom="1px"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="10dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="10dp"
    android:paddingRight="10dp"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius_TL="6.67dp"
    app:rv_cornerRadius_TR="6.67dp">

    <CheckBox
        android:id="@+id/cb_choice"
        style="@style/CustomCheckboxTheme"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_gravity="center"
        android:layout_marginLeft="5dp"
        android:clickable="false"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="2"
        android:text=""
        android:textColor="@color/text_292933"
        android:textSize="16sp"
        android:textStyle="bold"/>

    <RelativeLayout
        android:id="@+id/rl_product_edit"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical">

        <include layout="@layout/product_edit_layout_02"/>

    </RelativeLayout>

</com.ybmmarket20.common.widget.RoundLinearLayout>