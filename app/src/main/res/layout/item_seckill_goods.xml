<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dimen_dp_94"
    android:layout_height="wrap_content"
    android:paddingEnd="@dimen/dimen_dp_10"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv"
        android:layout_width="@dimen/dimen_dp_72"
        android:layout_height="@dimen/dimen_dp_72"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_margin="@dimen/dimen_dp_6"
        android:scaleType="fitCenter" />

    <View
        android:id="@+id/v_mask"
        android:layout_width="@dimen/dimen_dp_84"
        android:layout_height="@dimen/dimen_dp_84"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/shape_seckill_goods_bg" />

    <TextView
        android:id="@+id/tv_tag"
        android:layout_width="@dimen/dimen_dp_70"
        android:layout_height="@dimen/dimen_dp_14"
        android:background="@drawable/icon_seckill_tag"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/v_mask"
        tools:text="限量200件"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dimen_dp_3"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:singleLine="true"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="@color/white" />

    <TextView
        android:id="@+id/tv_goods_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/v_mask"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="广云堂银杏叶"
        android:textSize="@dimen/dimen_dp_12"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_4"
        android:singleLine="true"
        android:textColor="@color/color_292933" />

    <TextView
        android:id="@+id/tv_goods_spec"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_name"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_4"
        android:layout_marginTop="@dimen/dimen_dp_2"
        tools:text="50mg*1盒"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:singleLine="true" />

    <TextView
        android:id="@+id/tv_goods_price"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_spec"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_6"
        android:layout_marginTop="@dimen/dimen_dp_5"
        tools:text="¥26.9"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="#FFFF2121"
        android:singleLine="true" />

    <TextView
        android:id="@+id/tv_goods_discount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_price"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_4"
        android:layout_marginTop="@dimen/dimen_dp_2"
        tools:text="¥39"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="@color/color_9494A6"
        android:singleLine="true" />

</androidx.constraintlayout.widget.ConstraintLayout>