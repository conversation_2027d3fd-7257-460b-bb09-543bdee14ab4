<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fafafa"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/fl_unused"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RadioButton
                android:id="@+id/rb_coupon_unused"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:button="@null"
                android:checked="true"
                android:clickable="false"
                android:textStyle="bold"
                android:gravity="center"
                android:text="未使用（0）"
                android:textColor="@drawable/product_base_selector_textcolor"
                android:textSize="17sp" />

            <RadioButton
                android:id="@+id/rb_coupon_unused_t"
                style="@style/coupon_member_tab_line"
                android:checked="true" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_used"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RadioButton
                android:id="@+id/rb_coupon_used"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:button="@null"
                android:checked="false"
                android:clickable="false"
                android:gravity="center"
                android:text="已使用（0）"
                android:textColor="@drawable/product_base_selector_textcolor"
                android:textSize="15sp" />

            <RadioButton
                android:id="@+id/rb_coupon_used_t"
                style="@style/coupon_member_tab_line" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_expired"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <RadioButton
                android:id="@+id/rb_coupon_expired"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:button="@null"
                android:clickable="false"
                android:gravity="center"
                android:text="已过期（0）"
                android:textColor="@drawable/product_base_selector_textcolor"
                android:textSize="15sp" />

            <RadioButton
                android:id="@+id/rb_coupon_expired_t"
                style="@style/coupon_member_tab_line" />
        </FrameLayout>


    </RadioGroup>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/coupon_meber_lv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="8dp"
        android:background="#fafafa">

    </com.ybm.app.view.CommonRecyclerView>

</LinearLayout>