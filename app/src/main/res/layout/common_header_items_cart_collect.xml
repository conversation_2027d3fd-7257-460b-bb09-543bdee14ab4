<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_title"
    style="@style/header_layout"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_back"
        style="@style/header_layout_left"
        android:src="@drawable/ic_back" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/header_layout_mid"
        android:text="" />

    <RelativeLayout
        android:id="@+id/rl_cart"
        style="@style/header_layout_right_img"
        android:layout_width="56dp"
        android:layout_alignParentRight="false"
        android:layout_marginLeft="5dp"
        android:layout_toLeftOf="@+id/tv_edit"
        android:visibility="visible">

        <ImageView
            android:id="@+id/iv_cart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/cart_icon" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:layout_marginRight="2dp"
            android:background="@drawable/bg_message"
            android:gravity="center"
            android:text=""
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_edit"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="7dp"
        android:gravity="center_vertical"
        android:text="编辑"
        android:textColor="@color/color_676773"
        android:textSize="14sp" />
</RelativeLayout>