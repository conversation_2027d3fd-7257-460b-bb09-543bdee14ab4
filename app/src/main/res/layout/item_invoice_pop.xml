<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_40">

    <TextView
        android:id="@+id/tv_invoice_name"
        style="@style/InvoicePopTitle"
        android:text="发票"
        android:layout_marginStart="@dimen/dimen_dp_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_invoice_btn"
        android:layout_width="@dimen/dimen_dp_40"
        android:layout_height="@dimen/dimen_dp_21"
        android:text="查看"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="#131415"
        android:gravity="center"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:background="@drawable/shape_invoice_pop_btn"
        app:layout_constraintBottom_toBottomOf="@+id/tv_invoice_name"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_invoice_name" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/color_f7f7f8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>