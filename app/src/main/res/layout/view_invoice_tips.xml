<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:background="@color/colors_f4f4f4"
    android:padding="12dp"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_notice_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/icon_invoice_tips"
        android:text="注意事项"
        android:textColor="@color/color_222222"
        android:textSize="14sp"
        android:drawablePadding="2dp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_notice_content1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="4sp"
        android:text="@string/invoice_tips"
        android:textColor="@color/color_444444"
        android:textSize="14sp"
        android:layout_marginTop="5dp"
        app:layout_constraintTop_toBottomOf="@id/tv_notice_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>