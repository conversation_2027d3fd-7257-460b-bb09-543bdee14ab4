<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 发票类型 -->
        <LinearLayout
            android:id="@+id/ll_invoice_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="发票类型"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_invoice_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="增值税专用发票" />
        </LinearLayout>


        <!-- 公司名称 -->
        <LinearLayout
            android:id="@+id/ll_company_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="公司名称"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_company_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="辽宁市西安区永和大药房" />

        </LinearLayout>

        <!-- 纳税人识别号 -->
        <LinearLayout
            android:id="@+id/ll_tax_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="纳税人识别号"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_tax_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="91420112MA4KLGHW01" />

        </LinearLayout>

        <!-- 地址 -->
        <LinearLayout
            android:id="@+id/ll_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="地址"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="武汉市东湖新技术开发区关东大道77号金融港后台服务中心一期A1栋第3层301室" />

        </LinearLayout>

        <!-- 电话 -->
        <LinearLayout
            android:id="@+id/ll_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="电话"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="027-********" />

        </LinearLayout>

        <!-- 开户银行 -->
        <LinearLayout
            android:id="@+id/ll_bank_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="开户银行"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_bank_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="中国建设银行武汉白沙洲支行" />

        </LinearLayout>

        <!-- 银行账号 -->
        <LinearLayout
            android:id="@+id/ll_bank_account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="银行账号"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_bank_account"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="62043098712004076353" />
        </LinearLayout>

        <!-- 修改时间 -->
        <LinearLayout
            android:id="@+id/ll_edit_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="修改时间"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_edit_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="2025年9月18日 13:30" />

        </LinearLayout>

        <!-- 审核状态 -->
        <LinearLayout
            android:id="@+id/ll_status_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="审核状态"
                android:textColor="@color/text_666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_status_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_333333"
                android:textSize="14sp"
                tools:text="审核通过" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
