<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colors_fff7ef"
        android:gravity="center"
        android:minHeight="30dp"
        android:padding="2dp"
        android:text="账号或密码错误，请重新输入账号及密码"
        android:textColor="@color/colors_99664D"
        android:textSize="14sp"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/login_user_name_wrapper"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:theme="@style/inputLayoutLineColor"
            app:hintTextAppearance="@style/inputLayoutHintAppearance">

            <EditText
                android:id="@+id/et_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入手机号"
                android:inputType="phone"
                android:maxLength="11"
                android:longClickable="false"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/loginTextAppearance"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/login_password_wrapper"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:theme="@style/inputLayoutLineColor"
            app:hintTextAppearance="@style/inputLayoutHintAppearance"
            app:passwordToggleDrawable="@drawable/password_taggle_select"
            app:passwordToggleEnabled="true">

            <EditText
                android:id="@+id/et_pwd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入登录密码"
                android:inputType="textPassword"
                android:longClickable="false"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/loginTextAppearance"
                android:textSize="16sp" />
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

    <Button
        android:id="@+id/btn_add"
        style="@style/common_base_btn_layout"
        android:layout_marginTop="50dp"
        android:text="确认添加" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:src="@drawable/icon_login_bg" />
    </RelativeLayout>


</LinearLayout>