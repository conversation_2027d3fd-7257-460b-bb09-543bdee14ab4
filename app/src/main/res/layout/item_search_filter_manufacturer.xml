<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_ybm"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal">

    <CheckBox
        android:id="@+id/cb_item"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_29"
        android:background="@drawable/bg_selecter_seach_spacfication"
        android:button="@null"
        android:clickable="false"
        android:textColor="@color/selector_text_color_292933"
        tools:checked="true"
        android:paddingStart="@dimen/dimen_dp_9"
        android:textSize="@dimen/dimen_dp_12"
        tools:text="15mg*10" />

</LinearLayout>