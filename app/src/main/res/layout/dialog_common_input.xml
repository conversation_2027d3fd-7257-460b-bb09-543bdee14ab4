<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/my_dialog_bg"
    android:orientation="vertical"
    android:paddingTop="12dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:text="新建补货计划单"
        android:textColor="@color/color_292933"
        android:textSize="17sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="8dp"
        android:text="请为计划单输入名称"
        android:textColor="@color/color_292933"
        android:textSize="14sp" />

    <com.ybmmarket20.common.widget.RoundRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginTop="10dp"
        app:rv_backgroundColor="@color/color_f7f7f8"
        app:rv_cornerRadius="2dp">

        <EditText
            android:id="@+id/et_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/iv_clear"
            android:background="@null"
            android:inputType="text"
            android:maxLength="10"
            android:maxLines="1"
            android:padding="8dp"
            android:selectAllOnFocus="true"
            android:singleLine="true"
            android:textColor="@color/color_292933"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:scaleType="center"
            android:src="@drawable/clear_sousou"
            android:visibility="gone" />
    </com.ybmmarket20.common.widget.RoundRelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="14dp"
        android:background="@color/colors_DDDDDD" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="我再想想"
            android:textColor="@color/color_292933"
            android:textSize="17sp" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/colors_DDDDDD" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/base_colors_new"
            android:textSize="17sp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>