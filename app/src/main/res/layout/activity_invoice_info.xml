<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg">

    <!-- 头部 -->
    <include
        android:id="@+id/header"
        layout="@layout/common_header_items"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/rv_edit_history"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header">

        <include
            android:id="@+id/invoice_tips"
            layout="@layout/view_invoice_tips" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="发票信息" />

        <include
            android:id="@+id/invoice_item"
            layout="@layout/item_invoice_edit_history" />

    </androidx.core.widget.NestedScrollView>

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/ll_empty_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.5"
            android:src="@drawable/icon_empty" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无修改记录"
            android:textColor="@color/text_999999"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>