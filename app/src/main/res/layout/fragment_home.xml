<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_home_cms"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            app:elevation="0dp">

            <com.ybmmarket20.view.DynamicCommonLayout
                android:id="@+id/home_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll" />
        </com.google.android.material.appbar.AppBarLayout>

        <com.ybm.app.view.refresh.RecyclerRefreshLayout
            android:id="@+id/rfl_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="com.ybmmarket20.view.RefreshViewBehavior">

            <com.ybmmarket20.view.NoScrollview
                android:id="@+id/home_scrollview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none"
                android:fillViewport="true"
                android:overScrollMode="never"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <com.ybmmarket20.view.DynamicHomeLayout
                        android:id="@+id/home"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:orientation="vertical" />

                    <com.ybmmarket20.view.DynamicProductTabLayout
                        android:id="@+id/tl_product"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:orientation="vertical" />

                </LinearLayout>

            </com.ybmmarket20.view.NoScrollview>
        </com.ybm.app.view.refresh.RecyclerRefreshLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <include layout="@layout/home_search" />

    <ImageView
        android:id="@+id/iv_fastscroll"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="32dp"
        android:layout_marginRight="20dp"
        android:src="@drawable/icon_fast_to_top"
        android:visibility="invisible" />

    <ImageView
        android:id="@+id/iv_ad_suspension"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="110dp"
        android:src="@drawable/icon_ad_suspension"
        android:visibility="invisible" />

    <com.ybmmarket20.view.DialImageView
        android:id="@+id/iv_dial_suspension"
        android:layout_width="114dp"
        android:layout_height="76dp"
        android:layout_gravity="bottom|right"
        android:layout_marginBottom="90dp"
        android:src="@drawable/transparent"
        android:visibility="gone" />

</FrameLayout>
