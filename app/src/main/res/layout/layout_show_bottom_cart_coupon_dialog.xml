<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_weight="6"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_cart_bottom_coupon_bg"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <!--<View-->
            <!--style="@style/view_line_icon"-->
            <!--android:layout_marginRight="11dp"-->
            <!--android:layout_toLeftOf="@+id/tv"/>-->

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/text_292933"
                android:textSize="17sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="优惠券" />

            <!--<View-->
            <!--style="@style/view_line_icon"-->
            <!--android:layout_marginLeft="11dp"-->
            <!--android:layout_toRightOf="@+id/tv"/>-->

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                app:tabGravity="fill"
                android:layout_centerInParent="true"
                app:tabIndicatorColor="@android:color/transparent"
                app:tabMinWidth="0dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="6dp"
                app:tabPaddingStart="6dp"
                android:overScrollMode="never"
                app:tabRippleColor="@null"
                android:visibility="gone"
                android:layout_marginStart="3dp"
                android:layout_marginTop="7dp" />


            <ImageView
                android:id="@+id/product_detail_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:src="@drawable/icon_cart_bottom_coupon_close" />
        </RelativeLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"/>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_loading"
        android:layout_width="@dimen/dimen_dp_40"
        android:layout_height="@dimen/dimen_dp_40"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>