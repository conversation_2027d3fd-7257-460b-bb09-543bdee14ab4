<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_balance_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="12dp"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    android:orientation="vertical"
    android:paddingTop="12dp">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        />
    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6.5dp"
        android:layout_toEndOf="@+id/iv_icon"
        android:layout_centerVertical="true"
        android:textColor="#292933"
        android:textSize="16sp"
        tools:text="小药白条" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/iv_right"
        android:layout_marginEnd="10dp"
        android:layout_centerVertical="true"
        android:textColor="#9494A6"
        android:textSize="14sp"
        tools:text="审核中" />



    <ImageView
        android:id="@+id/iv_right"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_my_balance_arrow_right" />

    </RelativeLayout>
    <TextView
        android:id="@+id/tv_tips"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="15dp"
        android:textColor="#99664D"
        android:text="@string/str_mine_my_banking_bottom_tips"
        android:background="@drawable/icon_my_banking_bottom_list_bg"
        android:gravity="center"

        />
</LinearLayout>