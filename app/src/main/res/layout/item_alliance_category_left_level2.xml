<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:textColor="@color/text_9494A6"
        android:background="@drawable/bg_category_level2"
        android:textSize="12sp"
        android:gravity="center"
        android:maxLines="2"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:ellipsize="end"
        tools:text="哈哈哈哈"
        />

</LinearLayout>