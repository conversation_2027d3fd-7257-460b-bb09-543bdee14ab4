<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.ybmmarket20.activity.MyBankingForWishListActivity">

    <include layout="@layout/common_header_items" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_fff7ef"
                android:padding="15dp"
                android:text="@string/str_my_banking_wish_list_top_tips"
                android:textColor="@color/colors_99664D"
                android:textSize="14dp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="19dp">

                <TextView
                    android:id="@+id/tv_subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/str_my_banking_wish_list_subtitle"
                    android:textColor="@color/text_292933"
                    android:textSize="18dp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="5dp"
                    android:layout_toStartOf="@+id/tv_subtitle"
                    android:src="@drawable/icon_my_banking_wish_list_left" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="5dp"
                    android:layout_toEndOf="@+id/tv_subtitle"
                    android:src="@drawable/icon_my_banking_wish_list_right" />
            </RelativeLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingLeft="15dp"
                android:paddingRight="15dp">

                # 药店名称

                <LinearLayout
                    android:id="@+id/ll_shop_name"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="药店名称"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <TextView
                        android:id="@+id/tv_shop_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:textColor="#9595A6"
                        android:textSize="15dp"
                        tools:text="武汉仁爱大医院" />

                </LinearLayout>

                <View
                    android:id="@+id/divider1"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:background="@drawable/bg_banking_divider_unselected"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_shop_name" />

                # 联系人

                <LinearLayout
                    android:id="@+id/ll_contact"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider1"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="联系人"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <EditText
                        android:id="@+id/et_name"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/str_my_banking_wish_list_please_enter_username"
                        android:maxLength="30"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:longClickable="false"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="15dp"
                        tools:text="李建国" />

                </LinearLayout>

                <View
                    android:id="@+id/divider2"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@drawable/bg_banking_divider_unselected"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_contact" />

                # 联系手机

                <LinearLayout
                    android:id="@+id/ll_mobile"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider2"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="联系手机"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <EditText
                            android:id="@+id/et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@null"
                            android:gravity="center_vertical"
                            android:inputType="phone"
                            android:maxLength="11"
                            android:singleLine="true"
                            android:hint="@string/str_my_banking_wish_list_please_enter_phone"
                            android:longClickable="false"
                            android:textColor="#292933"
                            android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                            android:textSize="15dp"
                            tools:text="***********" />

                        <TextView
                            android:id="@+id/tv_phone_tips"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center_vertical|right"
                            android:text="请输入正确的手机号"
                            android:textColor="#FF2121"
                            android:layout_alignParentRight="true"
                            tools:visibility="visible"
                            android:visibility="gone" />

                    </RelativeLayout>


                </LinearLayout>

                <View
                    android:id="@+id/divider3"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@drawable/bg_banking_divider_unselected"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_mobile" />

                # 注册资金

                <LinearLayout
                    android:id="@+id/ll_register_fund"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider3"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="注册资金"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <EditText
                        android:id="@+id/et_register_fund"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:inputType="numberDecimal"
                        android:longClickable="false"
                        android:singleLine="true"
                        android:hint="@string/str_my_banking_wish_list_please_enter_register_fund"
                        android:textColor="#292933"
                        android:textSize="15dp"
                        tools:text="50000" />

                </LinearLayout>


                <View
                    android:id="@+id/divider4"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_register_fund" />

                # 预计贷款

                <LinearLayout
                    android:id="@+id/ll_loans"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider4"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="预计贷款"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <EditText
                        android:id="@+id/et_loans"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/str_my_banking_wish_list_please_enter_loans"
                        android:inputType="numberDecimal"
                        android:maxLength="100"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="15dp" />

                </LinearLayout>

                <View
                    android:id="@+id/divider5"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_loans" />

                # 备注

                <LinearLayout
                    android:id="@+id/ll_remark"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:orientation="horizontal"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider5"
                    tools:visibility="visible">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:text="备注"
                        android:textColor="#292933"
                        android:textSize="15dp" />

                    <EditText
                        android:id="@+id/et_remark"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@null"
                        android:gravity="center_vertical"
                        android:hint="@string/str_my_banking_wish_list_please_enter_remark"
                        android:maxLength="100"
                        android:singleLine="true"
                        android:textColor="#292933"
                        android:longClickable="false"
                        android:textCursorDrawable="@drawable/shape_edit_cursor_drawable"
                        android:textSize="15dp" />

                </LinearLayout>

                <View
                    android:id="@+id/divider6"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="9dp"
                    android:background="#F5F5F5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/ll_remark" />

                <TextView
                    android:id="@+id/tv_promise"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="23.5dp"
                    android:gravity="center_vertical"
                    android:text="@string/str_my_banking_wish_list_promise"
                    android:textColor="@color/color_00b377"
                    android:textSize="13sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider6" />

                <com.ybmmarket20.view.ButtonObserver
                    android:id="@+id/btn_submit"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/selector_common_btn"
                    android:enabled="false"
                    android:gravity="center"
                    android:text="@string/str_my_banking_wish_list_submit"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_promise" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </ScrollView>
</LinearLayout>
