<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="center_horizontal">

        <!-- 手势指引图片 - 将通过代码动态定位 -->
        <ImageView
            android:id="@+id/icon_quck_top"
            android:layout_width="159dp"
            android:layout_height="@dimen/dimen_dp_103"
            android:layout_marginTop="@dimen/dimen_dp_108"
            android:layout_marginRight="@dimen/dimen_dp_24"
            android:src="@drawable/icon_quck_top"
            android:layout_gravity="right"
            android:clickable="false"
            android:focusable="false" />

        <!-- 手势指引图片 - 将通过代码动态定位 -->
        <ImageView
            android:layout_width="227.5dp"
            android:layout_height="108.5dp"
            android:layout_marginTop="6dp"
            android:layout_marginRight="@dimen/dimen_dp_24"
            android:layout_gravity="right"
            android:src="@drawable/icon_quck_bottom"
            android:clickable="false"
            android:focusable="false" />

    </LinearLayout>



</FrameLayout>
