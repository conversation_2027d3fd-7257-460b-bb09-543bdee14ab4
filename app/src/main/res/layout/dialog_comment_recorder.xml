<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/bt_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="19dp"
        android:padding="10dp"
        android:layout_marginRight="9dp"
        android:src="@drawable/icon_comment_dialog_delete" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="33dp"
        android:text="说出你想要输入的内容"
        android:textColor="#FF292933"
        android:textSize="20sp" />

    <ImageView
        android:id="@+id/iv_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:src="@drawable/voice_loading" />

    <Button
        android:id="@+id/bt_end"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@id/iv_loading"
        android:layout_marginLeft="35dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="35dp"
        android:layout_marginBottom="25dp"
        android:background="@drawable/selector_comment_commit"
        android:text="说完了"
        android:textColor="@color/white" />

</RelativeLayout>