<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingBottom="@dimen/dimen_dp_5"
    android:background="@drawable/shape_home_steady_shop1">

    <TextView
        android:id="@+id/tv_shop_name"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_33"
        android:layout_margin="@dimen/dimen_dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="武汉鑫弘森药业有限公司"
        android:lines="2"
        android:ellipsize="end"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_292933" />

    <View
        android:id="@+id/tv_tag_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintDimensionRatio="h, 97:25"
        app:layout_constraintTop_toBottomOf="@+id/tv_shop_name"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:background="@drawable/icon_home_shop_tag1"
        android:paddingStart="@dimen/dimen_dp_5"
        android:paddingEnd="@dimen/dimen_dp_19"
        android:layout_marginTop="@dimen/dimen_dp_4"
        android:layout_marginEnd="@dimen/dimen_dp_5" />

    <TextView
        android:id="@+id/tv_tag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_0"
        tools:text="123456"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="@color/color_ff2121"
        android:gravity="center_vertical"
        android:textStyle="bold"
        android:layout_marginStart="@dimen/dimen_dp_3"
        app:layout_constraintStart_toStartOf="@+id/tv_tag_bg"
        app:layout_constraintTop_toTopOf="@+id/tv_tag_bg"
        app:layout_constraintBottom_toBottomOf="@+id/tv_tag_bg"
        android:singleLine="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:paddingBottom="@dimen/dimen_dp_5"
        app:layout_constraintTop_toBottomOf="@+id/tv_tag_bg">

        <ImageView
            android:id="@+id/iv_goods"
            android:layout_width="@dimen/dimen_dp_72"
            android:layout_height="@dimen/dimen_dp_72"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dimen_dp_3"
            android:scaleType="fitCenter" />

        <View
            android:layout_width="@dimen/dimen_dp_72"
            android:layout_height="@dimen/dimen_dp_72"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:background="#05000000"
            android:layout_marginTop="@dimen/dimen_dp_3" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/iv_goods"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_5"
            tools:text="¥6.9"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:textSize="@dimen/dimen_dp_10"
            android:textStyle="bold"
            android:textColor="@color/color_ff2121" />

        <TextView
            android:id="@+id/tv_price_after_discount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_price"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_5"
            tools:text="折后约¥16.32"
            android:layout_marginTop="@dimen/dimen_dp_1"
            android:textSize="@dimen/dimen_dp_10"
            android:textColor="@color/color_676773" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>