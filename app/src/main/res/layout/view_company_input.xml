<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_required"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="*"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textColor="#FA1313"/>
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_222"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/tv_required"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_999"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@id/tv_title"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="start"
        android:hint="请输入"
        android:inputType="textMultiLine"
        android:maxLines="3"
        android:padding="10dp"
        android:textColor="@color/color_222"
        android:textColorHint="@color/color_999"
        android:textSize="14sp"
        android:background="@drawable/shape_4corner_e0e0e0"
        app:layout_constraintTop_toBottomOf="@id/tv_desc"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
