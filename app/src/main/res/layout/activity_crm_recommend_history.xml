<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/ll_container"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_white_layout">

        <ImageView
            android:id="@+id/iv_back"
            style="@style/header_layout_left"
            android:src="@drawable/nav_return" />

        <TextView
            android:id="@+id/tv_title"
            style="@style/header_layout_mid"
            android:text="历史推荐清单"
            android:textColor="#FF292933" />

        <TextView
            android:id="@+id/tv_right"
            android:text=""
            android:visibility="visible"
            style="@style/header_layout_right_text"
            android:textColor="@color/color_676763" />

    </RelativeLayout>

    <com.ybm.app.view.CommonRecyclerView
        android:id="@+id/crl_commend"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:descendantFocusability="blocksDescendants" />

</LinearLayout>