<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <!--android:id="@+id/ll_title" id不要随便换BaseActivity会用到-->
        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="0dp"
            android:layout_height="@dimen/dimen_dp_35"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dimen_dp_4"
            tools:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/title_left_search"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:paddingStart="15dp"
                android:paddingEnd="10dp"
                android:src="@drawable/ic_back" />

            <RelativeLayout
                android:id="@+id/rel_search"
                android:layout_width="0dp"
                android:layout_height="@dimen/dimen_dp_34"
                android:layout_weight="1"
                android:background="@drawable/search_round_corner_gray_bg_03"
                android:focusable="true">

                <ImageView
                    android:id="@+id/iv_a_magnifying_glass"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="6dp"
                    android:src="@drawable/icon_home_steady_scan" />

                <View
                    android:id="@+id/v_splite_line"
                    android:layout_width="1dp"
                    android:layout_height="18dp"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="2dp"
                    android:layout_toRightOf="@id/iv_a_magnifying_glass"
                    android:background="@color/gray_C4C4C4" />

                <EditText
                    android:id="@+id/title_et"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="3dp"
                    android:layout_toLeftOf="@id/iv_clear"
                    android:layout_toRightOf="@id/v_splite_line"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:ellipsize="end"
                    android:hint="@string/search_hint2"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_292933"
                    android:textColorHint="@color/color_9494A6"
                    android:textCursorDrawable="@drawable/color_cursor"
                    android:textSize="13dp" />

                <View
                    android:id="@+id/titleEtMask"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    android:layout_marginLeft="3dp"
                    android:layout_toLeftOf="@id/iv_clear"
                    android:layout_toRightOf="@id/v_splite_line" />

                <ImageView
                    android:id="@+id/iv_clear"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:layout_marginStart="@dimen/dimen_dp_10"
                    android:src="@drawable/icon_home_steady_voice"
                    android:visibility="visible" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="54dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/title_right_btn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:gravity="center"
                    android:textSize="@dimen/dimen_dp_15"
                    android:text="搜索"
                    android:textColor="@color/text_292933" />

            </RelativeLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/rl_title_from_often"
            app:layout_constraintTop_toBottomOf="@+id/ll_title"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            style="@style/header_layout">

            <ImageView
                android:id="@+id/iv_back"
                android:src="@drawable/ic_back"
                style="@style/header_layout_left" />

            <TextView
                android:id="@+id/tv_title"
                android:text=""
                style="@style/header_layout_mid" />

            <TextView
                android:id="@+id/tv_right"
                style="@style/header_layout_right_text"  />
            <ImageView
                android:id="@+id/iv_right"
                style="@style/header_layout_right_img" />
        </RelativeLayout>

        <!--  搜索结果页-->
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/brand_ctl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rl_title_from_often"
            tools:visibility="visible">

            <!-- 搜索筛选条目 -->
            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                app:elevation="0dp">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|enterAlways"
                    android:orientation="vertical">
                    <!--筛选-->
                    <RadioGroup
                        android:id="@+id/brand_rg_01"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/base_header_default_bg"
                        android:orientation="horizontal">


                        <TextView
                            android:id="@+id/tv_default"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawableRight="@null"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="默认"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <TextView
                            android:id="@+id/tv_synthesize"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="15dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:button="@null"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="排序"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_brand_rg_01_new"
                            style="@style/simple_filter_btn_item2"
                            android:layout_width="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_margin="@dimen/dimen_dp_0"
                            android:layout_weight="0"
                            android:gravity="center"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="最新"
                            android:textSize="@dimen/brand_rb"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tv_specification"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="规格" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_manufacturer"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="厂家"
                            android:visibility="visible" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_shopStore"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="商家" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/rb_all_category"
                            style="@style/drug_list_tv_05"
                            android:layout_width="wrap_content"
                            android:layout_marginLeft="0dp"
                            android:layout_marginRight="0dp"
                            android:layout_weight="0"
                            android:drawablePadding="2dp"
                            android:maxLength="6"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="分类" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1" />

                        <TextView
                            android:id="@+id/tv_shop"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginLeft="0dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="15dp"
                            android:layout_marginBottom="4dp"
                            android:layout_weight="0"
                            android:drawableRight="@drawable/selector_filter_icon_green"
                            android:drawablePadding="2dp"
                            android:gravity="center"
                            android:paddingLeft="6dp"
                            android:paddingRight="6dp"
                            android:text="@string/brand_manufacturers_filtrate"
                            android:textColor="@color/product_category_selector_01"
                            android:textSize="@dimen/brand_rb" />

                    </RadioGroup>

                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="none">
                        <RadioGroup
                            android:id="@+id/brand_rg_02"
                            android:layout_width="match_parent"
                            android:layout_height="36dp"
                            android:background="@drawable/bg_brand_rb_01"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingLeft="@dimen/dimen_dp_5"
                            android:paddingEnd="@dimen/dimen_dp_10"
                            android:visibility="visible">

                            <TextView
                                android:id="@+id/rb_chinese_medicine"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="visible"
                                android:text="仅看中药" />

                            <TextView
                                android:id="@+id/rb_spell_group"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="gone"
                                android:text="拼团" />

                            <TextView
                                android:id="@+id/rb_spell_group_and_pgby"
                                style="@style/simple_filter_btn_item3"
                                android:text="拼团包邮" />

                            <TextView
                                android:id="@+id/rb_self_support"
                                style="@style/simple_filter_btn_item3"
                                android:text="自营" />

                            <TextView
                                android:id="@+id/rb_same_province"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="gone"
                                android:text="同省" />

                            <TextView
                                android:id="@+id/rb_can_use_coupon"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="visible"
                                android:text="凑单" />

                            <TextView
                                android:id="@+id/rb_Gross"
                                style="@style/simple_filter_btn_item3"
                                android:text="优选" />

                            <TextView
                                android:id="@+id/rb_express"
                                style="@style/simple_filter_btn_item3"
                                android:text="京东/顺丰" />

                            <TextView
                                android:id="@+id/rb_available"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="gone"
                                android:text="有货" />

                            <TextView
                                android:id="@+id/rb_dpby"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="gone"
                                android:text="单品包邮" />

                            <TextView
                                android:id="@+id/rb_promotion"
                                style="@style/simple_filter_btn_item3"
                                android:visibility="gone"
                                android:text="促销" />
                        </RadioGroup>
                    </HorizontalScrollView>

                    <!-- 动态标签 -->
                    <com.ybmmarket20.view.searchFilter.view.SearchDynamicLabelView
                        android:id="@+id/dynamicLabel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:layout_marginTop="@dimen/dimen_dp_5"
                        android:layout_marginBottom="@dimen/dimen_dp_5"
                        android:layout_marginStart="@dimen/dimen_dp_15"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:orientation="horizontal"
                        tools:listitem="@layout/item_dynamic_label_horizontal_linear" />
                    <com.ybmmarket20.common.widget.RoundConstraintLayout
                        android:id="@+id/ll_red_pack_rebate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        tools:visibility="visible"
                        android:visibility="gone"
                        app:rv_backgroundColor="#FF2222"
                        app:rv_cornerRadius="4dp">

                        <ImageView
                            android:id="@+id/iv_red_pack"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:src="@drawable/icon_red_packet_rebate"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_tips"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="6dp"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tv_go_top_up"
                            app:layout_constraintStart_toEndOf="@id/iv_red_pack"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="下单返红包，最高返" />

                        <com.ybmmarket20.common.widget.RoundTextView
                            android:id="@+id/tv_go_top_up"
                            android:layout_width="wrap_content"
                            android:paddingStart="6dp"
                            android:paddingEnd="6dp"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            android:layout_marginEnd="5dp"
                            android:text="去看看 > "
                            android:gravity="center"
                            android:textColor="#FF2323"
                            android:textSize="12sp"
                            android:layout_marginTop="5dp"
                            android:layout_marginBottom="5dp"
                            app:rv_backgroundColor="@color/white"
                            app:rv_cornerRadius="4dp" />

                    </com.ybmmarket20.common.widget.RoundConstraintLayout>

                </LinearLayout>

                <!--  活动搜索提示 -->
                <CheckBox
                    android:id="@+id/cb_activity_tag"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/bg_promotion_selector"
                    android:button="@null"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/str_search_tag_tips"
                    android:textColor="@color/color_FF2121"
                    android:textSize="13dp"
                    android:visibility="gone"
                    tools:text="以下是 【319年货节】活动商品 取消"
                    tools:visibility="visible" />

                <!--  专区搜索提示 -->
                <TextView
                    android:id="@+id/tv_tag_tips"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="10dp"
                    android:background="#10FF2121"
                    android:gravity="center"
                    android:text="@string/str_search_tag_tips"
                    android:textColor="@color/color_FF2121"
                    android:textSize="13dp"
                    tools:text="为您显示【战略专区】内商品">

                </TextView>

            </com.google.android.material.appbar.AppBarLayout>

            <!-- 这个要在搜索记录之前，否则当显示搜索记录SearchGuidKeyLayout这个控件容易把搜索记录布局挡住一部分-->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <!--   为获取到搜索结果，展示为你推荐的头-->
                <LinearLayout
                    android:id="@+id/search_guid_layout_by_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/dimen_dp_15"
                    android:paddingTop="@dimen/dimen_dp_10"
                    android:paddingEnd="@dimen/dimen_dp_15"
                    android:paddingBottom="@dimen/dimen_dp_8"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/tv_hightlight_keyword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#676773"
                        android:textSize="@dimen/dimen_dp_13"
                        tools:text="抱歉，没有找到商品，为您推荐“阿莫西林”下搜索结果" />

                    <LinearLayout
                        android:id="@+id/ll_hotkey"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_10"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_hot_keyword_list_head"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dimen_dp_3"
                            android:text="@string/search_try_with_symbol" />

                        <com.ybmmarket20.view.taggroupview.TagContainerLayout
                            android:id="@+id/crv_hot_keyword"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="0dp"
                            android:background="@android:color/white"
                            app:horizontal_interval="8dp"
                            app:tag_background_color="#F7F7F8"
                            app:tag_clickable="true"
                            app:tag_corner_radius="2dp"
                            app:tag_enable_cross="false"
                            app:tag_text_color="#292933"
                            app:tag_text_size="@dimen/dimen_dp_12"
                            app:vertical_interval="8dp" />
                    </LinearLayout>
                    <!--   专区搜索无结果页面展示             -->
                    <include
                        android:id="@+id/layout_tag_no_more"
                        layout="@layout/layout_search_tag_no_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="52dp"
                        android:visibility="gone"
                        tools:visibility="visible" />
                    <!--兜底-->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_recommend_header"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_20"
                        android:layout_marginTop="@dimen/dimen_dp_30">

                        <ImageView
                            android:id="@+id/iv_search_head_left"
                            android:layout_width="@dimen/dimen_dp_18"
                            android:layout_height="@dimen/dimen_dp_10"
                            android:src="@drawable/icon_search_head_left"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/tv_search_head_text"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_search_head_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dimen_dp_5"
                            android:layout_marginEnd="@dimen/dimen_dp_5"
                            android:text="@string/recommondforyou"
                            android:textColor="@color/color_676773"
                            android:textSize="@dimen/dimen_dp_14"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@+id/iv_search_head_right"
                            app:layout_constraintStart_toEndOf="@+id/iv_search_head_left"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:id="@+id/iv_search_head_right"
                            android:layout_width="@dimen/dimen_dp_18"
                            android:layout_height="@dimen/dimen_dp_10"
                            android:src="@drawable/icon_search_head_right"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/tv_search_head_text"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <LinearLayout
                        android:id="@+id/llFromOftenBuy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:orientation="vertical">
                        <include layout="@layout/item_often_buy_empty" />
                        <include layout="@layout/item_often_buy_empty" />
                    </LinearLayout>
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/search_product_list_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/search_guid_layout_by_layout"
                    android:background="@color/activity_bg"
                    android:descendantFocusability="blocksDescendants"
                    android:layout_marginTop="-1dp" />

            </RelativeLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <!-- 搜索启动页-->
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/cl_before_search_result"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_title"
            tools:visibility="gone">

            <!--历史搜索记录 + 推荐搜索 -->
            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                app:elevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <com.ybmmarket20.common.widget.RoundConstraintLayout
                        android:id="@+id/ll_red_pack_rebate_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="10dp"
                        tools:visibility="visible"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/ll_title"
                        app:rv_backgroundColor="#FF2222"
                        app:rv_cornerRadius="4dp">

                        <ImageView
                            android:id="@+id/iv_red_pack_history"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="6dp"
                            android:src="@drawable/icon_red_packet_rebate"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_tips_history"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="6dp"
                            android:textColor="@color/white"
                            android:textSize="13sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/tv_go_top_up_history"
                            app:layout_constraintStart_toEndOf="@id/iv_red_pack_history"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="下单返红包，最高返" />

                        <com.ybmmarket20.common.widget.RoundTextView
                            android:id="@+id/tv_go_top_up_history"
                            android:layout_width="wrap_content"
                            android:paddingStart="6dp"
                            android:paddingEnd="6dp"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            android:layout_marginEnd="5dp"
                            android:text="去看看 > "
                            android:gravity="center"
                            android:textColor="#FF2323"
                            android:textSize="12sp"
                            android:layout_marginTop="5dp"
                            android:layout_marginBottom="5dp"
                            app:rv_backgroundColor="@color/white"
                            app:rv_cornerRadius="4dp" />

                    </com.ybmmarket20.common.widget.RoundConstraintLayout>

                    <!--  历史搜索-->
                    <LinearLayout
                        android:id="@+id/ll_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dimen_dp_5"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center_vertical"
                                android:paddingLeft="@dimen/dimen_dp_10"
                                android:text="历史搜索"
                                android:textColor="@color/color_history_search"
                                android:textSize="15sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:layout_gravity="bottom"
                                android:src="@drawable/icon_clean" />

                            <TextView
                                android:id="@+id/tv_history_clear"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="bottom"
                                android:layout_marginRight="14dp"
                                android:gravity="center"
                                android:paddingLeft="6dp"
                                android:paddingRight="6dp"
                                android:text="@string/del_empty"
                                android:textColor="#9494A6"
                                android:textSize="@dimen/dimen_dp_12" />
                        </LinearLayout>

                        <com.ybmmarketkotlin.views.FlexBoxLayoutMaxLines
                            android:id="@+id/flex_box_history"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dimen_dp_10"
                            android:layout_marginRight="@dimen/dimen_dp_10"
                            android:background="@color/white" />

                    </LinearLayout>

                    <!--  搜索发现-->
                    <com.ybmmarket20.view.SearchFindView
                        android:id="@+id/searchFindView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone" />

                    <!--  推荐搜索-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_dp_20"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:maxLines="1"
                            android:paddingLeft="@dimen/dimen_dp_10"
                            android:text="精选搜索"
                            android:textColor="@color/color_history_search"
                            android:textSize="15sp"
                            android:textStyle="bold" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_recommed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dimen_dp_12" />

                    </LinearLayout>

                    <!--  拼团主推品资源位-->
<!--                    <androidx.recyclerview.widget.RecyclerView-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginTop="@dimen/dimen_dp_20"-->
<!--                        android:orientation="vertical"-->
<!--                        android:layout_marginStart="@dimen/dimen_dp_10"-->
<!--                        android:layout_marginEnd="@dimen/dimen_dp_10"-->
<!--                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"-->
<!--                        tools:itemCount="6"-->
<!--                        tools:listitem="@layout/item_search_spell_group"-->
<!--                        app:spanCount="3" />-->
<!--                    <com.ybmmarket20.view.SearchSpellGroupView-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content" />-->
                    <com.ybmmarket20.view.cms.SearchStartRecommendView
                        android:id="@+id/search_start_recommend_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dimen_dp_10"
                        android:layout_marginEnd="@dimen/dimen_dp_10"
                        android:visibility="gone"
                        android:layout_marginBottom="@dimen/dimen_dp_10" />


                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--悬浮购物车-->
    <RelativeLayout
        android:id="@+id/rl_cart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/iv_fast_scroll_search"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginBottom="10dp"
        android:visibility="invisible"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_cart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/icon_float_car"
            android:layout_centerInParent="true" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="37dp"
            android:layout_marginLeft="37dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:background="@drawable/bg_message"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            tools:visibility="visible" />

    </RelativeLayout>

    <!--置顶按钮-->
    <com.ybmmarket20.view.MyFastScrollViewKt
        android:id="@+id/iv_fast_scroll_search"
        android:layout_width="53dp"
        android:layout_height="53dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginRight="9dp"
        android:layout_marginBottom="60dp" />

</RelativeLayout>