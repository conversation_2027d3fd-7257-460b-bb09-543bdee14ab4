<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="40dp"
            android:layout_height="@dimen/dimen_dp_30"
            android:layout_gravity="center"
            android:paddingTop="@dimen/dimen_dp_5"
            android:paddingBottom="@dimen/dimen_dp_5"
            android:src="@drawable/ic_back" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_28"
            android:layout_marginEnd="@dimen/dimen_dp_15">

            <EditText
                android:id="@+id/etSearch"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_28"
                android:layout_centerVertical="true"
                android:layout_marginStart="3dp"
                android:background="@drawable/search_round_corner_gray_bg_04"
                android:hint="搜索活动商品 "
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:paddingStart="@dimen/dimen_dp_11"
                android:singleLine="true"
                android:textColor="@color/color_292933"
                android:textColorHint="@color/color_9494A6"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="@dimen/dimen_dp_13"
                android:paddingEnd="@dimen/dimen_dp_15"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivEtClear"
                android:layout_width="@dimen/dimen_dp_20"
                android:layout_height="@dimen/dimen_dp_20"
                android:src="@drawable/clear_sousou"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_gift_tips"
        app:layout_constraintTop_toBottomOf="@+id/ll_title">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_F7F7F8"
            android:fadingEdge="none"
            app:elevation="@dimen/dimen_dp_0">

            <com.ybmmarket20.common.widget.RoundConstraintLayout
                android:id="@+id/cl_gift_select_head"
                android:layout_width="match_parent"
                android:layout_height="96dp"
                android:layout_marginTop="4dp"
                app:rv_backgroundColor="@color/white"
                app:rv_cornerRadius="9dp"
                android:layout_marginHorizontal="8dp"
                android:layout_marginBottom="8dp"
                app:layout_scrollFlags="scroll">


                <TextView
                    android:id="@+id/tv_sub_title"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:textSize="14dp"
                    android:text="以下商品可参加满赠活动"
                    android:textColor="@color/color_111111"
                    android:layout_width="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="13dp"
                    android:layout_height="wrap_content"/>


                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_gift_activity_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_sub_title"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="14dp"
                    tools:text="满10件送1件赠品，满20件送3件赠品"
                    android:paddingHorizontal="10dp"
                    android:textColor="@color/color_FA2C19"
                    android:gravity="center"
                    android:textSize="13dp"
                    app:rv_cornerRadius="7dp"
                    app:rv_backgroundColor="@color/color_14FA2C19"
                    android:layout_width="0dp"
                    android:minHeight="39dp"
                    android:layout_height="wrap_content"/>

            </com.ybmmarket20.common.widget.RoundConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

                <!--筛选-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/brand_rg_01"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:background="@drawable/base_header_default_bg">

                    <TextView
                        android:id="@+id/tv_synthesize"
                        style="@style/drug_list_tv_05"
                        android:layout_width="wrap_content"
                        android:button="@null"
                        android:layout_marginHorizontal="0dp"
                        android:drawablePadding="2dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tv_specification"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="排序"
                        tools:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_specification"
                        style="@style/drug_list_tv_05"
                        android:layout_width="wrap_content"
                        android:drawablePadding="2dp"
                        android:layout_marginHorizontal="0dp"
                        app:layout_constraintStart_toEndOf="@id/tv_synthesize"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/tv_manufacturer"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="规格" />

                    <TextView
                        android:id="@+id/tv_manufacturer"
                        style="@style/drug_list_tv_05"
                        android:layout_width="wrap_content"
                        android:layout_marginHorizontal="0dp"
                        app:layout_constraintStart_toEndOf="@id/tv_specification"
                        app:layout_constraintEnd_toStartOf="@id/rb_all_category"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:drawablePadding="2dp"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="厂家"
                        android:visibility="visible" />


                    <TextView
                        android:id="@+id/rb_all_category"
                        style="@style/drug_list_tv_05"
                        android:layout_width="wrap_content"
                        android:drawablePadding="2dp"
                        android:layout_marginHorizontal="0dp"
                        app:layout_constraintStart_toEndOf="@id/tv_manufacturer"
                        app:layout_constraintEnd_toStartOf="@id/tv_shop"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:maxLength="6"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="分类" />


                    <TextView
                        android:id="@+id/tv_shop"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        app:layout_constraintStart_toEndOf="@id/rb_all_category"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:drawableRight="@drawable/selector_filter_icon_green"
                        android:drawablePadding="2dp"
                        android:gravity="center"
                        android:paddingLeft="6dp"
                        android:paddingRight="6dp"
                        android:text="@string/brand_manufacturers_filtrate"
                        android:textColor="@color/product_category_selector_01"
                        android:textSize="@dimen/brand_rb" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_gift"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            app:layout_scrollFlags="scroll|enterAlways" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_gift_tips"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toTopOf="@id/clBottom"
        android:paddingHorizontal="9dp"
        tools:text="满10件送1件赠品，满20件送3件赠品"
        android:textColor="@color/color_FA2C19"
        android:textSize="12dp"
        android:visibility="gone"
        app:rv_backgroundColor="@color/color_14FA2C19"
        android:layout_width="0dp"
        android:layout_height="32dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:minHeight="49dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dimen_dp_20"
            android:layout_marginTop="10dp"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_16"
            android:layout_marginBottom="6dp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tv_gift_total"
            tools:text="小计：¥213.00" />

        <TextView
            android:id="@+id/tv_gift_total"
            android:layout_width="0dp"
            app:layout_constraintStart_toStartOf="@id/tv_amount"
            app:layout_constraintEnd_toEndOf="@id/tv_amount"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="3dp"
            tools:text="共10件商品"
            android:textSize="13dp"
            android:textColor="@color/text_color_333333"
            android:layout_height="wrap_content"/>


        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tvToCart"
            android:layout_width="@dimen/dimen_dp_107"
            android:layout_height="@dimen/dimen_dp_37"
            android:gravity="center"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="3dp"
            app:rv_backgroundColor="@color/color_00B955"
            app:rv_cornerRadius="5dp"
            android:text="@string/go_to_cart"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>