<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_50"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_f7f7f8">

    <TextView
        android:id="@+id/tvRefundReason"
        style="@style/RejectRefundText"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="商家退款原因错误"/>

    <RadioButton
        android:id="@+id/rbRefundReason"
        android:layout_width="@dimen/dimen_dp_18"
        android:layout_height="@dimen/dimen_dp_18"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@drawable/selector_refund_reason_radio_button"
        android:button="@null"
        android:layout_marginEnd="@dimen/dimen_dp_10" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:background="@color/colors_DDDDDD"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10" />

</androidx.constraintlayout.widget.ConstraintLayout>