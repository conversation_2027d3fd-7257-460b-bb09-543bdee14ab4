<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:layout_weight="6">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_f7f7f8"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="60dp">

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="优惠"
                android:textColor="@color/color_292933"
                android:textSize="16sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/product_detail_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="12dp"
                android:src="@drawable/icon_detail_service_close" />
        </RelativeLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rl_tags"
            android:layout_width="match_parent"
            android:overScrollMode="never"
            android:layout_height="wrap_content"
            tools:itemCount="2" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rl_coupons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:itemCount="2" />

    </LinearLayout>


</LinearLayout>