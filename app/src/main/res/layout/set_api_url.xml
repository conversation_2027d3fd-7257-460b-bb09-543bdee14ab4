<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="Prod"
            android:textSize="11sp" />

        <Button
            android:id="@+id/btn_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="Test"
            android:textSize="11sp" />

        <Button
            android:id="@+id/btn_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:visibility="gone"
            android:text="预上线"
            android:textSize="11sp" />

        <Button
            android:id="@+id/btn_4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="Dev"
            android:textSize="11sp" />

        <Button
            android:id="@+id/btn_5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="Stage"
            android:textSize="11sp" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="4dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_api"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:text="API域名:"
            android:textColor="@color/wish_tv1"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/et_api"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_toRightOf="@id/tv_api"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="接口域名"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_centerInParent="true"
        android:background="@color/main_line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_cdn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:text="CDN域名:"
            android:textColor="@color/wish_tv1"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/et_cdn"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/tv_cdn"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="图片文件域名"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_centerInParent="true"
        android:background="@color/main_line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_static"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:text="静态域名:"
            android:textColor="@color/wish_tv1"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/et_static"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/tv_static"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="静态资源域名"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="12sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_centerInParent="true"
        android:background="@color/main_line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <TextView
            android:id="@+id/tv_path"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:text="API项目:"
            android:textColor="@color/wish_tv1"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/et_path"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@id/tv_path"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="接口项目名"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/text_292933"
            android:textCursorDrawable="@drawable/color_cursor"
            android:textSize="12sp" />
    </RelativeLayout>
</LinearLayout>