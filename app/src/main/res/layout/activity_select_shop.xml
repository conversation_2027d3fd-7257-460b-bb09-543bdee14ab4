<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/nv"
        layout="@layout/common_header_items" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0"
        app:layout_constraintVertical_weight="1"
        android:layout_marginBottom="@dimen/dimen_dp_5"
        app:layout_constraintTop_toBottomOf="@+id/nv"
        app:layout_constraintBottom_toTopOf="@+id/tvQuiteSingle" />

    <TextView
        android:id="@+id/tvQuiteSingle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="退出"
        android:gravity="center"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_quite"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>