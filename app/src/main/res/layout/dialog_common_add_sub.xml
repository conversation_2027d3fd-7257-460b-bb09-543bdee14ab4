<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="185dp"
              android:background="@drawable/bg_common_dialog_add_sub"
              android:divider="@drawable/divider_line_w_1px"
              android:orientation="vertical"
              android:showDividers="middle">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:gravity="center"
        android:text="编辑购买数量"
        android:textStyle="bold"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/common_add_sub_tv01"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="79dp"
        android:gravity="center">

        <include layout="@layout/product_dialog_edit_layout" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:divider="@drawable/divider_line_dialog_w_1px"
        android:orientation="horizontal"
        android:showDividers="middle">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color_292933"
            android:textSize="17sp"/>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="确认"
            android:textColor="@color/base_colors_new"
            android:textSize="17sp"
            android:textStyle="bold"/>

    </LinearLayout>

</LinearLayout>