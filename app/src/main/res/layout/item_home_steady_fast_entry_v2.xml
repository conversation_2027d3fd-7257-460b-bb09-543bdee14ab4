<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_item_fast_entry"
        android:layout_width="@dimen/dimen_dp_45"
        android:layout_height="@dimen/dimen_dp_45"
        android:layout_marginTop="@dimen/dimen_dp_8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:src="@drawable/icon_home_steady_fast_entry_placehold"/>

    <TextView
        android:id="@+id/tv_item_fast_entry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintEnd_toEndOf="@+id/iv_item_fast_entry"
        app:layout_constraintStart_toStartOf="@+id/iv_item_fast_entry"
        tools:text="领券中心"
        app:layout_constraintTop_toBottomOf="@+id/iv_item_fast_entry" />


</androidx.constraintlayout.widget.ConstraintLayout>