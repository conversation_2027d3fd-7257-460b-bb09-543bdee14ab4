<?xml version="1.0" encoding="utf-8"?><!--优惠券新的样式 店铺券样式 VoucherAvailableFragment和VoucherAvailableForShopFragment复用-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dimen_dp_10"

    android:paddingEnd="@dimen/dimen_dp_12">

    <CheckBox
        android:id="@+id/voucher_check"
        style="@style/voucher_check_box_theme2"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:clickable="false"
        android:duplicateParentState="true"
        android:gravity="center"
        android:visibility="visible"
        android:layout_marginStart="@dimen/dimen_dp_12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_coupon_bg_left"
        android:layout_width="@dimen/dimen_dp_90"
        android:layout_height="@dimen/dimen_dp_120"
        android:layout_marginStart="12.5dp"
        android:background="@drawable/shape_item_goods_coupon_left"
        app:layout_constraintStart_toEndOf="@+id/voucher_check"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_coupon_amount"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginTop="@dimen/dimen_dp_20"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_PriceUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥"
                android:textColor="@color/color_ff4244"
                android:textSize="@dimen/dimen_dp_15"
                tools:text="¥" />

            <TextView
                android:id="@+id/tv_coupon_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_ff4244"
                android:textSize="@dimen/dimen_dp_32"
                android:textStyle="bold"
                tools:text="2000" />

            <TextView
                android:id="@+id/tv_discount_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="折"
                android:textColor="@color/color_ff4244"
                android:textSize="@dimen/dimen_dp_15"
                android:visibility="gone"
                tools:text="折" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_coupon_full_reduce"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_5"
            android:singleLine="true"
            android:textColor="@color/color_ff4244"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ll_coupon_amount"
            tools:text="满200减20" />

        <TextView
            android:id="@+id/tv_coupon_full_reduce_max"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_2"
            android:singleLine="true"
            android:textColor="@color/color_ff4244"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_coupon_full_reduce"
            tools:text="最高可减80" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_goods_coupon_bg_right"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_dp_120"
        android:background="@drawable/shape_item_goods_coupon_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/cl_goods_coupon_bg_left"
        app:layout_constraintTop_toTopOf="@+id/cl_goods_coupon_bg_left">

        <TextView
            android:id="@+id/tv_coupon_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_12"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_14"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginTop="@dimen/dimen_dp_9"
            tools:text="店铺名" />

        <TextView
            android:id="@+id/tv_coupon_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:maxLength="10"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/dimen_dp_14"
            app:layout_constraintStart_toStartOf="@+id/tv_coupon_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_coupon_title"
            tools:text="券名称券名称券名称券名称1234567" />

        <TextView
            android:id="@+id/tv_coupon_limit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:textColor="@color/text_676773"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="@+id/tv_coupon_subtitle"
            app:layout_constraintTop_toBottomOf="@+id/tv_coupon_subtitle"
            tools:text="全店商品可用" />

        <TextView
            android:id="@+id/tv_coupon_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginTop="@dimen/dimen_dp_12"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_limit"
            tools:text="2019/11/11-2019/12/11" />

        <View
            android:id="@+id/coupon_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_2"
            android:layout_marginTop="@dimen/dimen_dp_119"
            android:background="@drawable/shape_coupon_imaginary_line"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="@dimen/dimen_dp_12"
        android:layout_height="6dp"
        android:background="@drawable/shape_item_goods_coupon_top_half"
        app:layout_constraintEnd_toStartOf="@+id/cl_goods_coupon_bg_right"
        app:layout_constraintStart_toStartOf="@+id/cl_goods_coupon_bg_right"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_half_circle"
        android:layout_width="@dimen/dimen_dp_12"
        android:layout_height="6dp"
        android:background="@drawable/shape_item_goods_coupon_bottom_half"
        app:layout_constraintBottom_toBottomOf="@+id/cl_goods_coupon_bg_left"
        app:layout_constraintEnd_toStartOf="@+id/cl_goods_coupon_bg_right"
        app:layout_constraintStart_toStartOf="@+id/cl_goods_coupon_bg_right" />

    <TextView
        android:id="@+id/tv_unUsed_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="不可用原因：所结算商品中没有该券可用商品"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:visibility="gone"
        tools:visibility="visible"
        android:drawableStart="@drawable/icon_unused_coupon_tip"
        app:layout_constraintTop_toBottomOf="@+id/cl_goods_coupon_bg_right"
        android:drawablePadding="@dimen/dimen_dp_5"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_10" />

</androidx.constraintlayout.widget.ConstraintLayout>