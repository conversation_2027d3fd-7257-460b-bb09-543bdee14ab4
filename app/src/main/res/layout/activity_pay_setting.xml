<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#F7F7F7">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPayPassword"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_60"
        android:paddingStart="@dimen/dimen_dp_15"
        android:background="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/header">

        <TextView
            android:id="@+id/tvPW"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="支付密码"
            android:textStyle="bold"
            android:textSize="@dimen/dimen_dp_14"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvPWDes"
            app:layout_constraintVertical_chainStyle="packed"
            android:textColor="#292933" />

        <TextView
            android:id="@+id/tvPWDes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="方便使用银行卡快捷支付"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="#9494A6"
            android:layout_marginTop="@dimen/dimen_dp_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPW" />

        <ImageView
            android:layout_width="@dimen/dimen_dp_5"
            android:layout_height="@dimen/dimen_dp_10"
            android:src="@drawable/icon_my_balance_arrow_right"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_15" />

        <TextView
            android:id="@+id/tvPWStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#9494A6"
            android:textSize="@dimen/dimen_dp_14"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_30"
            android:text="去设置" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clFingerprint"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_60"
        android:paddingStart="@dimen/dimen_dp_15"
        android:background="@color/white"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/clPayPassword">

        <TextView
            android:id="@+id/tvFingerprint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="指纹支付"
            android:textStyle="bold"
            android:textSize="@dimen/dimen_dp_14"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tvFingerprintDes"
            app:layout_constraintVertical_chainStyle="packed"
            android:textColor="#292933" />

        <TextView
            android:id="@+id/tvFingerprintDes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="使用银行卡支付时，可通过验证指纹快速完成付款"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="#9494A6"
            android:layout_marginTop="@dimen/dimen_dp_8"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFingerprint" />


        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switchFingerPrint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dimen_dp_16"
            android:thumb="@drawable/selector_switch_thumb"
            app:track="@drawable/selector_switch_track"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <View
            android:id="@+id/switchDelegate"
            android:layout_width="@dimen/dimen_dp_50"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_16" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>