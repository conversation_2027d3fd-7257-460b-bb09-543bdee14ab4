<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colors_fff7ef"
            android:paddingBottom="10dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:paddingTop="10dp"
            android:text="@string/address_tips"
            android:textColor="@color/colors_99664D"
            android:textSize="14sp" />

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/rv_addreRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>
</LinearLayout>