<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colors_f5f5f5"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_horizontal|bottom"
            android:paddingTop="15dp"
            android:text="订单总金额"
            android:textColor="@color/text_676773"
            android:textSize="17sp"
            tools:visibility="visible"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvTotal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="20dp"
            android:textColor="@color/color_292933"
            android:textSize="32sp"
            tools:text="1999"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPayDiscountTips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#FF7200"
            android:textSize="12sp"
            tools:visibility="visible"
            android:visibility="gone"
            android:layout_marginTop="6dp"
            tools:text="使用银行卡支付，已省5元" />
        
        <TextView
            android:id="@+id/tv_shopping_gold_tips"
            android:layout_width="match_parent"
            tools:text="充值成功返10元红包"
            android:textColor="@color/color_ff2121"
            android:textSize="13dp"
            tools:visibility="visible"
            android:visibility="gone"
            android:gravity="center"
            android:layout_marginTop="6dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tvTimer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="12dp"
            android:paddingBottom="20dp"
            android:textColor="@color/color_676763"
            android:textSize="14sp"
            tools:text="内完成支付" />

        <com.ybmmarketkotlin.views.virtualgold.PayVirtualGoldView
            android:id="@+id/payVirtualGoldView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginTop="@dimen/pabr_dimen10dp"
            android:layout_marginBottom="@dimen/pabr_dimen10dp"
            android:layout_marginHorizontal="@dimen/pabr_dimen10dp" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPayWay"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_0"
            android:overScrollMode="never"
            android:background="@color/colors_f5f5f5"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/tvPayWayTips"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_39"
            android:textColor="#99664D"
            android:textSize="@dimen/dimen_dp_12"
            android:gravity="center"
            android:background="#FFF7EF"
            android:paddingStart="@dimen/dimen_dp_10"
            android:paddingEnd="@dimen/dimen_dp_10"
            android:singleLine="true"
            tools:text="温馨提示：当前订单金额较大，建议您使用微信/支付宝进行支付" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_dp_1"
            android:background="#E9E9E9"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/btnPay"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:gravity="center"
            android:text="确认支付"
            android:textColor="@color/white"
            android:textSize="17sp"
            app:rv_backgroundColor="@color/base_color"
            app:rv_cornerRadius="2dp" />

    </LinearLayout>
</LinearLayout>