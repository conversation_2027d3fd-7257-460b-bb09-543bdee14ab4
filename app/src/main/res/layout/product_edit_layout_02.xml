<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_layout"
    android:layout_width="90dp"
    android:layout_height="wrap_content">

    <!--为了里面的TextView动画的起止位置在可控，所以这个地方加一层布局，删除慎重！-->
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="23dp"
        android:layout_centerInParent="true">

        <TextView
            android:id="@+id/tv_number"
            android:layout_width="60dp"
            android:layout_height="match_parent"
            android:background="@drawable/bg_product_edit_def_02"
            android:gravity="center"
            android:hint="0"
            android:imeOptions="actionGo"
            android:inputType="phone"
            android:minWidth="60dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:textColor="@color/coupon_limit_tv01"
            android:textSize="14sp" />
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_numSub"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:layout_centerVertical="true"
        android:src="@drawable/page_minus" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_numAdd"
        android:layout_width="23dp"
        android:layout_height="23dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/page_add" />
</RelativeLayout>
