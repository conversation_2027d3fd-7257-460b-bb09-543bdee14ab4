<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="3dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_recommend_shop"
        android:layout_width="@dimen/dimen_dp_31"
        android:layout_height="@dimen/dimen_dp_31"
        android:layout_marginTop="@dimen/dimen_dp_7_5"
        android:layout_marginStart="@dimen/dimen_dp_7"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_recommend_shop_name"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/dimen_dp_3_5"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:textSize="@dimen/dimen_dp_13"
        android:textColor="@color/color_292933"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_recommend_shop"
        app:layout_constraintTop_toTopOf="@+id/iv_recommend_shop"
        tools:text="武汉鑫弘森药业公司" />

    <com.ybmmarket20.view.ShopNameWithTagView
        android:id="@+id/snwt_recommend_shop_tag"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_14"
        android:layout_marginTop="@dimen/dimen_dp_1"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_recommend_shop_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_recommend_shop_name" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_recommend_shop_coupon"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_17"
        app:layout_constraintHorizontal_bias="0"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:layout_marginStart="@dimen/dimen_dp_3_5"
        app:layout_constraintStart_toEndOf="@+id/iv_recommend_shop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/snwt_recommend_shop_tag">

        <TextView
            android:id="@+id/tv_recommend_shop_coupon_content"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_17"
            app:layout_constraintHorizontal_bias="0"
            android:textSize="@dimen/dimen_dp_10"
            android:textColor="@color/color_ff2121"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:paddingStart="@dimen/dimen_dp_5"
            android:paddingEnd="@dimen/dimen_dp_5"
            tools:text="满500打9折/满700"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@drawable/icon_recommend_shop_coupon_home"
            android:layout_marginEnd="@dimen/dimen_dp_5" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_recommend_shop_promotion"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_17"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_3_5"
        app:layout_constraintStart_toEndOf="@+id/iv_recommend_shop"
        app:layout_constraintTop_toBottomOf="@+id/cl_recommend_shop_coupon">

        <TextView
            android:id="@+id/tv_recommend_shop_promotion_content"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_17"
            app:layout_constraintHorizontal_bias="0"
            android:background="@drawable/shape_recommend_shop_promotion_content"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dimen_dp_2"
            android:paddingEnd="@dimen/dimen_dp_2"
            android:singleLine="true"
            android:textColor="@color/color_ff2121"
            android:textSize="@dimen/dimen_dp_10"
            android:text="每满1000元/减100元"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_recommend_shop_delivery"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_dp_3_5"
        app:layout_constraintStart_toEndOf="@+id/iv_recommend_shop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cl_recommend_shop_promotion"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:layout_marginTop="@dimen/dimen_dp_6"
        android:textSize="@dimen/dimen_dp_11"
        android:singleLine="true"
        android:paddingEnd="@dimen/dimen_dp_2"
        android:textColor="@color/color_676773"
        android:text="100元起送，实付300元包邮"
        android:maxLines="1"
        android:ellipsize="end" />


</androidx.constraintlayout.widget.ConstraintLayout>