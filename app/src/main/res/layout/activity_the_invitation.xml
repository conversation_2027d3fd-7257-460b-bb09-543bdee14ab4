<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colors_FFC940">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_the_invitation_top_bg"
                android:gravity="right"
                android:minHeight="99dp"
                android:scaleType="fitXY">

                <TextView
                    android:id="@+id/tv_the_rules"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_the_invitation_the_rules"
                    android:drawablePadding="2dp"
                    android:drawableRight="@drawable/icon_the_invitation_the_rules"
                    android:paddingBottom="2dp"
                    android:paddingLeft="7dp"
                    android:paddingRight="3dp"
                    android:paddingTop="2dp"
                    android:text="规则"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_the_invitation_center_bg"
                android:gravity="center"
                android:minHeight="90dp"
                android:scaleType="fitXY">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="最低\n获得"
                    android:textColor="@color/colors_BE600D"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/tv_money_voucher"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="7dp"
                    android:layout_marginRight="7dp"
                    android:text="0"
                    android:textColor="@color/colors_FD570A"
                    android:textSize="60sp"
                    tools:text="4699" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|bottom"
                    android:layout_marginBottom="12dp"
                    android:text="元"
                    android:textColor="@color/colors_BE600D"
                    android:textSize="18sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/icon_the_invitation_bottom_bg"
                android:gravity="center"
                android:minHeight="160dp"
                android:scaleType="fitXY">

                <TextView
                    android:id="@+id/tv_money_voucher_friends"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="好友最低获得0元"
                    android:textColor="@color/white"
                    android:textSize="19sp"
                    tools:text="好友最低获得100元" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_immediately_invited"
                    android:layout_width="match_parent"
                    android:layout_height="49dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="38dp"
                    android:layout_marginRight="38dp"
                    android:background="@drawable/icon_the_invitation_btn_bg"
                    android:gravity="center"
                    android:text="立刻邀请"
                    android:textColor="@color/colors_FFDC8C"
                    android:textSize="18sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:minHeight="41dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_posters"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="30dp"
                        android:drawableLeft="@drawable/icon_the_invitation_posters"
                        android:drawablePadding="3dp"
                        android:drawableRight="@drawable/icon_the_invitation_right"
                        android:padding="5dp"
                        android:text="生成海报"
                        android:textColor="@color/colors_5A2A00"
                        android:textSize="15sp" />

                    <View
                        android:layout_width="1dp"
                        android:layout_height="17dp"
                        android:background="@color/colors_5A2A00" />

                    <TextView
                        android:id="@+id/tv_sms_invitation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="30dp"
                        android:drawableLeft="@drawable/icon_the_invitation_sms"
                        android:drawablePadding="3dp"
                        android:drawableRight="@drawable/icon_the_invitation_right"
                        android:padding="5dp"
                        android:text="短信邀请"
                        android:textColor="@color/colors_5A2A00"
                        android:textSize="15sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="15dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:background="@drawable/the_invitation_bg"
                    android:minHeight="149dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_my_bonus_pool"
                        android:layout_width="match_parent"
                        android:layout_height="44dp"
                        android:drawableRight="@drawable/icon_the_invitation_right_big"
                        android:gravity="center_vertical"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="我的奖励池"
                        android:textColor="@color/colors_924D00"
                        android:textSize="15sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="105dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/tv_accumulated_coupon"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="100" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="元"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="13sp" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="累计获得优惠券"
                                android:textColor="@color/colors_5A2A00"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="17dp"
                            android:layout_gravity="center"
                            android:background="@color/colors_5A2A00"
                            android:gravity="center" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/tv_successful_registration"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="2" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="人"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="13sp" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="成功注册"
                                android:textColor="@color/colors_5A2A00"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="17dp"
                            android:layout_gravity="center"
                            android:background="@color/colors_5A2A00"
                            android:gravity="center" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:id="@+id/tv_successful_order"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="1" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="人"
                                    android:textColor="@color/colors_5A2A00"
                                    android:textSize="13sp" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="7dp"
                                android:text="成功下单"
                                android:textColor="@color/colors_5A2A00"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>