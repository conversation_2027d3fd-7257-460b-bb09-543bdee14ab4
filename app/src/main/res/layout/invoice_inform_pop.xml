<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rl_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:paddingTop="6dp">

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:drawablePadding="8dp"
                android:drawableRight="@drawable/icon_tv_invoice_information"
                android:text="发票信息"
                android:textColor="@color/text_292933"
                android:textSize="16sp" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerInParent="true"
                android:paddingBottom="6dp"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="6dp"
                android:src="@drawable/icon_seckill_grey_close" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ll_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rl_layout"
            android:orientation="vertical">

            <com.ybmmarket20.view.MyScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_title_class"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:text="发票类型"
                            android:textColor="@color/text_292933"
                            android:textSize="@dimen/detail_tv_dimen_14sp" />

                        <RadioGroup
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:minHeight="40dp"
                            android:orientation="horizontal"
                            android:paddingLeft="10dp">

                            <RadioButton
                                android:id="@+id/rb_bill_elec"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:background="@drawable/selector_brand_pop_window_tv2"
                                android:button="@null"
                                android:maxLines="1"
                                android:paddingBottom="6dp"
                                android:paddingLeft="13dp"
                                android:paddingRight="13dp"
                                android:paddingTop="6dp"
                                android:singleLine="true"
                                android:text="电子普通发票"
                                android:textColor="@drawable/product_rb_selector_textcolor" />

                            <RadioButton
                                android:id="@+id/rb_bill_ordinary"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginLeft="10dp"
                                android:background="@drawable/selector_brand_pop_window_tv2"
                                android:button="@null"
                                android:maxLines="1"
                                android:paddingBottom="6dp"
                                android:paddingLeft="13dp"
                                android:paddingRight="13dp"
                                android:paddingTop="6dp"
                                android:singleLine="true"
                                android:text="纸质普通发票"
                                android:textColor="@drawable/product_rb_selector_textcolor" />

                        </RadioGroup>

                        <TextView
                            android:id="@+id/tv_zzs_text"
                            style="@style/invoice_information_tv3_style"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:text=""
                            android:textColor="@color/text_9494A6" />

                        <LinearLayout
                            android:id="@+id/ll_peer_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:minHeight="40dp"
                                android:paddingLeft="10dp">

                                <CheckBox
                                    android:id="@+id/cb"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:button="@drawable/invoice_inform_bg_selector" />

                                <TextView
                                    android:id="@+id/tv_explain"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center_vertical"
                                    android:layout_marginLeft="10dp"
                                    android:text="电子普通发票随货通行"
                                    android:textColor="@color/text_292933"
                                    android:textSize="14sp" />

                            </LinearLayout>

                            <TextView
                                android:id="@+id/tv_explain_02"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginRight="10dp"
                                android:minHeight="40dp"
                                android:text="勾选后，我们会将您的电子普通发票打印并随货为您寄出。" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="25dp"
                        android:orientation="vertical"
                        android:paddingLeft="10dp">

                        <TextView
                            android:id="@+id/tv_fp_title"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="发票抬头"
                            android:textColor="@color/text_292933"
                            android:textSize="@dimen/detail_tv_dimen_14sp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_fp_gs"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:background="@drawable/bg_invoice_inform_checked"
                                android:paddingBottom="5dp"
                                android:paddingLeft="27dp"
                                android:paddingRight="27dp"
                                android:paddingTop="5dp"
                                android:text="公司"
                                android:textColor="@color/white"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_fp_ydmc"
                                style="@style/invoice_information_tv3_style"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="药店名称:" />

                            <TextView
                                android:id="@+id/tv_fp_nsrsbh"
                                style="@style/invoice_information_tv3_style"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="纳税人识别号:" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_fp_memo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:text="" />

                    </LinearLayout>
                </LinearLayout>
            </com.ybmmarket20.view.MyScrollView>

            <TextView
                android:id="@+id/tv_btn_ok"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/base_colors"
                android:gravity="center"
                android:text="确定"
                android:textColor="@color/white"
                android:textSize="17sp" />
        </LinearLayout>

        <include
            layout="@layout/layout_load_error_reload"
            android:visibility="gone" />

    </RelativeLayout>

</LinearLayout>