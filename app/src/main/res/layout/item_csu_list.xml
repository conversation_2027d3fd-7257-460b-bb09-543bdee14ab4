<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="68dp"
    android:layout_margin="5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlRow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_back_corner_white_6dp">

        <ImageView
            android:id="@+id/sku_image"
            android:layout_width="58dp"
            android:layout_height="58dp"
            android:layout_marginStart="5dp"
            android:background="#fff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/sku_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:layout_marginStart="5dp"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textColor="#000"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@id/llEffect"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/sku_image"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="12131231231231231231121312312312312312311213123123123123123112131231231231231231" />

        <TextView
            android:id="@+id/tvGoodsCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="数量：1"
            android:textColor="@color/color_676773"
            android:textSize="11dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/sku_name"
            app:layout_constraintStart_toStartOf="@+id/sku_name" />

        <LinearLayout
            android:id="@+id/llEffect"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tvGoodsCount"
            app:layout_constraintTop_toBottomOf="@id/tvGoodsCount">

            <TextView
                android:id="@+id/sku_effect"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="@color/color_676773"
                android:textSize="11dp"
                tools:text="1111111" />

            <LinearLayout
                android:id="@+id/llZengPinPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/sku_effect"
                android:layout_alignBottom="@+id/sku_effect"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/dimen_dp_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvZengPinPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textColor="@color/text_color_333333"
                    android:textSize="@dimen/dimen_dp_12"
                    tools:text="￥0.01" />

                <ImageView
                    android:id="@+id/tvZengPinTip"
                    android:layout_width="@dimen/dimen_dp_12"
                    android:layout_height="@dimen/dimen_dp_12"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/dimen_dp_5"
                    android:src="@drawable/icon_zengpin_tip" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/rclMore"
        android:layout_width="@dimen/dimen_dp_29"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dimen_dp_6">

        <TextView
            android:id="@+id/tvMore"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_68"
            android:lineSpacingMultiplier="0.8"
            android:textSize="@dimen/dimen_dp_12"
            android:textColor="#3BC168"
            android:text="更多赠品"
            android:gravity="center"
            android:ems="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </com.ybmmarket20.common.widget.RoundConstraintLayout>
</RelativeLayout>