<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="6"
    android:background="@color/bgColor_alertview_alert"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:paddingTop="6dp"
            android:layout_height="38dp">

            <View
                style="@style/view_line_icon"
                android:layout_marginRight="11dp"
                android:layout_toLeftOf="@+id/tv" />

            <TextView
                android:id="@+id/tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="已经领取优惠券"
                android:textColor="#9494A6"
                android:textSize="13sp" />

            <View
                style="@style/view_line_icon"
                android:layout_marginLeft="11dp"
                android:layout_toRightOf="@+id/tv" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="60dp"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerInParent="true"
                android:paddingTop="6dp"
                android:paddingBottom="6dp"
                android:paddingRight="20dp"
                android:paddingLeft="20dp"
                android:src="@drawable/close" />
        </RelativeLayout>

        <com.ybm.app.view.CommonRecyclerView
            android:id="@+id/crv_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>