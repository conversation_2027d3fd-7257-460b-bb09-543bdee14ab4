<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_title"
        style="@style/header_layout">

        <ImageView
            android:id="@+id/ivBack"
            style="@style/header_layout_left"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            android:text=""
            style="@style/header_layout_mid" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvPW"
            style="@style/setPayPwText"
            app:layout_constraintTop_toTopOf="parent"
            android:text="请输入密码"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tvPWTips"
            style="@style/setPayPwTextTips"
            android:layout_marginStart="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@+id/tvPW"
            app:layout_constraintStart_toEndOf="@+id/tvPW"
            app:layout_constraintTop_toTopOf="@+id/tvPW"
            tools:text="密码过于简单，请避免输入相同或连续数字" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <EditText
        android:id="@+id/etInputPW"
        style="@style/setPayPwEdit"
        android:hint="请输入6位数字"
        android:maxLength="6"
        android:inputType="numberPassword"
        android:digits="1234567890" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvPWSecond"
            style="@style/setPayPwText"
            app:layout_constraintTop_toTopOf="parent"
            android:text="再次输入密码"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tvPWSecondTips"
            style="@style/setPayPwTextTips"
            android:layout_marginStart="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@+id/tvPWSecond"
            app:layout_constraintStart_toEndOf="@+id/tvPWSecond"
            app:layout_constraintTop_toTopOf="@+id/tvPWSecond"
            tools:text="两次密码输入不一致，请重新设置密码" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <EditText
        android:id="@+id/etInputPWSecond"
        style="@style/setPayPwEdit"
        android:hint="请输入6位数字"
        android:maxLength="6"
        android:inputType="numberPassword"
        android:digits="1234567890" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvCode"
            style="@style/setPayPwText"
            app:layout_constraintTop_toTopOf="parent"
            android:text="请输入验证码"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tvCodeTips"
            style="@style/setPayPwTextTips"
            android:textColor="#9494A6"
            android:layout_marginStart="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@+id/tvCode"
            app:layout_constraintStart_toEndOf="@+id/tvCode"
            app:layout_constraintTop_toTopOf="@+id/tvCode"
            tools:text="已向***5816发送验证码，注意查收" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <EditText
            android:id="@+id/etVerifyCode"
            style="@style/setPayPwEdit"
            android:hint="请输入验证码"
            android:maxLength="6"
            app:layout_constraintTop_toTopOf="parent"
            android:inputType="number"
            android:digits="1234567890"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tvCodeBtn"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginEnd="@dimen/dimen_dp_15"
            android:layout_marginTop="@dimen/dimen_dp_10"
            android:paddingEnd="@dimen/dimen_dp_30"
            android:text="获取验证码"
            android:textSize="@dimen/dimen_dp_14"
            android:gravity="center"
            android:textColor="#00B377"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvSendCodeTips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:textColor="#9494A6"
        android:textSize="@dimen/dimen_dp_11"
        android:lineHeight="@dimen/dimen_dp_15"
        tools:text="收不到验证码？\n
请确认186****4799是否能正常正使用，若该手机号已停用，请联系银行更换手机号，您还可以尝试：\n
1.确认短信是否被手机安全软件拦截或折叠隐藏；\n
2.查看手机网络状况是否良好，是否可正常接受其他号码短信。" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_0_5"
        android:background="#DEDEDE"
        android:layout_marginTop="@dimen/dimen_dp_20" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvSubmit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:layout_marginTop="@dimen/dimen_dp_8"
        android:text="确定"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_17"
        app:rv_backgroundColor="#37b377"
        app:rv_cornerRadius="@dimen/dimen_dp_6" />

</LinearLayout>