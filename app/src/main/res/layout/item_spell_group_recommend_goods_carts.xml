<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingBottom="15dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="60dp"
        android:layout_height="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_goods_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/invoice_tv_292933"
        android:singleLine="true"
        android:textStyle="bold"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:text="念慈菴 蜜炼川贝枇杷膏/500ml/盒"
        android:layout_marginStart="5.5dp"
        app:layout_constraintStart_toEndOf="@+id/iv_goods"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_goods" />

    <TextView
        android:id="@+id/tv_effect"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@color/color_676773"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:text="有效期：2025.10.16"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_title" />


    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:text="¥24.00/盒"
        android:includeFontPadding="false"
        android:textColor="@color/color_ff2121"
        android:textSize="20sp"
        app:layout_constraintStart_toStartOf="@+id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_effect" />

    <com.ybmmarket20.view.ProductEditLayoutSuiXinPin
        android:id="@+id/pel"
        android:layout_width="@dimen/dimen_dp_25"
        android:layout_height="@dimen/dimen_dp_25"
        android:layout_marginTop="@dimen/dimen_dp_3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_effect" />

    <ImageView
        android:id="@+id/iv_goods_tag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_15"
        android:scaleType="fitStart"
        app:layout_constraintStart_toStartOf="@+id/iv_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_goods" />

</androidx.constraintlayout.widget.ConstraintLayout>