<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/activity_bg"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_floor_index"
            android:layout_width="@dimen/dimen_dp_76"
            android:layout_height="match_parent" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llFilter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="horizontal">

                <RadioGroup
                    android:id="@+id/rg_property"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@color/white"
                    android:paddingLeft="@dimen/dimen_dp_15"
                    android:paddingTop="@dimen/dimen_dp_10"
                    android:paddingBottom="@dimen/dimen_dp_10">

                    <RadioButton
                        android:id="@+id/rb_01"
                        android:layout_width="83dp"
                        android:layout_height="@dimen/dimen_dp_24"
                        android:background="@drawable/bg_selecter_seach_spacfication"
                        android:button="@null"
                        android:gravity="center"
                        android:text="综合"
                        android:checked="true"
                        android:textColor="@color/selector_text_color_292933"
                        android:textSize="@dimen/dimen_dp_12" />

                    <RadioButton
                        android:id="@+id/rb_02"
                        android:layout_width="83dp"
                        android:layout_height="@dimen/dimen_dp_24"
                        android:layout_marginLeft="@dimen/dimen_dp_5"
                        android:background="@drawable/bg_selecter_seach_spacfication"
                        android:button="@null"
                        android:gravity="center"
                        android:text="销量"
                        android:textColor="@color/selector_text_color_292933"
                        android:textSize="@dimen/dimen_dp_12" />

                </RadioGroup>

                <View
                    android:layout_width="@dimen/dimen_dp_0"
                    android:layout_height="@dimen/dimen_dp_0"
                    android:layout_weight="1" />

                <CheckBox
                    android:id="@+id/cbCanUseCoupon"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dimen_dp_24"
                    android:layout_marginEnd="@dimen/dimen_dp_10"
                    android:textSize="@dimen/dimen_dp_12"
                    android:textColor="@color/color_676773"
                    android:layout_marginTop="@dimen/dimen_dp_10"
                    android:checked="true"
                    android:button="@drawable/payway_bg_selector"
                    android:background="@android:color/transparent"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:text=" 仅看可用券" />
            </LinearLayout>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/smartrefresh"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_goodslist"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        </LinearLayout>


    </LinearLayout>


</LinearLayout>