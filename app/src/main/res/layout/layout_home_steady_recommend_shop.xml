<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_title"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/shape_seckill_title"
        android:layout_height="@dimen/dimen_dp_35">

        <TextView
            android:id="@+id/tv_shop_moudle_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="人气好店"
            android:textSize="16sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textStyle="bold"
            android:textColor="@color/color_292933" />

        <TextView
            android:id="@+id/moreShop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="更多店铺"
            android:textSize="12sp"
            android:textColor="@color/color_676773"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/dimen_dp_3"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:drawableEnd="@drawable/icon_home_arrow_entry" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:itemCount="6"
        tools:spanCount="2"
        android:background="@color/white"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        tools:listitem="@layout/item_home_steady_recommend_shop"
        app:layoutManager="com.ybm.app.view.WrapGridLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/cl_title" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv"
        android:background="@drawable/shape_seckill_foot" />

</androidx.constraintlayout.widget.ConstraintLayout>