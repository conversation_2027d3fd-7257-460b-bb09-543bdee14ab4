<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/fl_home_steady"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_home_steady"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/color_f7f7f8"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <View
                android:id="@+id/status_bar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dimen_dp_1"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- 头部搜索   -->
            <com.ybmmarket20.view.homesteady.newpage.HomeSteadySearchView3
                android:id="@+id/search_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dimen_dp_7"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/status_bar" />

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="0dp"
                android:layout_height="32dp"
                app:tabGravity="fill"
                app:tabIndicatorColor="@android:color/transparent"
                app:tabMinWidth="0dp"
                app:tabMode="scrollable"
                app:tabPaddingEnd="6dp"
                app:tabPaddingStart="6dp"
                android:overScrollMode="never"
                app:tabRippleColor="@null"
                android:layout_marginStart="3dp"
                android:layout_marginTop="7dp"
                app:layout_constraintEnd_toStartOf="@id/barrier"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/search_view" />

<!--            渐变-->
<!--            <View-->
<!--                android:id="@+id/view_tab_gradient"-->
<!--                android:layout_width="3dp"-->
<!--                android:layout_height="0dp"-->
<!--                android:background="@drawable/bg_gradient_d8d8d8_eeeeee"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/tab_layout"-->
<!--                app:layout_constraintEnd_toStartOf="@id/ll_all_drug"-->
<!--                app:layout_constraintTop_toTopOf="@id/tab_layout" />-->

            <LinearLayout
                android:id="@+id/ll_all_drug"
                android:layout_width="90dp"
                android:layout_height="32dp"
                android:orientation="horizontal"
                app:layout_constraintTop_toTopOf="@id/tab_layout"
                app:layout_constraintBottom_toBottomOf="@id/tab_layout"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/iv_all_drug"
                    android:layout_width="13dp"
                    android:layout_height="13dp"
                    android:scaleType="centerInside"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="3dp"
                    android:src="@drawable/home_ic_all_drug" />

                <TextView
                    android:id="@+id/tv_all_drug"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="11dp"
                    android:text="@string/str_all_drug"
                    android:textColor="@color/black"
                    android:textSize="14dp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_icon_all_drug"
                android:layout_width="84dp"
                android:layout_height="27dp"
                android:scaleType="centerCrop"
                android:layout_marginEnd="8dp"
                app:layout_constraintTop_toTopOf="@id/tab_layout"
                app:layout_constraintBottom_toBottomOf="@id/tab_layout"
                app:layout_constraintEnd_toEndOf="parent">

            </ImageView>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                app:barrierDirection="left"
                app:constraint_referenced_ids="ll_all_drug,iv_icon_all_drug"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


            <ImageView
                android:id="@+id/iv_head_bg"
                android:layout_width="0dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:scaleType="fitXY"
                android:background="@color/colors_eeeeee"
                app:layout_constraintBottom_toTopOf="@id/viewpager"
                android:translationZ="-1dp"
                android:layout_height="0dp"/>

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewpager"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:overScrollMode="never"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tab_layout" />

            
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_dial_suspension"
                android:layout_width="wrap_content"
                android:layout_marginBottom="90dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="6dp"
                android:layout_height="wrap_content">
                
                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="15dp"
                    app:layout_constraintTop_toTopOf="parent"
                    android:src="@drawable/icon_dial_suspension_close"
                    app:layout_constraintBottom_toTopOf="@id/iv_dial_suspension"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_height="15dp"/>
                
                <com.ybmmarket20.view.DialImageView
                    android:id="@+id/iv_dial_suspension"
                    android:layout_width="114dp"
                    android:layout_height="76dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_close"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:src="@drawable/transparent"
                    android:visibility="gone"
                    tools:visibility="visible" />


            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>
