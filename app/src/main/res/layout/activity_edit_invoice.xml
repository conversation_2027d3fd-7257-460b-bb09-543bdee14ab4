<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/header_view"
        layout="@layout/common_header_items" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@id/tv_submit"
        app:layout_constraintTop_toBottomOf="@id/header_view">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include
                android:id="@+id/invoice_tips"
                layout="@layout/view_invoice_tips" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="12dp"
                android:paddingTop="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/invoice_tips">

                <TextView
                    android:id="@+id/tv_invoice_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="发票类型"
                    android:textColor="@color/color_222222"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/ll_invoice_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_invoice_type">

                    <TextView
                        android:id="@+id/tv_invoice_type_normal"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/selector_invoice_type"
                        android:gravity="center"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:text="普通发票"
                        android:textColor="@color/selector_222222_00b955" />

                    <TextView
                        android:id="@+id/tv_invoice_type_special"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:background="@drawable/selector_invoice_type"
                        android:gravity="center"
                        android:paddingTop="10dp"
                        android:paddingBottom="10dp"
                        android:text="专用发票"
                        android:textColor="@color/selector_222222_00b955" />
                </LinearLayout>

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_special_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="9dp"
                    android:text="专用发票需要上传开票信息图片，或者填写所有开票必填信息"
                    android:textColor="@color/text_color_333333"
                    android:textSize="12sp"
                    app:layout_constraintTop_toBottomOf="@id/ll_invoice_type"
                    app:rv_backgroundColor="#FFF6E0"
                    app:rv_cornerRadius="4dp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/con_upload_img"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:layout_constraintTop_toBottomOf="@id/tv_special_tips">

                    <TextView
                        android:id="@+id/tv_required"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="*"
                        android:textColor="#FA1313"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="上传开票信息"
                        android:textColor="@color/color_222"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toEndOf="@id/tv_required"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/iv_upload_img"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:src="@drawable/icon_invoice_upload_img"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_title" />

                    <TextView
                        android:id="@+id/tv_upload_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="最多上传一张，"
                        android:textColor="@color/text_color_999999"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_upload_img"
                        app:layout_constraintStart_toEndOf="@id/iv_upload_img" />

                    <TextView
                        android:id="@+id/tv_look_img"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/icon_arrow_right_green"
                        android:text="查看示例图"
                        android:textColor="@color/color_00b955"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="@id/iv_upload_img"
                        app:layout_constraintStart_toEndOf="@id/tv_upload_tips" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_name_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（营业执照上的公司名称）"
                    app:layout_constraintTop_toBottomOf="@id/con_upload_img"
                    app:title="公司名称" />

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_number_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（营业执照上的统一社会信用代码）"
                    app:layout_constraintTop_toBottomOf="@id/company_name_input"
                    app:title="纳税人识别号" />

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_address_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（营业执照上的公司地址）"
                    app:layout_constraintTop_toBottomOf="@id/company_number_input"
                    app:title="地址" />

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_phone_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（专票要求的公司电话）"
                    app:layout_constraintTop_toBottomOf="@id/company_address_input"
                    app:title="电话" />

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_bank_name_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（开户银行、开户银行支行）"
                    app:layout_constraintTop_toBottomOf="@id/company_phone_input"
                    app:title="开户银行" />

                <com.ybmmarket20.view.CompanyNameInputView
                    android:id="@+id/company_bank_card_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    app:desc="（专票要求的银行账号）"
                    app:layout_constraintTop_toBottomOf="@id/company_bank_name_input"
                    app:title="银行账号" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/group_special"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tv_special_tips, con_upload_img,company_phone_input,company_bank_name_input,company_bank_card_input" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tv_submit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="10dp"
        android:paddingVertical="12dp"
        android:text="保存"
        android:gravity="center"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
       android:background="@drawable/selector_invoice_submit" />
</androidx.constraintlayout.widget.ConstraintLayout>