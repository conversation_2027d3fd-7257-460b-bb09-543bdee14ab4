<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_gravity="center"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="1px">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius_TL="2dp"
        app:rv_cornerRadius_TR="2dp">

        <LinearLayout
            android:id="@+id/cart_item_ll"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <CheckBox
                android:id="@+id/shop_check"
                style="@style/CustomCheckboxTheme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="9dp"
                android:gravity="center"
                android:visibility="visible" />

            <TextView
                android:id="@+id/shop_lose"
                android:layout_width="30dp"
                android:layout_height="16dp"
                android:layout_margin="6dp"
                android:background="@drawable/bg_cart_lose_icon"
                android:gravity="center"
                android:text="失效"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />
        </LinearLayout>

        <TextView
            android:id="@+id/cart_section_head02_price"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="搭配套餐：¥18.00"
            android:textColor="@color/cart_head_tv01"
            android:textSize="@dimen/cart_content_tv01"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_cart_section_head03_price"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:text="原价"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/cart_content_tv02" />

        <TextView
            android:id="@+id/cart_section_head03_price"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:text="¥18.00"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/cart_content_tv02" />

    </com.ybmmarket20.common.widget.RoundLinearLayout>
</LinearLayout>