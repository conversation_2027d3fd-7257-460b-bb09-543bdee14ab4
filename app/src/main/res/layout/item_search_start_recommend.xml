<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:layout_marginEnd="@dimen/dimen_dp_10"
    android:background="@drawable/shape_search_start_recommend_item_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/head_bg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_37"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/shape_search_start_recommend_item_head"
        android:layout_marginStart="0.5dp"
        android:layout_marginTop="0.5dp"
        android:layout_marginEnd="0.5dp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_search_start_item_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tv_search_start_recommend_buy_count"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_10"
        android:text="热卖排行榜"
        android:textSize="@dimen/dimen_dp_15"
        android:textColor="#292933"
        android:textStyle="bold"
        android:singleLine="true"
        android:drawableStart="@drawable/icon_search_start_recommend_fire"
        android:drawablePadding="@dimen/dimen_dp_5" />

    <TextView
        android:id="@+id/tv_search_start_recommend_buy_count"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_search_start_item_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_search_start_item_title"
        android:background="@drawable/shape_search_start_recommend_buy_count"
        android:textSize="@dimen/dimen_dp_10"
        android:textColor="@color/white"
        android:paddingStart="@dimen/dimen_dp_5"
        android:paddingEnd="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_9"
        android:gravity="center_vertical"
        android:drawableEnd="@drawable/me_arrow_white"
        android:text="26.5万人买过" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_search_start_recommend_goods"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/head_bg"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10" />

</androidx.constraintlayout.widget.ConstraintLayout>