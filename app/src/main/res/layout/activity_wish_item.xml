<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="110dp"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="115dp"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/wish_iv"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="15dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="15dp"
                android:layout_marginTop="15dp" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="65dp">

                <TextView
                    android:id="@+id/wish_name"
                    android:layout_width="match_parent"
                    android:layout_height="29dp"
                    android:layout_marginRight="@dimen/wish_tv04"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_wish_name"
                    android:textSize="@dimen/wish_tv02" />

                <TextView
                    android:id="@+id/wish_specification"
                    android:layout_width="match_parent"
                    android:layout_height="18dp"
                    android:layout_below="@+id/wish_name"
                    android:layout_marginRight="@dimen/wish_tv04"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_wish01"
                    android:textSize="@dimen/wish_tv07" />

                <TextView
                    android:id="@+id/wish_manufacturers"
                    android:layout_width="match_parent"
                    android:layout_height="18dp"
                    android:layout_below="@+id/wish_specification"
                    android:layout_marginRight="@dimen/wish_tv04"
                    android:gravity="bottom"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_wish01"
                    android:textSize="@dimen/wish_tv07" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/wish_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:text=""
                    android:textColor="@color/text_wish_time"
                    android:textSize="@dimen/wish_tv07" />

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/wish_btn"
                        android:layout_width="@dimen/wish_tv05"
                        android:layout_height="@dimen/wish_tv06"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="@dimen/wish_tv04"
                        android:gravity="center"
                        android:text="立即购买"
                        android:textColor="@color/white"
                        android:textSize="@dimen/wish_tv07" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>