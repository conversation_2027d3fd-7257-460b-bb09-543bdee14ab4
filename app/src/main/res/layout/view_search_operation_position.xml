<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardElevation="@dimen/dimen_dp_0"
    app:cardCornerRadius="@dimen/dimen_dp_8">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <include layout="@layout/item_search_operation_position_single_shop_header" />

        <include layout="@layout/item_search_operation_position_multi_shop_header" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvSearchOP"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:spanCount="3"
            tools:itemCount="3"
            tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            tools:listitem="@layout/item_search_operation_position_goods" />
    </LinearLayout>
</androidx.cardview.widget.CardView>