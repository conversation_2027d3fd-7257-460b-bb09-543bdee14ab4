<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@color/color_fff7ef"
    android:foreground="?android:attr/selectableItemBackground">

    <com.ybmmarket20.common.widget.RoundLinearLayout
        android:id="@+id/fg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:minHeight="40dp"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_product_channel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:gravity="center"
            android:paddingLeft="1.67dp"
            android:paddingRight="1.67dp"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_product_channel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@+id/cart_new_rl_iv"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:maxLines="2"
            android:text=""
            android:textColor="@color/tv_control_new"
            android:textSize="13sp"
            android:textStyle="bold" />


    </com.ybmmarket20.common.widget.RoundLinearLayout>
</RelativeLayout>