<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/v_line_left"
        android:layout_width="@dimen/dimen_dp_0"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="@dimen/dimen_dp_2"
        android:background="@color/colors_DDDDDD"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintEnd_toStartOf="@+id/v_line_right" />

    <View
        android:id="@+id/v_line_right"
        android:layout_width="@dimen/dimen_dp_0"
        app:layout_constraintHorizontal_weight="1"
        android:layout_height="@dimen/dimen_dp_2"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintStart_toEndOf="@+id/v_line_left"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/v_dot"
        android:layout_width="@dimen/dimen_dp_10"
        android:layout_height="@dimen/dimen_dp_10"
        android:background="@drawable/shape_aptitude_dot"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="信息认证"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/color_292933"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dimen_dp_12"
        app:layout_constraintTop_toBottomOf="@+id/v_dot"
        />
</androidx.constraintlayout.widget.ConstraintLayout>