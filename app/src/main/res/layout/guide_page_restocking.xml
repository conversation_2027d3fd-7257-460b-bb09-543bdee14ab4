<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <View
        android:id="@+id/viewWeight"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintEnd_toStartOf="@id/iv_guide_page_product"/>

    <ImageView
        android:id="@+id/iv_guide_page_product"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_dp_10"
        android:src="@drawable/guide_page_restocking_one"
        app:layout_constraintStart_toEndOf="@id/viewWeight"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintEnd_toStartOf="@id/viewWeight2"/>
    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="@dimen/dimen_dp_30"
        android:src="@drawable/guide_page_restocking_two"
        app:layout_constraintBottom_toTopOf="@id/iv_guide_page_product"
       />
    <View
        android:id="@+id/viewWeight2"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_guide_page_product"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="3" />

</androidx.constraintlayout.widget.ConstraintLayout>