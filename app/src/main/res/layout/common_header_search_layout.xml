<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_search"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="vertical"
    android:paddingLeft="10dp"
    android:paddingTop="8dp"
    android:paddingRight="10dp"
    android:paddingBottom="8dp"
    tools:visibility="visible"
    android:visibility="gone">

    <RelativeLayout
        android:id="@+id/home_search_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:background="@drawable/search_round_corner_gray_bg_03">

        <ImageView
            android:id="@+id/iv_a_magnifying_glass"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="6dp"
            android:src="@drawable/icon_a_magnifying_glass" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="3dp"
            android:layout_toRightOf="@+id/iv_a_magnifying_glass"
            android:background="@null"
            android:maxLines="1"
            android:singleLine="true"
            android:text="@string/search_hint"
            android:textColor="@color/color_9494A6"
            android:textSize="13dp" />

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="6dp"
            android:src="@drawable/nav_voice_01" />

    </RelativeLayout>
</LinearLayout>

