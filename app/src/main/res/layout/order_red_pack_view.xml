<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_red_pack"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_ffeeef">

    <ImageView
        android:id="@+id/iv_red_pack"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:src="@drawable/icon_red_packet_order_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_red_pack_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:textColor="@color/text_000000"
        android:textSize="14sp"
        android:ellipsize="end"
        android:textStyle="bold"
        android:maxLines="2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/tv_go_top_up"
        app:layout_constraintStart_toEndOf="@id/iv_red_pack"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="购物金充值最高返200元红包" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_go_top_up"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:drawableEnd="@drawable/icon_arrow_right"
        android:drawablePadding="5dp"
        android:paddingStart="9dp"
        android:paddingTop="8dp"
        android:paddingEnd="6dp"
        android:paddingBottom="8dp"
        android:text="立即充值"
        android:textColor="@color/color_white_FFFFFF"
        android:textSize="13sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/color_FE0F23"
        app:rv_cornerRadius="4dp" />
</androidx.constraintlayout.widget.ConstraintLayout>