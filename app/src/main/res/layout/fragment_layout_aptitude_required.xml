<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_aptitude_bg"
    android:orientation="vertical">

    <com.ybmmarket20.view.MyScrollView
        android:id="@+id/msv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/color_aptitude_bg"
        android:scrollbars="none">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/ll_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_aptitude_required_top_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="13dp"
                    android:text="@string/str_aptitude_required_top_hint"
                    android:textColor="@color/color_text_hint_base_color"
                    android:textSize="12dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="blocksDescendants"
                    android:orientation="vertical">
                    <!--列表-->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rcv_aptitude"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="4"
                        tools:listitem="@layout/item_aptitude_updated" />

                </RelativeLayout>


                <com.ybmmarket20.common.widget.RoundLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="10dp"
                    android:orientation="vertical"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="2dp">

                    <LinearLayout
                        android:id="@+id/ll_tv_customer_phone"
                        android:layout_width="match_parent"
                        android:layout_height="64dp"
                        android:orientation="horizontal"
                        android:paddingLeft="10dp"
                        android:paddingRight="0dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_customer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginTop="15dp"
                                android:text="客户经理"
                                android:textColor="@color/color_text_base_color"
                                android:textSize="14dp" />

                            <TextView
                                android:id="@+id/tv_customer_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginTop="2dp"
                                android:text=""
                                android:textColor="@color/color_text_little_base_color"
                                android:textSize="12dp" />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center_vertical"
                            android:background="#f5f5f5" />

                        <ImageView
                            android:id="@+id/iv_customer_phone"
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_gravity="center_vertical"
                            android:padding="18dp"
                            android:src="@drawable/icon_aptitude_phone" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:background="#f5f5f5" />

                    <LinearLayout
                        android:id="@+id/ll_tv_customer_phone2"
                        android:layout_width="match_parent"
                        android:layout_height="64dp"
                        android:orientation="horizontal"
                        android:paddingLeft="10dp"
                        android:paddingRight="0dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_customer2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginTop="15dp"
                                android:text="客户服务"
                                android:textColor="@color/color_text_base_color"
                                android:textSize="14dp" />

                            <TextView
                                android:id="@+id/tv_customer_phone2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="10dp"
                                android:layout_marginTop="2dp"
                                android:text="400-0505-111"
                                android:textColor="@color/color_text_little_base_color"
                                android:textSize="12dp" />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center_vertical"
                            android:background="#f5f5f5" />

                        <ImageView
                            android:id="@+id/iv_customer_phone2"
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:layout_gravity="center_vertical"
                            android:padding="18dp"
                            android:src="@drawable/icon_aptitude_phone" />

                    </LinearLayout>

                </com.ybmmarket20.common.widget.RoundLinearLayout>

                <TextView
                    android:id="@+id/tv_bottom_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginRight="15dp"
                    android:text="@string/str_aptitude_bottom_tips"
                    android:textColor="@color/color_text_hint_base_color"
                    android:textSize="12dp" />

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_download_more"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginBottom="20dp"
                    android:drawableEnd="@drawable/aptitude_download_more"
                    android:drawableRight="@drawable/aptitude_download_more"
                    android:gravity="left|center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="5dp"
                    android:text="@string/str_aptitude_download_more"
                    android:textColor="@color/color_text_base_color"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:rv_backgroundColor="@color/white"
                    app:rv_cornerRadius="2dp"
                    tools:visibility="visible" />
            </LinearLayout>

            <include
                android:id="@+id/layout_load_error"
                layout="@layout/layout_load_error_reload"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>
