<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_46"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius_BL="@dimen/dimen_dp_6"
    app:rv_cornerRadius_BR="@dimen/dimen_dp_6">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="#9494A6"
        android:layout_marginStart="@dimen/dimen_dp_46"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="以下银行，免输卡号" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="@dimen/dimen_dp_12"
        android:layout_height="@dimen/dimen_dp_12"
        android:src="@drawable/icon_arrow_down"
        android:layout_marginStart="@dimen/dimen_dp_5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvTitle"
        app:layout_constraintTop_toTopOf="parent" />

</com.ybmmarket20.common.widget.RoundConstraintLayout>