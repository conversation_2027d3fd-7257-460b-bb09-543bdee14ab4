<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_gravity="center"
    android:foreground="?android:attr/selectableItemBackground"
    android:gravity="">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/cart_item_ll"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/cart_new_rl_tv02"
                android:layout_width="0dp"
                android:layout_height="0dp" />

            <CheckBox
                android:id="@+id/shop_check"
                style="@style/CustomCheckboxTheme"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_margin="9dp"
                android:gravity="center"
                android:visibility="visible" />

            <TextView
                android:id="@+id/shop_lose"
                android:layout_width="30dp"
                android:layout_height="16dp"
                android:layout_gravity="bottom"
                android:layout_margin="6dp"
                android:background="@drawable/bg_cart_lose_icon"
                android:gravity="center"
                android:text="失效"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone" />
        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/icon_cart_proprietary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:paddingLeft="1.67dp"
            android:paddingRight="1.67dp"
            android:text="他盈"
            android:textColor="@color/color_FF2121"
            android:textSize="11sp"
            android:visibility="visible"
            app:rv_backgroundColor="#0D00B377"
            app:rv_cornerRadius="1.33dp"
            app:rv_strokeColor="@color/color_FF2121"
            app:rv_strokeWidth="0.5dp" />

        <TextView
            android:id="@+id/tv_cart_proprietary"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_marginLeft="6dp"
            android:drawableRight="@drawable/right_new"
            android:gravity="center_vertical"
            android:text="浙江小药药分公司"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/cart_content_tv01"
            android:textStyle="bold" />

    </LinearLayout>
</LinearLayout>