<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/gradient_fe510f_ff2222"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:paddingBottom="10dp"
    android:paddingTop="5dp"
    android:id="@+id/item_view">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="wrap_content"
        android:paddingTop="3dp"
        android:paddingBottom="3dp"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_rebate_red_dot"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_countdown_time"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="29天23:59:59活动结束"
        android:textColor="#FFFFFF"
        android:gravity="end"
        android:textSize="13sp"
        android:drawableEnd="@drawable/icon_arrow_right"
        android:drawablePadding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/iv_icon" />

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_tips"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        tools:text="参与下单返活动，预估月均可返红包30元"
        android:textColor="#000000"
        android:layout_marginTop="5dp"
        android:paddingStart="7dp"
        android:paddingTop="7dp"
        android:textStyle="bold"
        android:paddingBottom="5dp"
        android:paddingEnd="7dp"
        android:textSize="13sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="4dp" />

</androidx.constraintlayout.widget.ConstraintLayout>