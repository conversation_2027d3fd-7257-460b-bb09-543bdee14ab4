<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="18dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="14dp"
        android:layout_height="18dp"/>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="0dp"
        app:layout_constraintStart_toEndOf="@id/iv_select"
        android:layout_marginHorizontal="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginVertical="13dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textColor="@color/color_292933"
        android:textSize="14dp"
        tools:text="已购满10件，下单可获赠品"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>