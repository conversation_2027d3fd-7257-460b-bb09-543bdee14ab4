package com.ybmmarket20.utils

import android.app.Activity
import android.content.Context
import com.ybmmarket20.bean.ProductDetail
import com.ybmmarket20.bean.ProductDetailBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.AlertDialogEx

object ControlGoodsDialogUtil {

    fun showControlGoodsDialog(context: Context, rowsBean: RowsBean?) {
        if (rowsBean == null) return
        if (context !is Activity) return
        if (context.isFinishing) return
        AlertDialogEx(context).apply {
            setMessage("商品id:${rowsBean.id},此商品为控销品，您暂无购买权限，请到控销商城申请授权")
            setConfirmButton("我知道了") { dialog, _ -> dialog.dismiss() }
            setTitle("")
            show()
        }
    }

    fun showControlGoodsDetailDialog(context: Context, productDetail: ProductDetailBean?) {
        if (productDetail == null) return
        if (context !is Activity) return
        if (context.isFinishing) return
        AlertDialogEx(context).apply {
            setMessage("商品id:${productDetail.productId},此商品为控销品，您暂无购买权限，请到控销商城申请授权")
            setConfirmButton("我知道了") { dialog, _ -> dialog.dismiss() }
            setTitle("")
            show()
        }
    }
}