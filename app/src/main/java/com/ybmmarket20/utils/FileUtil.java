package com.ybmmarket20.utils;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;

import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;

import android.util.Log;

import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.YBMBaseFragment;
import com.ybmmarket20.common.util.ToastUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;

/**
 * <AUTHOR> Brin
 * @date : 2019/6/4 - 16:15
 * @Description :
 */
public class FileUtil {

    public static final int CAMERA = 100;
    public static final int PICTURE = 200;
    private static final int REQUEST_EXTERNAL_STORAGE = 1;

    public static String getExternalFilePath() {
        return YBMAppLike.getAppContext().getExternalFilesDir(null).getAbsolutePath();
    }

    public static String getExternalFilePath(String type) {
        return YBMAppLike.getAppContext().getExternalFilesDir(type).getAbsolutePath();
    }

    public static String getExternalCachePath() {
        return YBMAppLike.getAppContext().getExternalCacheDir().getAbsolutePath();
    }

    public static boolean isFileExist(String fileFullPath) {
        File file = new File(fileFullPath);
        return file.exists();
    }

    public static String getFileFullPath(String fileName) {
        return FileUtil.getExternalFilePath() + File.separator + fileName;
    }

    public static String getAptitudeDownloadFilePath() {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getAbsolutePath() + File.separator + "aptitude_" + System.currentTimeMillis() + ".png";
    }

    public static String getAptitudeExamplePicFilePath() {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).getAbsolutePath() + File.separator + "aptitude_example_img_" + System.currentTimeMillis() + ".png";
    }

    /**
     * 获取更新下载apk名字
     *
     * @param packageName
     * @param versionName
     * @return
     */
    public static String getDownLoadApkName(String packageName, String versionName) {
        return packageName + "_" + versionName + ".apk";
    }

    /**
     * 获取file文件的uri地址，适配了7.0以上版本
     *
     * @param file
     * @return
     */
    public static Uri getFileUri(File file) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            String authority = BuildConfig.APPLICATION_ID + ".fileProvider";
            Uri fileUri = FileProvider.getUriForFile(YBMAppLike.getAppContext(), authority, file);
            return fileUri;
        } else {
            return Uri.fromFile(file);
        }
    }

    /**
     * activity中调用相机，并处理了相机调用权限
     *
     * @param mContent
     * @param filePath
     */
    public static void startCameraForResult(BaseActivity mContent, String filePath) {
        RxPermissions rxPermissions = new RxPermissions(mContent);
        if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
            startCameraForResultInternal(mContent, filePath);
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(mContent,
                    "药帮忙App需要申请相机权限，用于拍照",
                    () -> startCameraForResultInternal(mContent, filePath));
        }

    }

    private static void startCameraForResultInternal(BaseActivity mContent, String filePath) {

        mContent.requestEachPermissions(new BaseActivity.PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(new File(filePath)));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    cameraIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                mContent.startActivityForResult(cameraIntent, FileUtil.CAMERA);
            }
        }, Manifest.permission.CAMERA);
    }

    /**
     * fragment中调用相机，并处理了相机调用权限
     *
     * @param mFragment
     * @param filePath
     */
    public static void startCameraForResult(YBMBaseFragment mFragment, String filePath) {
        RxPermissions rxPermissions = new RxPermissions(mFragment.getNotNullActivity());
        if (rxPermissions.isGranted(android.Manifest.permission.CAMERA)) {
            startCameraForResultInternal(mFragment, filePath);
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(mFragment.getNotNullActivity(),
                    "药帮忙App需要申请相机权限，用于拍照",
                    () -> startCameraForResultInternal(mFragment, filePath));
        }

    }

    private static void startCameraForResultInternal(YBMBaseFragment mFragment, String filePath) {
        BaseActivity activity = (BaseActivity) mFragment.getNotNullActivity();
        activity.requestEachPermissions(new BaseActivity.PermissionCallBack("拍照需要申请相机权限") {
            @Override
            public void granted(Permission permission) {
                Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

                cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, FileUtil.getFileUri(new File(filePath)));
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    cameraIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                mFragment.startActivityForResult(cameraIntent, CAMERA);
            }
        }, Manifest.permission.CAMERA);

    }

    /**
     * \
     * fragment中调用图库
     *
     * @param mFragment
     */
    public static void startPicturesForResult(YBMBaseFragment mFragment) {
        RxPermissions rxPermissions = new RxPermissions(mFragment.getNotNullActivity());
        if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
            startPicturesForResultInternal(mFragment);
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(mFragment.getNotNullActivity(),
                    "药帮忙App需要申请存储权限，用于获取相册图片",
                    () -> startPicturesForResultInternal(mFragment));
        }
    }

    private static void startPicturesForResultInternal(YBMBaseFragment mFragment) {
        BaseActivity activity = (BaseActivity) mFragment.getNotNullActivity();
        activity.requestEachPermissions(new BaseActivity.PermissionCallBack("访问相册需要申请存储权限") {
            @Override
            public void granted(Permission permission) {
                Intent pictureIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                pictureIntent.setType("image/*");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    pictureIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                mFragment.startActivityForResult(pictureIntent, PICTURE);
            }
        }, Manifest.permission.READ_EXTERNAL_STORAGE);
    }


    /**
     * \
     * activity中调用图库
     *
     * @param mContent
     */
    public static void startPicturesForResult(BaseActivity mContent) {

        RxPermissions rxPermissions = new RxPermissions(mContent);
        if (rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
            startPicturesForResultInternal(mContent);
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(mContent,
                    "药帮忙App需要申请存储权限，用于获取相册图片",
                    () -> startPicturesForResultInternal(mContent));
        }
    }

    private static void startPicturesForResultInternal(BaseActivity mContent) {

        mContent.requestEachPermissions(new BaseActivity.PermissionCallBack("访问相册需要申请存储权限") {
            @Override
            public void granted(Permission permission) {
                Intent pictureIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                pictureIntent.setType("image/*");
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    pictureIntent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                }
                mContent.startActivityForResult(pictureIntent, PICTURE);
            }
        }, Manifest.permission.READ_EXTERNAL_STORAGE);
    }

    public static boolean isOpenVerifyStoragePermissions(BaseActivity mContent) {
        boolean isOpen = true;
        try {
            //检测是否有写的权限
            int permission = ActivityCompat.checkSelfPermission(mContent,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE);
            if (permission != PackageManager.PERMISSION_GRANTED) {
                isOpen = false;
                // 没有写的权限，去申请写的权限，会弹出对话框
                RxPermissions rxPermissions = new RxPermissions(mContent);
                if (rxPermissions.isGranted(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        && rxPermissions.isGranted(android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
                    ActivityCompat.requestPermissions(mContent, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_EXTERNAL_STORAGE);
                } else {
                    PermissionDialogUtil.showPermissionInfoDialog(mContent,
                            "药帮忙App需要申请存储权限，用于存储文件/图片",
                            () -> ActivityCompat.requestPermissions(mContent, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_EXTERNAL_STORAGE));
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isOpen;
    }

    /**
     * 复制文件
     *
     * @param filename 文件名
     * @param bytes    数据
     */
    public static void copy(String filename, byte[] bytes) {
        try {
            //如果手机已插入sd卡,且app具有读写sd卡的权限
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                FileOutputStream output = null;
                output = new FileOutputStream(filename);
                output.write(bytes);
                Log.e("YBM", "copy: success，" + filename);
                output.close();
            } else {
                Log.e("YBM", "copy:fail, " + filename);
                ToastUtils.showShort("SD卡暂不可读写,请检查是否开启存储权限");
            }
        } catch (IOException e) {
            e.printStackTrace();
            ToastUtils.showShort("保存图片到相册异常");
        }

    }

    /**
     * 通知图库更新
     *
     * @param context
     * @param filePath
     */
    public static void sendBroadcastImage(Context context, String filePath, String fileName) {
        // 其次把文件插入到系统图库
        try {
            MediaStore.Images.Media.insertImage(context.getContentResolver(),
                    filePath, fileName, null);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 最后通知图库更新
        context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                Uri.parse("file://" + filePath)));
    }


    /**
     * 获取单个文件的MD5值
     *
     * @param file  文件
     * @param radix 位 16 32 64
     * @return
     */
    public static String getFileMD5s(File file, int radix) {
        if (!file.isFile()) {
            return null;
        }
        MessageDigest digest = null;
        FileInputStream in = null;
        byte buffer[] = new byte[1024];
        int len;
        try {
            digest = MessageDigest.getInstance("MD5");
            in = new FileInputStream(file);
            while ((len = in.read(buffer, 0, 1024)) != -1) {
                digest.update(buffer, 0, len);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        BigInteger bigInt = new BigInteger(1, digest.digest());
        return bigInt.toString(radix);
    }

    public static String md5sum(File file) {
        InputStream fis;
        byte[] buffer = new byte[1024];
        int numRead = 0;
        MessageDigest md5;
        try {
            fis = new FileInputStream(file);
            md5 = MessageDigest.getInstance("MD5");
            while ((numRead = fis.read(buffer)) > 0) {
                md5.update(buffer, 0, numRead);
            }
            fis.close();
            return toHexString(md5.digest());
        } catch (Exception e) {
            System.out.println("error");
            return null;
        }
    }

    private static final char HEX_DIGITS[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    public static String toHexString(byte[] b) {
        StringBuilder sb = new StringBuilder(b.length * 2);
        for (int i = 0; i < b.length; i++) {
            sb.append(HEX_DIGITS[(b[i] & 0xf0) >>> 4]);
            sb.append(HEX_DIGITS[b[i] & 0x0f]);
        }
        return sb.toString();
    }

    /**
     * 复制文件
     * @param oldPath
     * @param newPath
     * @return
     */
    public static boolean copyFile(String oldPath, String newPath) {
        try {
            File oldFile = new File(oldPath);
            if (!oldFile.exists()) {
                Log.e("--Method--", "copyFile:  oldFile not exist.");
                return false;
            } else if (!oldFile.isFile()) {
                Log.e("--Method--", "copyFile:  oldFile not file.");
                return false;
            } else if (!oldFile.canRead()) {
                Log.e("--Method--", "copyFile:  oldFile cannot read.");
                return false;
            }
            FileInputStream fileInputStream = new FileInputStream(oldPath);
            FileOutputStream fileOutputStream = new FileOutputStream(newPath);
            byte[] buffer = new byte[1024];
            int byteRead;
            while (-1 != (byteRead = fileInputStream.read(buffer))) {
                fileOutputStream.write(buffer, 0, byteRead);
            }
            fileInputStream.close();
            fileOutputStream.flush();
            fileOutputStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
