package com.ybmmarket20.utils;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.view.CommonDialogLayout;


/**
 * Created by wh on 2017/4/6.
 */

public class DialogUtil {

    public static final int SHARE_TYPE_ALL = 0; // 分享全部类型 （微信、qq）
    public static final int SHARE_TYPE_WECHAT = 1; // 分享微信
    public static final int SHARE_TYPE_QQ = 2; // 分享qq

    public static int number;

    public interface DialogClickListener {
        void confirm(String content);

        void cancel();

        void showSoftInput(View view);

        default void onDismiss(){

        }
    }

    public interface DialogPerfectClickListener {
        void confirm(Dialog dialog, EditText et, String content);

        void cancel(Dialog dialog);

        void showSoftInput(Dialog dialog, View view);

    }

    public interface DialogSingleClick {
        void click(String content);
    }

    /**
     * 弹出的dialog(创建电子计划单,修改数量等)
     */
    public static void inputDialog(final Activity activity, int inputType, String title, String tip, String confirm, String cancel, String defaultStr, final DialogClickListener listener) {
        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_common_input, null);
        final EditText etName = (EditText) view.findViewById(R.id.et_content);
        TextView tvTitle = (TextView) view.findViewById(R.id.tv_title);
        TextView tvConfirm = (TextView) view.findViewById(R.id.tv_confirm);
        TextView tvCancel = (TextView) view.findViewById(R.id.tv_cancel);
        TextView tvTip = (TextView) view.findViewById(R.id.tv_tip);
        final ImageView ivClear = (ImageView) view.findViewById(R.id.iv_clear);
        etName.setInputType(inputType);
        if (inputType == InputType.TYPE_CLASS_NUMBER) {
            etName.setFilters(new InputFilter[]{new InputFilter.LengthFilter(7)});
        }
        if (!TextUtils.isEmpty(title)) {
            tvTitle.setText(title);
        }
        if (!TextUtils.isEmpty(confirm)) {
            tvConfirm.setText(confirm);
        }
        if (!TextUtils.isEmpty(cancel)) {
            tvCancel.setText(cancel);
        }

        if (!TextUtils.isEmpty(tip)) {
            tvTip.setVisibility(View.VISIBLE);
            tvTip.setText(tip);
        } else {
            tvTip.setVisibility(View.GONE);
        }
        //先添加监听
        etName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                ivClear.setVisibility(s != null && s.length() > 0 ? View.VISIBLE : View.GONE);
            }
        });

        if (!TextUtils.isEmpty(defaultStr)) {
            etName.setText(defaultStr);
        }

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.cancel();
                }
            }
        });

        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.confirm(etName.getText().toString());
                }
            }
        });

        ivClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                etName.setText("");
            }
        });

        /*
         * 自动弹出软键盘
         * */
        dialog.setOnShowListener(new DialogInterface.OnShowListener() {
            public void onShow(DialogInterface dialog) {
                if (listener != null) {
                    listener.showSoftInput(etName);
                }
            }
        });
        dialog.setContentView(view);
        int screenWidth = UiUtils.getScreenWidth();
        Window dialogWindow = dialog.getWindow();
        WindowManager.LayoutParams p = dialogWindow.getAttributes();
        p.width = (int) (screenWidth * 0.8);
        dialogWindow.setAttributes(p);
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }


    /**
     * 弹出的电子计划单修改数量
     */
    public static void modifyProductNum(final Activity activity, String num, String price, final DialogClickListener listener) {
        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_plan_input, null);
        final EditText etNum = (EditText) view.findViewById(R.id.et_content);
        final EditText etPrice = (EditText) view.findViewById(R.id.et_content2);
        TextView tvTitle = (TextView) view.findViewById(R.id.tv_title);
        TextView tvConfirm = (TextView) view.findViewById(R.id.tv_confirm);
        TextView tvCancel = (TextView) view.findViewById(R.id.tv_cancel);
        final ImageView ivClear = (ImageView) view.findViewById(R.id.iv_clear);
        final ImageView ivClear2 = (ImageView) view.findViewById(R.id.iv_content2_clear);

        //先添加监听
        etNum.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                ivClear.setVisibility(s != null && s.length() > 0 ? View.VISIBLE : View.GONE);
            }
        });

        //先添加监听
        etPrice.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable edt) {
                ivClear2.setVisibility(edt != null && edt.length() > 0 ? View.VISIBLE : View.GONE);
                String temp = edt.toString();
                int posDot = temp.indexOf(".");
                if (posDot <= 0) return;
                if (temp.length() - posDot - 1 > 2) {
                    edt.delete(posDot + 3, posDot + 4);
                }
            }
        });

        etNum.setText(num);
        etPrice.setText(price);
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.cancel();
                }
            }
        });
        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(etNum.getText())) {
                    etNum.setError("请输入补货数量");
                    return;
                }
                dialog.dismiss();
                if (listener != null) {
                    listener.confirm(etNum.getText().toString() + "&" + etPrice.getText().toString());
                }
            }
        });

        ivClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                etNum.setText("");
            }
        });

        ivClear2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                etPrice.setText("");
            }
        });

        /*
         * 自动弹出软键盘
         * */
        dialog.setOnShowListener(new DialogInterface.OnShowListener() {
            public void onShow(DialogInterface dialog) {
                if (listener != null) {
                    listener.showSoftInput(etNum);
                }
            }
        });

        dialog.setContentView(view);
        int screenWidth = UiUtils.getScreenWidth();
        Window dialogWindow = dialog.getWindow();
        WindowManager.LayoutParams p = dialogWindow.getAttributes();
        p.width = (int) (screenWidth * 0.8);
        dialogWindow.setAttributes(p);
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }

    /**
     * 弹出的dialog(创建电子计划单,修改数量等)
     */
    public static void sendEmailDialog(final Activity activity, int inputType, String title, String confirm, String cancel, final DialogPerfectClickListener listener) {

        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_send_email, null);
        final EditText etName = (EditText) view.findViewById(R.id.et_content);
        TextView tvTitle = (TextView) view.findViewById(R.id.tv_title);
        TextView tvConfirm = (TextView) view.findViewById(R.id.tv_confirm);
        TextView tvCancel = (TextView) view.findViewById(R.id.tv_cancel);
        final ImageView ivClear = (ImageView) view.findViewById(R.id.iv_clear);

        etName.setInputType(inputType);
        if (inputType == InputType.TYPE_CLASS_NUMBER) {
            etName.setFilters(new InputFilter[]{new InputFilter.LengthFilter(7)});
        }
        if (!TextUtils.isEmpty(title)) {
            tvTitle.setText(title);
        }
        if (!TextUtils.isEmpty(confirm)) {
            tvConfirm.setText(confirm);
        }
        if (!TextUtils.isEmpty(cancel)) {
            tvCancel.setText(cancel);
        }

        //先添加监听
        etName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                ivClear.setVisibility(s != null && s.length() > 0 ? View.VISIBLE : View.GONE);
            }
        });

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.cancel(dialog);
                }
            }
        });

        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.confirm(dialog, etName, etName.getText().toString());
                }
            }
        });

        ivClear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                etName.setText("");
            }
        });

        /*
         * 自动弹出软键盘
         * */
        dialog.setOnShowListener(new DialogInterface.OnShowListener() {
            public void onShow(DialogInterface d) {
                if (listener != null) {
                    listener.showSoftInput(dialog, etName);
                }
            }
        });
        dialog.setContentView(view);
        int screenWidth = UiUtils.getScreenWidth();
        Window dialogWindow = dialog.getWindow();
        WindowManager.LayoutParams p = dialogWindow.getAttributes();
        p.width = (int) (screenWidth * 0.8);
        dialogWindow.setAttributes(p);
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }


    /**
     * 分享的dialog
     */
    public static void shareDialog(Activity activity, final DialogSingleClick listener) {
        shareDialog(activity, listener, SHARE_TYPE_ALL);
    }

    /**
     * 分享的dialog
     */
    public static void shareDialog(Activity activity, final DialogSingleClick listener, int type) {
        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_bottom_share, null);
        LinearLayout llShareWx = view.findViewById(R.id.ll_share_wx);
        LinearLayout llShareQQ = view.findViewById(R.id.ll_share_qq);
        if (type == SHARE_TYPE_QQ) {
            llShareWx.setVisibility(View.GONE);
            llShareQQ.setVisibility(View.VISIBLE);
        } else if (type == SHARE_TYPE_WECHAT) {
            llShareWx.setVisibility(View.VISIBLE);
            llShareQQ.setVisibility(View.GONE);
        }
        llShareWx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wx");
                }
            }
        });
        llShareQQ.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("qq");
                }
            }
        });
        view.findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams p = dialogWindow.getAttributes();
            p.width = UiUtils.getScreenWidth();
            dialogWindow.setAttributes(p);
            dialogWindow.setGravity(Gravity.BOTTOM);
            dialogWindow.setWindowAnimations(R.style.AnimBottom);
        }
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }

    /**
     * 分享到微信dialog
     */
    public static void shareDialogInvoice(Activity activity, final DialogSingleClick listener, int type) {
        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_bottom_invoice_share, null);
        LinearLayout llShareWx = view.findViewById(R.id.ll_share_wx);
        LinearLayout llShareQQ = view.findViewById(R.id.ll_share_qq);
        if (type == SHARE_TYPE_QQ) {
            llShareWx.setVisibility(View.GONE);
            llShareQQ.setVisibility(View.VISIBLE);
        } else if (type == SHARE_TYPE_WECHAT) {
            llShareWx.setVisibility(View.VISIBLE);
            llShareQQ.setVisibility(View.GONE);
        }
        llShareWx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wx");
                }
            }
        });
        llShareQQ.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("qq");
                }
            }
        });
        view.findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams p = dialogWindow.getAttributes();
            p.width = UiUtils.getScreenWidth();
            dialogWindow.setAttributes(p);
            dialogWindow.setGravity(Gravity.BOTTOM);
            dialogWindow.setWindowAnimations(R.style.AnimBottom);
        }
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }

    /**
     * 海报-分享的dialog
     */
    public static void shareTheInvitationDialog(Activity activity, boolean type, final DialogSingleClick listener) {
        final Dialog dialog = new Dialog(activity, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_bottom_the_invitation_share, null);
        view.findViewById(R.id.iv_share_wx).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wx");
                }
            }
        });
        view.findViewById(R.id.tv_share_wx).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wx");
                }
            }
        });
        view.findViewById(R.id.iv_share_wxpyq).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wxpyq");
                }
            }
        });
        view.findViewById(R.id.tv_share_wxpyq).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("wxpyq");
                }
            }
        });

        view.findViewById(R.id.iv_share_download).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("bctp");
                }
            }
        });

        view.findViewById(R.id.tv_share_download).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.click("bctp");
                }
            }
        });

        view.findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        if (type) {
            view.findViewById(R.id.ll_share_download).setVisibility(View.VISIBLE);
        }
        dialog.setContentView(view);
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams p = dialogWindow.getAttributes();
            p.width = UiUtils.getScreenWidth();
            dialogWindow.setAttributes(p);
            dialogWindow.setGravity(Gravity.BOTTOM);
            dialogWindow.setWindowAnimations(R.style.AnimBottom);
        }
        dialog.setCancelable(true);
        if (!activity.isFinishing()) {
            dialog.show();
        }
    }

    /*
     * 编辑弹出对话框加减数量
     * */
    public static void addOrSubDialog(Activity act, int inputType, String defaultStr, final int mediumPackageNum
            , final boolean isSplit, boolean isTitle, final DialogClickListener listener) {

        final Dialog dialog = new Dialog(act, R.style.dialog_confirm_style);
        View view = LayoutInflater.from(act).inflate(R.layout.dialog_common_add_sub, null);
        final EditText etNumber = (EditText) view.findViewById(R.id.et_num);
        ImageView ivNumSub = (ImageView) view.findViewById(R.id.iv_numSub);
        ImageView ivNumAdd = (ImageView) view.findViewById(R.id.iv_numAdd);
        TextView tvCancel = (TextView) view.findViewById(R.id.tv_cancel);
        TextView tvConfirm = (TextView) view.findViewById(R.id.tv_confirm);
        TextView tvTitle = (TextView) view.findViewById(R.id.tv_title);

        tvTitle.setText(isTitle ? "编辑购买数量" : "编辑可退数量");

        etNumber.setInputType(inputType);
        if (!TextUtils.isEmpty(defaultStr)) {
            etNumber.setText(defaultStr);
        }

        @SuppressLint("HandlerLeak") final Handler handler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                if (msg.what == 10) {
                    Integer number = (Integer) msg.obj;
                    String numStr = String.valueOf(number);
                    etNumber.setText(numStr);
                    etNumber.setSelection(numStr.length());
                }
            }
        };

        tvConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    int number = Integer.parseInt(etNumber.getText().toString().trim());
                    if (isSplit) {
                        dialog.dismiss();
                        if (listener != null) {
                            listener.confirm(etNumber.getText().toString().trim());
                        }
                    } else {
                        if (number % mediumPackageNum == 0) {
                            dialog.dismiss();
                            if (listener != null) {
                                listener.confirm(etNumber.getText().toString().trim());
                            }
                        } else {
                            try {
                                String num = (number > mediumPackageNum ? (number - number % mediumPackageNum) : mediumPackageNum) + "";
                                etNumber.setText(num);
                                etNumber.setSelection(num.length());
                                ToastUtils.showShort("只能以" + mediumPackageNum + "的倍数购买");
                            } catch (Exception e) {
                                BugUtil.sendBug(e);
                            }
                        }
                    }
                } catch (Throwable e) {
                    ToastUtils.showShort("输入数量错误");
                }
            }
        });
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                if (listener != null) {
                    listener.cancel();
                }
            }
        });

        ivNumSub.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String num = etNumber.getText().toString().trim();
                if (TextUtils.isEmpty(num) || num.length() > 5) {
                    return;
                }
                numOnClick(num, mediumPackageNum, isSplit, false, handler);
            }
        });

        ivNumAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String num = etNumber.getText().toString().trim();
                if (TextUtils.isEmpty(num) || num.length() > 5) {
                    return;
                }
                numOnClick(num, mediumPackageNum, isSplit, true, handler);
            }
        });
        dialog.setOnDismissListener(dialog1 -> {
            if(listener!=null){
                listener.onDismiss();
            }
        });
        // 自动弹出软键盘
        dialog.setOnShowListener(new DialogInterface.OnShowListener() {
            public void onShow(DialogInterface dialogI) {

//                if (dialog.isShowing()) {
//                    if (listener != null) {
//                        listener.showSoftInput(etNumber);
//                    }
//                    SmartExecutorManager.getInstance().executeUI(new Runnable() {
//                        @Override
//                        public void run() {
//
//                        }
//                    }, 200);
//                }
            }
        });

        etNumber.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    Window window = dialog.getWindow();
                    if (window != null) {
                        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
                    }
                }
            }
        });

        dialog.setContentView(view);
        int screenWidth = UiUtils.getScreenWidth();
        Window dialogWindow = dialog.getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams p = dialogWindow.getAttributes();
            p.width = (int) (screenWidth * 0.8);
            dialogWindow.setAttributes(p);
        }
        dialog.setCancelable(true);
        try {
            if (!act.isFinishing()) {
                if (view.getContext() instanceof Activity && ((Activity) view.getContext()).isFinishing()) return;
                dialog.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
     * 加减==改变编辑购买数量
     * */
    private static void numOnClick(String num, int mediumPackageNum, boolean isSplit, boolean isAdd, Handler handler) {

        //获取商品的数量
        int shopNum;
        try {
            shopNum = Integer.valueOf(num);
        } catch (Exception ex) {
            shopNum = 0;
        }

        if (!isAdd) {
            if (isSplit) {
                shopNum--;
            } else {
                shopNum -= mediumPackageNum;
            }
            if (shopNum < 1) {
                shopNum = 0;
            }

        } else {
            shopNum += mediumPackageNum;
            if (shopNum > 99999) {
                shopNum = 99999;
            }
        }
        handler.sendMessage(handler.obtainMessage(10, shopNum));
    }

    /**
     * 判断是不是emoji的表情符号
     */
    private static boolean isEmojiChar(char codePoint) {
        return !((codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD) || ((codePoint >= 0x20) && codePoint <= 0xD7FF)) ||
                ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));

    }

    /*------------------------------公共提示框（非简单的dialog实现）--------------------------------*/
    //做一个全局的，以防同一时刻相同的提示重复出现
    private static Toast mCommonToast = null;

    /**
     * 显示公共的状态
     *
     * @param type 类型：成功、失败、无网络
     * @param msg  dialog提示的文字（四个字为宜）
     */
    public static void showCommonStatus(CommonDialogLayout.CommonTip type, String msg) {
        CommonDialogLayout view = null;
        int imgRes;
        if (type == CommonDialogLayout.CommonTip.failed) {
            imgRes = R.drawable.icon_common_tip_failed;
        } else if (type == CommonDialogLayout.CommonTip.success) {
            imgRes = R.drawable.icon_common_tip_success;
        } else {
            imgRes = R.drawable.icon_common_tip_notnet;
        }

        if (mCommonToast == null) {
            mCommonToast = new Toast(YBMAppLike.getAppContext());
            mCommonToast.setGravity(Gravity.CENTER, 0, 0);
            mCommonToast.setDuration(Toast.LENGTH_SHORT);
        }
        view = new CommonDialogLayout(YBMAppLike.getAppContext());
        view.setDialogAttr(imgRes, msg);
        mCommonToast.setView(view);
        mCommonToast.show();
    }
}
