package com.ybmmarket20.utils.analysis

import org.json.JSONObject

/**
 * 首页埋点（不包含商品跟踪的埋点）
 */
class HomeSteadyAnalysisEntry(
    var actionKey: String?, // 事件名称
    var action: String?, //动作（一般为路由地址）
    var offset: String?, //位置
    var image_url: String?, //图片url
    var text: String?, //文本
    var id: String?,
    var sku_id: String?,
    var extra: MutableMap<String, String>? //扩展字段
) {
    constructor(): this(null, null, null, null, null, null, null, null)

    /**
     * 添加扩展字段
     */
    fun putExtraString(key: String?, value: String?) {
        if (key == null || value == null) return
        if (extra == null) extra = mutableMapOf()
        extra!![key] = value
    }
}

/**
 * 生成字段
 */
fun generateEntry(): HomeSteadyAnalysisEntry = HomeSteadyAnalysisEntry()

/**
 * 基础首页埋点
 */
fun homeSteadyAnalysis(entry: HomeSteadyAnalysisEntry?) {
    try {
        entry?.apply {
            val json = JSONObject()
            action?.let {json.put("action", it)}
            offset?.let {json.put("offset", it)}
            image_url?.let {json.put("image_url", it)}
            text?.let {json.put("text", it)}
            id?.let {json.put("id", it)}
            sku_id?.let {json.put("sku_id", it)}
            extra?.let {
                it.map { entry ->
                    json.put(entry.key, entry.value)
                }
            }
            actionKey?.let { XyyIoUtil.track(it, json) }
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * 事件相关埋点（主要用于点击事件）
 */
fun homeSteadyAnalysisAction(actionKey: String?, action: String?, offset: String? = null, text: String? = null, image_url: String? = null, block: ((HomeSteadyAnalysisEntry)->Unit)? = null) {
    val entry = generateEntry()
    if (block != null) block(entry)
    entry.apply {
        this.actionKey = actionKey
        this.action = action
        this.offset = offset
        this.text = text
        this.image_url = image_url
    }.let(::homeSteadyAnalysis)
}

///**
// * 事件相关埋点（主要用于点击事件）
// * 可修改参数
// */
//@JvmOverloads
//fun homeSteadyAnalysisAction(actionKey: String?, action: String?, offset: String? = null, text: String? = null, image_url: String? = null) {
//    homeSteadyAnalysisAction(actionKey, action, offset, text, image_url, null)
//}

/**
 * banner点击
 */
fun homeSteadyAnalysisBannerClick(actionKey: String?, action: String?, offset: String?, image_url: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, image_url = image_url)
}

/**
 * 金刚位点击
 */
fun homeSteadyAnalysisFastEntryClick(actionKey: String?, action: String?, offset: String?, text: String?, image_url: String?, block: ((HomeSteadyAnalysisEntry)->Unit)? = null) {
    homeSteadyAnalysisAction(actionKey, action, offset, text, image_url, block = block)
}

/**
 * 胶囊位点击
 */
fun homeSteadyAnalysisStreamerClick(actionKey: String?, action: String?, text: String?) {
    homeSteadyAnalysisAction(actionKey, action, text = text)
}

/**
 * 导购入口点击
 */
fun homeSteadyAnalysisShoppingGuide(actionKey: String?, action: String?, offset: String?, text: String?, sku_id: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, text = text) {
        it.sku_id = sku_id
    }
}

/**
 * 精选店铺顶栏更多按钮点击
 */
fun homeSteadyAnalysisShopTopClick(actionKey: String?, action: String?, text: String?) {
    homeSteadyAnalysisAction(actionKey, action, text = text)
}

/**
 * 精选店铺点击店铺
 */
fun homeSteadyAnalysisShopClick(actionKey: String?, action: String?, offset: String?, text: String?, sku_id: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, text = text) {
        it.sku_id = sku_id
    }
}

/**
 * 精选店铺店铺曝光
 */
fun homeSteadyAnalysisShopGoodsExposure(actionKey: String?, action: String?, offset: String?, text: String?, sku_id: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, text = text) {
        it.sku_id = sku_id
    }
}

/**
 * 定时活动顶栏点击
 */
fun homeSteadyAnalysisSeckillTopClick(actionKey: String?, action: String?, text: String?) {
    homeSteadyAnalysisAction(actionKey, action, text = text)
}

/**
 * 定时活动更多点击
 */
fun homeSteadyAnalysisSeckillMoreClick(actionKey: String?, action: String?, text: String?) {
    homeSteadyAnalysisAction(actionKey, action, text = text)
}

/**
 * 定时活动商品点击
 */
fun homeSteadyAnalysisSeckillGoodsClick(actionKey: String?, action: String?, offset: String?, text: String?, id: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, text = text) {
        it.id = id
    }
}

/**
 * 定时活动商品曝光
 */
fun homeSteadyAnalysisSeckillGoodsExposure(actionKey: String?, action: String?, offset: String?, text: String?, id: String?) {
    homeSteadyAnalysisAction(actionKey, action, offset, text = text) {
        it.id = id
    }
}

/**
 * 商品feed流tab点击
 */
fun homeSteadyAnalysisGoodsFeedTabClick(actionKey: String?, offset: String?, text: String?) {
    homeSteadyAnalysisAction(actionKey, null, offset, text = text)
}


