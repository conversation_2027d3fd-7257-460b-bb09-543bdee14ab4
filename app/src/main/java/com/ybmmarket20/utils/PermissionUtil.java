package com.ybmmarket20.utils;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.location.LocationManager;
import android.net.Uri;
import android.provider.Settings;

import com.ybmmarket20.R;
import com.ybmmarket20.common.AlertDialogEx;

/**
 * 权限请求的工具类
 */

public class PermissionUtil {

    /**
     * 打开 APP 的详情设置
     */
    public static void openAppDetails(final Context context, String permission, Boolean canCancel) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setMessage("应用需要 “" + permission + "” 权限，请到 “应用信息 -> 权限” 中授予！");
        builder.setPositiveButton("去手动授权", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openAppSettingDetail(context);
            }
        });
        if (canCancel) {
            builder.setNegativeButton("取消", null);
        }
        builder.show();
    }

    /**
     * 显示授权提示框
     *
     * @param context      上下文
     * @param permission   权限名称
     * @param isCancelable 是否可以取消
     */
    public static void showPermissionDialog(final Context context, String permission, Boolean isCancelable) {
        final AlertDialogEx permissionDialog = new AlertDialogEx(context);
        permissionDialog.setTitle("提示");
        permissionDialog.setMessage(permission + "权限，请到 “应用信息 -> 权限” 中授予！");


        if (isCancelable) {
            permissionDialog.setCancelButton("取消", null);
            permissionDialog.getButton(context.getResources().getColor(R.color.text_9494A6));
            permissionDialog.setCancelable(true);
        } else {
            permissionDialog.setCancelable(false);
        }

        permissionDialog.setConfirmButton("去授权", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                openAppSettingDetail(context);
                permissionDialog.dismiss();
            }
        });
    }

    /**
     * 打开应用设置详情页
     *
     * @param context
     */
    private static void openAppSettingDetail(Context context) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(Uri.parse("package:" + context.getPackageName()));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY);
        intent.addFlags(Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS);
        context.startActivity(intent);
    }
    /**
     * 检测GPS是否打开
     *
     * @return
     */
    public static boolean checkGPSIsOpen(Context context) {
        boolean isOpen;
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        isOpen = locationManager.isProviderEnabled(android.location.LocationManager.GPS_PROVIDER);
        return isOpen;
    }
}
