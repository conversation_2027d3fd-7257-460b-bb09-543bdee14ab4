package com.ybmmarket20.utils;

import android.text.Editable;
import android.text.TextWatcher;

import java.util.regex.Pattern;

public class DecimalTextWatcher implements TextWatcher {
    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
    }
    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
    }
    @Override
    public void afterTextChanged(Editable s) {
        if (s.length() > 1 && !Pattern.matches("^\\d+\\.{0,1}\\d{0,2}", s.toString())) {
            s.delete(s.length() - 1, s.length());
        }
        if (s.length() > 1 && Pattern.matches("^0{1,}(\\d+)", s.toString())) {
            s.delete(0, 1);
        }
        if (s.length() > 1 && Pattern.matches("^0{2,}\\.\\d", s.toString())) {
            s.delete(0, 1);
        }
    }
}
