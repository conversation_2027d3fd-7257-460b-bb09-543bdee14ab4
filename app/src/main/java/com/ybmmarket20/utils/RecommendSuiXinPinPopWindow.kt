package com.ybmmarket20.utils

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.RecommendSuiXinPinAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RecommendSuiXinPinBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.view.BaseBottomPopWindow
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import java.math.BigDecimal

/**
 * 推荐随心拼底部弹框
 */
class RecommendSuiXinPinPopWindow(val mContext: Context, val callback: (MutableList<RecommendSuiXinPinBean>) -> Unit): BaseBottomPopWindow() {

    private var searchMoreParams = RequestParams()
    private var mExcludeIds = ""

    private var mAdapter: RecommendSuiXinPinAdapter? = null
    private var goodsList: MutableList<RowsBean>? = null
    private var isThirdCompany: String = ""
    private var mShopCode: String? = null
    private var addedSuiXinPinGoodsList = mutableListOf<RecommendSuiXinPinBean>()
    private var mViewModel: SpellGroupRecommendGoodsViewModel? = null
    private var mKeyword:String = ""
    private var mScmId: String? = null
    var mPopWindowDismiss: (() -> Unit)? = null

    override fun getLayoutId(): Int = R.layout.popwindow_recommend_sui_xin_pin

    init {
        if (goodsList == null) {
            goodsList = mutableListOf()
        }
    }

    override fun initView() {
    }

    fun setData(excludeIds: String, viewModel: SpellGroupRecommendGoodsViewModel) {
        mViewModel = viewModel
        mExcludeIds = excludeIds
        mShopCode = viewModel.shopCode
        val rv = getView<RecyclerView>(R.id.rv)
        val ivClose = getView<ImageView>(R.id.ivClose)
        val cartCount = getView<TextView>(R.id.tv_cart_count_text)
        val skuCount = getView<TextView>(R.id.tv_cart_count)
        val totalPrice = getView<TextView>(R.id.tv_cart_price)
        val submit = getView<TextView>(R.id.tv_settle)
        val etSearch = getView<TextView>(R.id.et_search)
        val ivEtClear = getView<ImageView>(R.id.iv_et_clear)
        val tvTitle = getView<TextView>(R.id.tvTitle)
        tvTitle.setText("随心拼")
        submit.setOnClickListener {
            dismiss()
        }

        ivEtClear.setOnClickListener {
            etSearch.setText("")
        }

        etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                mKeyword = s.toString()
                ivEtClear.visibility = if (s.toString().isEmpty()) View.GONE else View.VISIBLE
                if (s.toString().isEmpty()){
                    getNewGoodsList(false)
                }
            }
        })

        etSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        etSearch.setOnEditorActionListener { v, actionId, event ->

            if(actionId == EditorInfo.IME_ACTION_SEARCH){
                if (mContext is BaseActivity){ //隐藏键盘
                    mContext.hideSoftInput(etSearch)
                }

                getNewGoodsList(false)
                return@setOnEditorActionListener true

            }
            return@setOnEditorActionListener false
        }


        ivClose.setOnClickListener { dismiss() }
        goodsList = mutableListOf()
        mAdapter = RecommendSuiXinPinAdapter(goodsList!!, {recommendSuiXinPinBean ->
            val bean = addedSuiXinPinGoodsList.find { it.rowsBean.id == recommendSuiXinPinBean.rowsBean.id }
            if (bean != null) {
                addedSuiXinPinGoodsList.remove(bean)
            }
            addedSuiXinPinGoodsList.add(recommendSuiXinPinBean)
            var goodsCount = 0
            var price = BigDecimal(0)
            addedSuiXinPinGoodsList.forEach {
                val selectedRowsBean = it.rowsBean
                val selectedCount = it.count
                goodsCount += selectedCount
                price = price.add(BigDecimal(selectedRowsBean.actSuiXinPin.suiXinPinPrice).multiply(BigDecimal(selectedCount)))
            }
            skuCount.text = if (addedSuiXinPinGoodsList.size > 99) "99+" else "${addedSuiXinPinGoodsList.count { it.count != 0 }}"
            cartCount.text = "共${goodsCount}件"

            totalPrice.text = "${price.toDouble()}"
        }, viewModel)

        mAdapter?.setOnLoadMoreListener({ getNewGoodsList(true) }, rv)

        rv.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        getNewGoodsList()
    }

    override fun show(token: View?) {
        super.show(token)
        popwindow.setOnDismissListener {
            if (addedSuiXinPinGoodsList.isEmpty()) {
                return@setOnDismissListener
            }
            mViewModel?.clearRecommendGoods()
            addedSuiXinPinGoodsList.filter { it.count > 0 }.toMutableList().let(callback::invoke)
            mPopWindowDismiss?.invoke()
        }
    }

    private fun getNewGoodsList(isLoadMore: Boolean = false) {
        if (mContext is BaseActivity) {
            mContext.showProgress()
        }
        val newgoodparams = getNewGoodsListRequestParams(isLoadMore)
        HttpManager.getInstance()
            .post(AppNetConfig.SORTNET, newgoodparams, object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean?>,
                    brandBean: SearchResultBean?
                ) {
                    if (!isLoadMore) {
                        mAdapter?.onClear()
                    }
                    val flowData = BaseFlowData(brandBean?.sptype, brandBean?.spid, brandBean?.sid)
//                    goodlistAdapter?.setFlowData(flowData)
                    if (mContext is BaseActivity) {
                        mContext.dismissProgress()
                    }
                    if (!brandBean?.rows.isNullOrEmpty()) {
                        mScmId = brandBean!!.rows[0]?.qtData?.scmId
                    }
                    goodsList?.addAll(brandBean?.rows?: mutableListOf())
                    if(brandBean?.rows.isNullOrEmpty()){
                        mAdapter?.setEmptyView(
                                mContext,
                                R.layout.layout_search_product_empty_view,
                                R.drawable.icon_empty,
                                "啊哦...\n没有找到相关的商品"
                        )
                    }
                    AdapterUtils.addLocalTimeForRows(brandBean?.rows)
                    mAdapter?.let {
                        AdapterUtils.notifyAndControlLoadmoreStatus(
                            brandBean?.rows,
                            it,
                            !isLoadMore,
                            brandBean?.isEnd?: true
                        )
                    }
                    mAdapter?.notifyDataSetChanged()
                    searchMoreParams = brandBean?.requestParams?: RequestParams()

                }

                override fun onFailure(error: NetError) {
                    if (mContext is BaseActivity) {
                        mContext.dismissProgress()
                    }
                }
            })
    }

    /**
     * 请求参数
     */
    private fun getNewGoodsListRequestParams(loadMore: Boolean): RequestParams? {

        return if (loadMore){
            searchMoreParams.apply {
                if (!mScmId.isNullOrEmpty()) {
                    put("scmId", mScmId)
                }
            }
        } else RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            if (mKeyword.isNotEmpty()){
                put("keyword", mKeyword)
            }
            mShopCode?.let {
                put("shopCodes", mShopCode)
            }
            put("displayCategoryId", "-1")
            put("property", "smsr.sale_num")
            put("direction", "desc")
            put("tagList", "YBM_ACT_SUI_XIN_PIN")
            put("isThirdCompany", isThirdCompany)
            put("excludeIds", mExcludeIds)
            put("spFrom", "8")

        }
    }



    override fun getLayoutParams(): LinearLayout.LayoutParams {
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight() - ConvertUtils.dp2px(60f));
    }


}