package com.ybmmarket20.utils.im.core.impl

import com.apkfuns.logutils.LogUtils
import com.ybmmarket20.utils.im.core.callback.IMCoreSimpleMessageCallback
import com.ybmmarket20.utils.im.core.data.MemberInfo

/**
 * <AUTHOR>
 * 接收消息基类
 */

abstract class BaseIMCoreSimpleMessageCallback: IMCoreSimpleMessageCallback {

    override fun onReceiveGroupTextMessage(msgID: String?, groupID: String?, sender: MemberInfo?, text: String?) {
        LogUtils.tag("IM_onReceiveGroupTextMessage()").i("msgID = ${msgID?: ""} --- groupID = ${groupID?: ""} ---  sender = ${sender?.toString()?: ""} --- text = ${text?: ""}")
    }

    override fun onReceiveGroupCustomMessage(msgID: String?, groupID: String?, sender: MemberInfo?, customData: ByteArray?) {
        LogUtils.tag("IM_onReceiveGroupTextMessage()").i("msgID = ${msgID?: ""} --- groupID = ${groupID?: ""} ---  sender = ${sender?.toString()?: ""} --- text = ${String(customData?: byteArrayOf())}")
    }
}