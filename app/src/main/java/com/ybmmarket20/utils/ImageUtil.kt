package com.ybmmarket20.utils

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import androidx.core.graphics.drawable.DrawableCompat
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import android.widget.ImageView
import com.apkfuns.logutils.LogUtils
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.BitmapImageViewTarget
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.utils.BugUtil
import com.ybm.app.utils.UiUtils.getContext
import com.ybmmarket20.R
import com.ybmmarket20.constant.AppNetConfig

/**
 * <AUTHOR> Brin
 * @date : 2019/7/9 - 17:06
 * @Description :
 * @version
 */
class ImageUtil {

    companion object {
        /**
         *  加载图片，如果url无效或加载失败或占位图，则是透明drawable
         */
        @JvmStatic
        fun load(mContext: Context, url: String?, iv: ImageView) {
            if (mContext is Activity && mContext.isDestroyed) return
            if (url.isNullOrEmpty()) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv)
            } else {
                ImageHelper.with(mContext).load(url)
                    .placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(iv)
            }
        }


        /**
         *  加载图片，如果url无效或加载失败或占位图，则是透明drawable
         */
        @JvmStatic
        fun loadNoPlace(mContext: Context, url: String?, iv: ImageView) {
            if (mContext is Activity && mContext.isDestroyed) return
            if (url.isNullOrEmpty()) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv)
            } else {
                ImageHelper.with(mContext).load(url)
                    .placeholder(R.drawable.transparent).error(R.drawable.transparent)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(iv)
            }
        }

        /**
         *  加载本地图片
         */
        @JvmStatic
        fun load(mContext: Context, resId: Int, iv: ImageView) {
            if (mContext is Activity && mContext.isDestroyed) return
            ImageHelper.with(mContext).load(resId).into(iv);
        }

        @JvmStatic
        fun loadCircleImage(mContext: Context, url: String?, iv: ImageView) {
            if (mContext is Activity && mContext.isDestroyed) return
            if (url.isNullOrEmpty()) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv);
            } else {
                ImageHelper.with(mContext).load(url).asBitmap().centerCrop()
                    .placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(object : BitmapImageViewTarget(iv) {
                        override fun setResource(resource: Bitmap?) {
                            val circularBitmapDrawable = RoundedBitmapDrawableFactory.create(
                                getContext().getResources(),
                                resource
                            )
                            circularBitmapDrawable.isCircular = true
                            iv.setImageDrawable(circularBitmapDrawable)
                        }
                    })
            }
        }

        @JvmStatic
        fun loadRoundCornerImage(
            mContext: Context,
            url: String?,
            iv: ImageView,
            cornerRadius: Int
        ) {
            if (mContext is Activity && mContext.isDestroyed) return
            if (url.isNullOrEmpty()) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(iv);
            } else {
                ImageHelper.with(mContext).load(url).asBitmap().centerCrop()
                    .placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min)
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(object : BitmapImageViewTarget(iv) {
                        override fun setResource(resource: Bitmap?) {
                            val circularBitmapDrawable = RoundedBitmapDrawableFactory.create(
                                getContext().getResources(),
                                resource
                            )
//                                circularBitmapDrawable.isCircular = true
                            circularBitmapDrawable.cornerRadius =
                                UiUtils.dp2px(cornerRadius).toFloat()
                            iv.setImageDrawable(circularBitmapDrawable)
                        }
                    })
            }
        }


        /**
         * 这里的比例调整为固定宽高比3：1
         */
        @JvmStatic
        fun getDrawableForHeight(mContext: Context, bitmap: Bitmap, mScaledHeight: Int): Drawable? {
            if (mScaledHeight <= 0) {
                return null
            }
            /*
            val width: Int = bitmap.getWidth()                  //图片原始宽度
            val height: Int = bitmap.getHeight()                //图片原始高度
            val mScaledWidth = width * mScaledHeight * 1.0f / height   //计算出按比缩放后的宽度
            */
            val scaledBitmap =
                Bitmap.createScaledBitmap(bitmap, mScaledHeight * 3, mScaledHeight, true)
            val backgroundDrawable: Drawable = BitmapDrawable(mContext.resources, scaledBitmap)
            return backgroundDrawable
        }

        /**
         * 这里的比例调整为固定宽高比3：1
         */
        @JvmStatic
        fun getBitmapForHeight(
            mContext: Context,
            bitmap: Bitmap,
            mScaledHeight: Int,
            mScaledwidth: Int
        ): Bitmap? {
            if (mScaledHeight <= 0) {
                return null
            }
            val scaledBitmap =
                Bitmap.createScaledBitmap(bitmap, mScaledwidth, mScaledHeight, true)
            return scaledBitmap
        }


        /**
         * 用纯色生成图片
         */
        @JvmStatic
        fun imageViewColor(view: ImageView, iconColor: String) {
            val mutate: Drawable = view.drawable.mutate()
            val temp: Drawable = DrawableCompat.wrap(mutate)
            val colorStateList = ColorStateList.valueOf(getColorInt(iconColor))
            DrawableCompat.setTintList(temp, colorStateList)
            view.setImageDrawable(temp)
        }


        @JvmStatic
        fun getImageUrl(imageUrl: String?): String? {
            return getStyleImageUrl(imageUrl, AppNetConfig.CDN_HOST)
        }

        @JvmStatic
        fun getTagImageUrl(imageUrl: String?): String? {
            return getStyleImageUrl(imageUrl, AppNetConfig.LORD_TAG)
        }

        @JvmStatic
        fun getStyleImageUrl(imageUrl: String?, style: String): String? {
            if (imageUrl == null) {
                return null
            }
            if (imageUrl.startsWith("http")) {
                return imageUrl
            }
            if (imageUrl.startsWith("//")) {
                return "http:" + imageUrl;
            }
            return style + imageUrl
        }

        /**
         * string color parse to int color
         * default is 0x00000000
         */
        private fun getColorInt(iconColor: String): Int {
            var parseColor: Int = 0x00000000;
            try {
                parseColor = Color.parseColor(iconColor)
            } catch (e: IllegalArgumentException) {
                BugUtil.sendBug(java.lang.IllegalArgumentException("色值转换错误"))
            }

            return parseColor
        }


    }

}

fun getFullPicUrl(picUrl: String): String {

    return if (picUrl.startsWith("http")) {
        picUrl
    } else {
        AppNetConfig.LORD_TAG.plus(picUrl)
    }
}

