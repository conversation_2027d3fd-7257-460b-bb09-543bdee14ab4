package com.ybmmarket20.business.shop

import android.annotation.SuppressLint
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.xyyreport.page.search.SearchReportConstant
import com.ybmmarket20.xyyreport.page.shopGoods.ShopGoodsTabReport
import com.ybmmarket20.xyyreport.paramsInfo.ShopTabInfo
import com.ybmmarket20.xyyreport.spm.XyyReportActivity
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory

abstract class ShopGoodsTabAnalysisFragment: LazyFragment() {

    var mScmId = ""

    abstract fun getGoodsListAdapter(): GoodsListAdapterNewCategory?

    @SuppressLint("NotifyDataSetChanged")
    fun trackPv() {
        val orgId = arguments?.getString("orgId")
        val shopCode = arguments?.getString("shopCode")
        if (!orgId.isNullOrEmpty()) ShopGoodsTabReport.trackShopOrgIdPv(notNullActivity, orgId)
        else ShopGoodsTabReport.trackShopCodePv(notNullActivity, shopCode)

        (notNullActivity as? XyyReportActivity)?.putExtension(SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID, mScmId)
        getGoodsListAdapter()?.onClearTrackRecord()
        getGoodsListAdapter()?.data?.filterIsInstance<RowsBean>()?.forEach {
            it.reloadTag ++
        }
        getGoodsListAdapter()?.notifyDataSetChanged()
    }

    fun addGoodsReportParams(searchResult: SearchResultBean, floorIndex: Int, floorName: String?, sortNavIndex: Int, sortNavText: String?, isShowFilter: Boolean) {
        searchResult.rows?.forEach { rowsBean ->
            rowsBean?.apply {
                mIsShopGoodsTab = true
                qtListData = null
                listExpId = null
                searchRecPurchaseStrategyCode = null
                scmId = searchResult.scmId
                mShopTabInfo = ShopTabInfo(floorIndex, floorName, sortNavIndex, sortNavText, isShowFilter)
            }
        }
        mScmId = searchResult.scmId?: ""
        (notNullActivity as? XyyReportActivity)?.putExtension(SearchReportConstant.EXTENSION_SEARCH_GOODS_SCM_ID, searchResult.scmId)
    }
}