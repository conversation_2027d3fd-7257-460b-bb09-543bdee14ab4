package com.ybmmarket20.search

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.Button
import android.widget.CheckBox
import androidx.core.text.isDigitsOnly
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.utils.JsonUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 *  规格pop弹框
 */
class SpecficationPopWindow(var specficationBeans: MutableList<SearchFilterBean>?) :
    BaseFilterPopWindow() {

    private var rv_specfication: RecyclerView? = null
    private lateinit var adapter: SynthesizeAdapter

    init {
        initThisView()
    }

    fun setData(data: MutableList<SearchFilterBean>?){
        val newData = data?.map { JsonUtils.fromJson<SearchFilterBean>(JsonUtils.toJson(it), SearchFilterBean::class.java) } as? MutableList<SearchFilterBean>
        newData?.fold(0) {acc, searchFilterBean ->
            acc + if (!searchFilterBean.specCount.isNullOrEmpty() && searchFilterBean.specCount.isDigitsOnly() && searchFilterBean.isSelected) searchFilterBean.specCount.toInt() else 0
        }.let {
            if (it == 0) {
                "确定"
            } else {
                "确定(${it}件)"
            }.let(getView<Button>(R.id.btn_affirm)::setText)
        }
        adapter.setNewData(newData)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initThisView() {
        rv_specfication = getView(R.id.rv_specfication)
        val btnConfirm = getView<Button>(R.id.btn_affirm)
        val data  = specficationBeans?.map { JsonUtils.fromJson<SearchFilterBean>(JsonUtils.toJson(it), SearchFilterBean::class.java) } as? MutableList<SearchFilterBean>
        adapter = SynthesizeAdapter(R.layout.item_pop_specfication, data)
        rv_specfication?.adapter = adapter
        adapter.setOnClickListener {
            if (it == "0") {
                "确定"
            } else {
                "确定(${it}件)"
            }.let(btnConfirm::setText)

        }
        rv_specfication?.layoutManager = GridLayoutManager(rv_specfication?.context, 2)

        getView<Button>(R.id.btn_reset).setOnClickListener {
            (adapter.data as MutableList<SearchFilterBean>).forEach {
                it.isSelected = false
            }
            btnConfirm.setText("确定")
            rv_specfication?.adapter?.notifyDataSetChanged()
            adapter.selectCallBack()
        }

        btnConfirm.setOnClickListener {

            multiSelectedSpecStr = StringBuilder()
            (adapter.data as MutableList<SearchFilterBean>).forEach {
                if (it.isSelected) {
                    multiSelectedSpecStr.append(it.key).append(",")
                }
            }
            if (multiSelectedSpecStr.endsWith(",")) {
                multiSelectedSpecStr.deleteCharAt(multiSelectedSpecStr.length - 1)
            }
            mOnSelectListener?.onConfirm(multiSelectedSpecStr.toString())
            dismiss()
        }
    }

//    override fun setMultiSelectedSpecStr(selectedSpecStr: String?) {
//        super.setMultiSelectedSpecStr(selectedSpecStr)
//        if (selectedSpecStr!=null) {
//            val arr = selectedSpecStr.split(",")
//            specficationBeans?.forEach {
//                it.isSelected = arr.contains(it.key)
//            }
//            adapter.notifyDataSetChanged()
//        }
//    }

    override fun onPreDismiss() {
        super.onPreDismiss()
        multiSelectedSpecStr = StringBuilder()
        (adapter.data as MutableList<SearchFilterBean>).forEach {
            if (it.isSelected) {
                multiSelectedSpecStr.append(it.key).append(",")
            }
        }
        if (multiSelectedSpecStr.endsWith(",")) {
            multiSelectedSpecStr.deleteCharAt(multiSelectedSpecStr.length - 1)
        }
    }

    override fun getLayoutId() = R.layout.pop_layout_specfication

    override fun initView() {
        /*specficationBeans = mutableListOf()
        specficationBeans?.apply {
            add(SearchFilterBean().apply { key = "5mg*12" })
            add(SearchFilterBean().apply { key = "10mg*12" })
            add(SearchFilterBean().apply { key = "15mg*12" })
            add(SearchFilterBean().apply { key = "20mg*12" })
        }*/
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun show(token: View?) {
        super.show(token)
        rv_specfication?.adapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelectedOption() {
        (adapter.data as MutableList<SearchFilterBean>).forEach {
            it.isSelected = false
        }
        rv_specfication?.adapter?.notifyDataSetChanged()
    }

    inner class SynthesizeAdapter(layoutResId: Int, val list: MutableList<SearchFilterBean>?)
        :YBMBaseAdapter<SearchFilterBean>(layoutResId, list) {

        var mCallback: ((selectedSpecCount: String)-> Unit)? = null

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchFilterBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val cbItem = baseViewHolder.getView<CheckBox>(R.id.cb_item)
                val specCount = if (!bean.specCount.isNullOrEmpty()) " (${bean.specCount})" else ""
                val specCountSpannable = SpannableStringBuilder(specCount).also {
                    it.setSpan(ForegroundColorSpan(Color.parseColor(if(bean.isSelected) "#00B377" else "#9090A1")), 0, it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
                cbItem.text = SpannableStringBuilder(bean.key ?: "").append(specCountSpannable)
                cbItem.isChecked = bean.isSelected
                holder.setOnClickListener(R.id.ll_item) {
                    bean.apply {
                        isSelected = !isSelected
                        notifyDataSetChanged()
                        selectCallBack()
                    }
                }
            }
        }

        fun setOnClickListener(callback: ((selectedSpecCount: String)-> Unit)?) {
            mCallback = callback
        }

        fun selectCallBack() {
            (data as MutableList<SearchFilterBean>).fold(0) {acc, searchFilterBean ->
                acc + if (!searchFilterBean.specCount.isNullOrEmpty() && searchFilterBean.specCount.isDigitsOnly() && searchFilterBean.isSelected) searchFilterBean.specCount.toInt() else 0
            }.let {
                mCallback?.invoke("${it?: ""}")
            }
        }
    }
}