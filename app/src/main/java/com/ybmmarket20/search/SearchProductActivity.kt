package com.ybmmarket20.search

import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import com.github.mzule.activityrouter.router.Routers
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import java.io.UnsupportedEncodingException
import java.net.URLEncoder

/**
 * 大搜
 */
//@Router(
//    "searchproduct",
//    "searchproduct/:keyword",
//    "searchproduct/:show",
//    "searchproduct/:voice",
//    "searchproduct/:id/:name",
//    "searchproduct/:tagList",
//    "searchproduct/:tagList/:title"
//)
@Deprecated("旧专区搜索，现在使用SearchProductSectionActivity")
class SearchProductActivity: AnalysisSearchProductActivity() {

    override fun getAdapter(): YBMBaseListAdapter<*> =
        GoodListAdapterNew(this, R.layout.item_goods_new, comments, true)

    override fun getParams(isLoadMore: Boolean): RequestParams {
        if (isLoadMore) {
            addAnalysisRequestParams(searchMoreParams, mFlowData)
            return searchMoreParams
        }

        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("showSimilarGoodsJump", "1")
        params.put("merchantId", merchantId)
        val pageUrl = intent.getStringExtra(Routers.KEY_RAW_URL)
        if (pageUrl != null) {
            params.put("pageurl", pageUrl)
        }
        if (prePageSource != null) {
            try {
                params.put(
                    "pageSource",
                    prePageSource + "_e" + URLEncoder.encode(
                        URLEncoder.encode(keyword, "UTF-8"),
                        "UTF-8"
                    )
                )
            } catch (e: UnsupportedEncodingException) {
                e.printStackTrace()
            }
        }

        // 综合、销量、价格 排序
        if (!SpUtil.isKa() && searchFilterSynthesizeBean != null) {
            var property = ""
            var propertyDescOrAsc = ""
            when (searchFilterSynthesizeBean.selectedSearchOption) {
                SearchFilterBean.SYNTHESIZE -> property = PROPERTY_SYNTHESIZE
                SearchFilterBean.SALESVOLUME -> {
                    property = PROPERTY_SALESVOLUME
                    propertyDescOrAsc = DIRECTION_DESC
                }

                SearchFilterBean.PRICE -> {
                    property = PROPERTY_PRICE
                    propertyDescOrAsc = DIRECTION_ASC
                }
            }
            // 不传排序入参，默认是综合
            if (!PROPERTY_SYNTHESIZE.equals(property, ignoreCase = true)) {
                params.put("property", property)
                params.put("direction", propertyDescOrAsc)
            }
        }
        if (SpUtil.isKa() && !TextUtils.isEmpty(property)) {
            propertyDescOrAsc = when (propertyName) {
                "价格从低到高" -> "asc"
                "价格从高到低" -> "desc"
                else -> "desc"
            }
            params.put("property", property)
            params.put("direction", propertyDescOrAsc)
        }

        // 规格
        if (!TextUtils.isEmpty(specStr)) {
            params.put("spec", specStr)
        }

        if (!TextUtils.isEmpty(isExcludePt)) {
            params.put("isExcludePt", isExcludePt)
        }

        // 外部进入增加shopcode过滤条件
        if (!TextUtils.isEmpty(shopCodesFromOut)) {
            params.put("shopCodes", shopCodesFromOut)
        }

        // 商家
        if (!TextUtils.isEmpty(selectedShopcodes.toString())) {
            params.put("shopCodes", selectedShopcodes.toString())
        }

        //自营
        if (isThirdCompany == 0) {
            params.put("isThirdCompany", "0")
        }
        // 高毛
        if (highGross == 1) {
            params.put("highGross", "1")
        }
        //厂家
        if (!TextUtils.isEmpty(manufacturer)) {
            params.put("manufacturer", manufacturer)
        }
        //经营类型
        if (!TextUtils.isEmpty(drugClassification)) {
            params.put("drugClassificationStr", drugClassification)
        }
        //仅看有货
        if (isAvailable) {
            params.put("hasStock", "1")
        }
        //可用券
        if (isCanUseCoupon) {
            params.put("isAvailableCoupons", "1")
        }
        //有促销
        if (isPromotion) {
            params.put("isPromotion", "1")
        }
        //价格区间-最低价
        if (!TextUtils.isEmpty(priceRangeFloor)) {
            params.put("minPrice", priceRangeFloor)
        }
        //价格区间-最高价
        if (!TextUtils.isEmpty(priceRangeTop)) {
            params.put("maxPrice", priceRangeTop)
        }
        // 分类
        if (TextUtils.isEmpty(keyword)) {

            //全部分类 categoryId
            if (!TextUtils.isEmpty(id)) {
                params.put("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null && !TextUtils.isEmpty(id)) {
                mClassifyPop2.setDataType("categoryIdsStr", id)
            }
        } else if (!keyword.equals("all", ignoreCase = true)) {
            //定义"all"为搜索全部药品(优惠券跳转过来)，参数不传
            if (!isFromOftenBuy) {
                params.put("keyword", keyword)
            }
            if (!TextUtils.isEmpty(id)) {
                params.put("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null) {
                mClassifyPop2.setDataType("categoryIdsStr", id)
            }
            if (mClassifyPop2 != null && !isFromOftenBuy) {
                mClassifyPop2.setDataType("keyword", keyword)
            }
        }

        if (preKeyWord != null && preKeyWord == keyword) {
            addAnalysisRequestParams(params, mFlowData)
        }

        params.put("type", SEARCH_TYPE_NORMAL.toString() + "")
        if (isFromOftenBuy) {
            params.put("spFrom", if (isFromHome == 1) "6" else "5")
            params.put("masterStandardProductId", masterStandardProductId)
        } else {
            params.put("spFrom", spFrom)
        }
        // 搜索全区或活动搜索，多种活动用 taglist 用","隔开
        if (!TextUtils.isEmpty(tagList)) {
            var tempTagList = tagList
            if (!TextUtils.isEmpty(mustTagList)) {
                tempTagList += ","
                tempTagList += mustTagList
            }
            params.put("tagList", tempTagList)
        } else if (!TextUtils.isEmpty(mustTagList)) {
            params.put("tagList", mustTagList)
        }

//        if (isDpby) {
//            params.put("isWholesale", "1")
//            XyyIoUtil.track("action_Search_FreeShipping_outside")
//        }
//        //拼团
//        if (isSpellGroup) {
//            params.put("productTypes", "3")
//        }
        // 拼团包邮
        if (isDpby && isSpellGroup) {
            params.put("isGroupBuyingOrWholesale", "1")
        }
        //只看中药
        if (isTraditionalChineseMedicine){
            params.put("isOnlyTraditionalChineseMedicine", "1")
        }
        //同省
        if (isSameProvince){
            params.put("isSameProvince", "1")
        }

        return params
    }

    override fun getSearchDataPost() {
        val params = getParams(false)
        val url = searchUrl
        HttpManager.getInstance().post(url, params, object : BaseResponse<SearchResultBean>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<SearchResultBean>?,
                rowsBeans: SearchResultBean
            ) {
                super.onSuccess(content, obj, rowsBeans)
                dismissProgress()
                hideSoftInput()
                searchType = rowsBeans.type
                if (obj != null) {
                    if (obj.isSuccess) {
                        //更新资质审核状态
                        updateLicenseStatus(rowsBeans.licenseStatus, currentLicenesStatusListener)
                        //根据状态获取埋点数据、触发列表打开事件
                        analysisAfterGetListData(BaseFlowData(rowsBeans.sptype, rowsBeans.spid, rowsBeans.sid), params)
                        //处理无结果query截断召回和截断后仍无结果兜底召
                        handleNoQuery(rowsBeans)
                        //处理 319类型的活动
                        handleActivityShowType(rowsBeans)
                        //购物车悬浮窗
                        showFloatCart(isFromOftenBuy)
                        //处理商品列表数据
                        if (comments != null) {
                            comments.clear()
                            //处理更换关键词回到列表顶部
                            searchProductListView.scrollToPosition(0)
                        } else {
                            comments = ArrayList()
                        }
                        //更新数据
                        updateSearchData(true, rowsBeans,false)
                        //隐藏搜索启动页
                        setSearchStartPageVisibility(false)
                        //显示第一行筛选项
                        mBrandRg01.visibility = View.VISIBLE
                        //显示第二行筛选项
                        mBrandRg02.visibility = View.VISIBLE
                        //显示商品列表
                        searchProductListView.visibility = View.VISIBLE
                    }
                }
                popDismiss()
                //大搜搜索埋点
                trackSearch(rowsBeans.count)
                //固定筛选栏曝光
//                searchDynamicFilterAllExposure()
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
                popDismiss()
                detailAdapter.loadMoreFail()
            }
        })
    }

    private fun searchDynamicFilterAllExposure() {
        val labelGroupKeys = arrayListOf<String>()
        val labelNames = arrayListOf<String>()
        rbChineseMedicine?.let {//仅看中药
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isOnlyTraditionalChineseMedicine")
            }
        }
        rbSpellGroupAndPgby?.let {//拼团包邮
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isGroupBuyingOrWholesale")
            }
        }
        mRbSelfSupport?.let {//自营
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isThirdCompany")
            }
        }
        rbSameProvince?.let {//同省
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isSameProvince")
            }
        }
        mRbCanUseCoupon?.let {//凑单、可用券
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isAvailableCoupons")
            }
        }
        rbGross?.let {//优选
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isHighGross")
            }
        }
        rbExpress?.let {//京东/顺丰
            if (it.isVisible) {
                labelNames.add(it.text.toString())
                labelGroupKeys.add("isJdOrSfExpress")
            }
        }

    }

    override fun requestParamsEqualsPre(
        preParams: RequestParams?,
        curParams: RequestParams
    ): Boolean {
        return preParams?.equalsParamValues(
            curParams,
            "property",
            "direction",
            "isThirdCompany",
            "manufacturer",
            "drugClassificationStr",
            "hasStock",
            "isPromotion",
            "minPrice",
            "maxPrice",
            "categoryIdsStr",
            "keyword"
        ) ?: false
    }

    override fun getAggsUrl(): String = AppNetConfig.SORTNET_aggs

    override fun getManufacturersUrl(): String = AppNetConfig.FIND_MANUFACTURER

    /**
     * 商品列表加载更多
     */
    override fun getLoadMoreResponse() {
        val params = getParams(true)
        HttpManager.getInstance().post(searchUrl, params, object :
            BaseResponse<SearchResultBean>() {
            override fun onSuccess(
                content: String?,
                obj: BaseBean<SearchResultBean>?,
                rowsBeans: SearchResultBean
            ) {
                super.onSuccess(content, obj, rowsBeans)
                if (obj != null) {
                    if (obj.isSuccess) {
                        updateLicenseStatus(rowsBeans.licenseStatus, null)
                        if (rowsBeans.rows != null) {
                            updateSearchData(false, rowsBeans,false)
                        }
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                detailAdapter.loadMoreFail()
            }
        })
    }

    override fun getManufacturersParams(): RequestParams? = null
    override fun getSearchScene(): String? = null
    override fun getPageType(): Int = PAGE_TYPE_NORMAL
    override fun isShowNearEffective(): Boolean = false
    override fun getSearchUrl(): String = AppNetConfig.SORTNET

    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_SEARCH_AREA_AND_SHOP
}