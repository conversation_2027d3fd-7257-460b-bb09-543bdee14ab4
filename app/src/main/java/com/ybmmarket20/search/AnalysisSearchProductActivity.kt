package com.ybmmarket20.search

import android.text.TextUtils
import com.ybmmarket20.bean.GoodsSearchRecommendInfo
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.OperationPositionInfo
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.utils.analysis.updateFlowData
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import org.json.JSONException
import java.util.Locale

/**
 * 大搜埋点
 */
abstract class AnalysisSearchProductActivity : BaseSearchProductActivity(), ICouponEntryType {


    private var preParams: RequestParams? = null

    /**
     * 搜索；埋点
     */
    fun trackSearch(searchCount: Int) {
        // 搜索的埋点。
        try {
            if (!(TextUtils.isEmpty(keyword) || keyword.lowercase(Locale.getDefault()) == "all")) {
                val trackParams = hashMapOf<String, String>(
                    "searchTerms" to keyword, // 商品总数
                    "goodsNumber" to "$searchCount",
                    "type" to "$searchType"
                )
                if (source == 2 || source == 3 || source == 4) {
                    // 搜索热词，历史，联想点击顺序（第几个）	position
                    trackParams["position"] = "$selectedSearchPosition"
                }
                // 联想词	lenovoWord
                if (source == 4) {
                    trackParams["lenovoWord"] = suggestStr
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_SEARCH, trackParams)
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }


    /**
     * 获取列表数据后的埋点 保存埋点参数(搜索或推荐推荐) 列表页打开、推荐列表页打开
     *
     * @param rowsBeans 列表请求返回的参数
     * @param params    本次请求参数
     */
    fun analysisAfterGetListData(
        rowsBeans: BaseFlowData,
        params: RequestParams
    ) {
        var spId = rowsBeans.spId
        var sId = rowsBeans.sId
        var spType = rowsBeans.spType
        // 是否是兜底推荐并且埋点参数通过逗号分为正常搜索埋点参数和推荐埋点参数
        val isNoResult =
            searchType == SEARCH_TYPE_OUT && spId != null && spId.contains(",") && sId != null && sId.contains(
                ","
            ) && spType != null && spType.contains(",")
        if (preKeyWord == null || preKeyWord != keyword) {
            if (isNoResult) {
                // 兜底推荐 需要埋点上报搜索打开事件和搜索无结果打开事件，两个spId使用逗号(,)隔开,两个sId使用逗号(,)隔开
                // 逗号(,)前面为搜索值后面为推荐值
                val spIds = spId!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                spId = spIds[0]
                val recommendSpId = spIds[1]
                val sIds = sId!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                sId = sIds[0]
                val recommendSId = sIds[1]
                val spTypes = spType!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                spType = spTypes[0]
                val recommendSpType = spTypes[1]
                // 兜底推荐 mFlowData 保存推荐的埋点参数，该状态下下拉、上拉、加购、打开列表页使用推荐的埋点参数
                updateFlowData(mFlowData, recommendSpType, recommendSpId, recommendSId, null)
            } else {
                // 普通搜索状态 mFlowData 保存搜索的买点参数，该状态下下拉、上拉、加购、打开列表页使用搜索的埋点参数
                updateFlowData(mFlowData, spType, spId, sId, null)
            }
        }
        if (isNoResult) {
            if (!requestParamsEqualsPre(preParams, params)) {
                // 埋点上报推荐列表打开事件
                val normalSearchFlowData = BaseFlowData(spType, spId, sId, null, "")
                flowDataPageCommoditySearch(normalSearchFlowData)
            }
        }
        if (!requestParamsEqualsPre(preParams, params)) {
            // searchType == SEARCH_TYPE_OUT 情况搜索
            if (mFlowData!= null) {
                flowDataPageCommoditySearch(mFlowData)
                detailAdapter.flowData = mFlowData
            }
        }
        preParams = params
        preKeyWord = keyword
    }

    /**
     * 判断当前请求参数与上次请求的keyWord和筛选条件是否一致
     *
     * @param preParams 上次请求的参数
     * @param curParams 当前请求的参数
     * @return true 一致
     */
    abstract fun requestParamsEqualsPre(
        preParams: RequestParams?,
        curParams: RequestParams
    ): Boolean

    /**
     * 获取普通品
     */
    private fun getNormalTypeList(rowsBean: RowsBean?): List<RowsBean> {
        return if (rowsBean == null) emptyList() else listOf(rowsBean.also { it.isNormalGoods = true })
    }

    /**
     * 运营位
     */
    private fun getOperationTypeList(operationInfo: OperationPositionInfo?): List<RowsBean> {
        if (operationInfo == null) return emptyList()
        val products = operationInfo.products
        if (products == null) return emptyList()
        val isSingleOp = products.size == 1
        return products.onEach {
            it.isOPSingleGoods = isSingleOp
            it.isOpGoods = true
        }
    }

    /**
     * 组合购
     */
    private fun getGroupCombinationList(groupPurchaseInfo: GroupPurchaseInfo?, isSingle: Boolean): List<RowsBean> {
        if (groupPurchaseInfo?.mainProduct == null) return emptyList()
        return mutableListOf(groupPurchaseInfo.mainProduct).apply {
            this += groupPurchaseInfo.subProducts
        }.onEach {
            it.isSingleCombinationPurchase = isSingle
            it.isMultipleCombinationPurchase = !isSingle
        }
    }

    /**
     * 加价购
     */
    private fun getGroupCombinationListRecommend(groupPurchaseInfo: GoodsSearchRecommendInfo?): List<RowsBeanCombinedExt> {
        if (groupPurchaseInfo?.products == null) return emptyList()
        return mutableListOf<RowsBeanCombinedExt>().apply {
            addAll(groupPurchaseInfo.products!!)
            onEach {
                it.isSingleCombinationPurchase = false
                it.isMultipleCombinationPurchase = true
            }
        }
    }


    fun addGoodsReportParams(searchResult: SearchResultOPBean?) {
        val qtListData = searchResult?.qtListData
        searchResult?.rows?.flatMap {
            //普通品
            val normalGoodsType = it.productInfo != null
            //运营位
            val operationPositionType = it.operationInfo?.products != null
            //组合购
            val groupCombinationSingleType = it.groupPurchaseInfo != null
            //加价购
//            val groupCombinationMultipleType = it.additionalPurchaseInfo != null
            val groupCombinationMultipleType = it.recommendInfo != null
            return@flatMap when {
                normalGoodsType -> getNormalTypeList(it.productInfo)
                operationPositionType -> getOperationTypeList(it.operationInfo)
                groupCombinationSingleType -> getGroupCombinationList(it.groupPurchaseInfo, true)
//                groupCombinationMultipleType -> getGroupCombinationList(it.additionalPurchaseInfo, false)
                groupCombinationMultipleType -> getGroupCombinationListRecommend(it.recommendInfo)
                else -> emptyList()
            }
        }?.forEach {
            it.qtListData = qtListData
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        GoodsPlaceExposureRecord.release(this@AnalysisSearchProductActivity)
    }
}