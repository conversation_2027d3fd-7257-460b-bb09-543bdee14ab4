package com.ybmmarket20.network;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.sdk.android.httpdns.HttpDns;
import com.alibaba.sdk.android.httpdns.HttpDnsService;
import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.common.AppUpdateManagerV2;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import okhttp3.Dns;

/**
 * <AUTHOR> Brin
 * @date : 2020/7/20 - 16:48
 * @Description :
 */
public class OkHttpDns implements Dns {


    private final Dns SYSTEM = Dns.SYSTEM;
    HttpDnsService httpdns;//httpdns 解析服务
    private static OkHttpDns instance = null;

    public final String UpdateHost = "downloads.ybm100.com";
    public final String SassUpdateHost = "saasdld.ybm100.com";
    public final String SourceUpdateHost = "update.ybm100.com";

    private OkHttpDns(Context context) {
        // host地址使用httpdns解析
        this.httpdns = HttpDns.getService(context, "196726", "a15f1ebc31957ca226930ddd56965fd5");
        ArrayList<String> hostList = new ArrayList<>(Arrays.asList(UpdateHost, SassUpdateHost, SourceUpdateHost));
        httpdns.setPreResolveHosts(hostList);
        httpdns.setCachedIPEnabled(true);
        httpdns.setPreResolveAfterNetworkChanged(true);
        httpdns.setLogEnabled(true);
        // 允许返回过期的IP，通过设置允许返回过期的IP，配合异步查询接口，我们可以实现DNS懒更新策略
        httpdns.setExpiredIPEnabled(true);
    }


    public static OkHttpDns getInstance(Context context) {
        if (instance == null) {
            instance = new OkHttpDns(context);
        }
        return instance;
    }

    @Override
    public List<InetAddress> lookup(String hostname) throws UnknownHostException {
        //通过异步解析接口获取ip
        String ip = httpdns.getIpByHostAsync(hostname);
        LogUtils.tag("HttpDns").d("hostName = " + hostname + "; host:" + ip);

        if (ip != null) {
            //如果ip不为null，直接使用该ip进行网络请求
            List<InetAddress> inetAddresses = Arrays.asList(InetAddress.getAllByName(ip));
            LogUtils.tag("HttpDns").d("inetAddresses:" + inetAddresses);
            return inetAddresses;
        }
        if (hostname.contains(UpdateHost) || hostname.contains(SassUpdateHost) || hostname.contains(SourceUpdateHost)) {
            BugUtil.sendBug(new Throwable("HttpDns解析失败:" + "hostName = " + hostname + ";  ip:" + ip));
        }
        uploadAndroidDownloadEvent(hostname, "HttpDns解析失败:" + "hostName = " + hostname, AppUpdateManagerV2.ERROR_TYPE_DNS,false);
        //如果返回null，走系统DNS服务解析域名
        return Dns.SYSTEM.lookup(hostname);
    }

    public void uploadAndroidDownloadEvent(String downLoadUrl, String errorStr, String errorType, boolean isSuccessed) {
        //上传
        if (downLoadUrl.contains(UpdateHost) || downLoadUrl.contains(SassUpdateHost) || downLoadUrl.contains(SourceUpdateHost)) {
            HashMap<String, String> segmentMap = new HashMap<>();
            segmentMap.put("merchantId", SpUtil.getMerchantid());
            segmentMap.put("downloadUrl", downLoadUrl);
            if (!TextUtils.isEmpty(errorStr)){
                segmentMap.put("errorStr", StringUtil.subStrByStrAndLen(errorStr,250));
            }
            if (!TextUtils.isEmpty(errorType)){
                segmentMap.put("errorType", errorType);
            }
            segmentMap.put("result",isSuccessed?"success":"fail");
            //增加上报雪地埋点
            XyyIoUtil.track(AppUpdateManagerV2.ANDROID_DOWNLOAD_APK, segmentMap);
        }
    }
}
