package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartVoucher
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.ShopBasicInfoBean
import com.ybmmarketkotlin.bean.ShopListBean
import com.ybmmarketkotlin.bean.VoucherShopListTabBean
import retrofit2.http.*

interface ProductAddOnItemRequest {

    @FormUrlEncoded
    @POST("voucher/getMyVoucherListByTemplateInfo")
    suspend fun getPopCompanyDetail(@Field("merchantId") merchantId: String, @Field("templateIds") orgId: String): BaseBean<List<CouponInfoBean>>

    @FormUrlEncoded
    @POST("voucher/shopListTab")
    suspend fun getVoucherShopListTab(@Field("merchantId") merchantId: String, @Field("voucherTemplateId") orgId: String): BaseBean<VoucherShopListTabBean>


    @FormUrlEncoded
    @POST("cart/selectCartVoucher")
    suspend fun getCartVoucherBean(@Field("merchantId") merchantId: String, @Field("voucherTemplateId") orgId: String): BaseBean<CartVoucher>

    @FormUrlEncoded
    @POST("voucher/shopList")
    suspend fun getVoucherShopListBean(@FieldMap pare: Map<String, String>): BaseBean<ShopListBean>

}