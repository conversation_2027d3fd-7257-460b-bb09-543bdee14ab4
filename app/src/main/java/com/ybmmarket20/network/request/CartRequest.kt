package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.cart.CartBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.*

interface CartService {

    @FormUrlEncoded
    @POST("cart/v1")
    suspend fun getCartData(@Field("merchantId") merchantId: String): BaseBean<CartBean>?

    @FormUrlEncoded
    @POST("selectItem")
    suspend fun selecItem(@FieldMap pare: Map<String, String>): BaseBean<AbstractChangeCart>?

    @FormUrlEncoded
    @POST("cancelItem")
    suspend fun cancelItem(@FieldMap pare: Map<String, String>): BaseBean<AbstractChangeCart>?

    @FormUrlEncoded
    @POST("selectAllItem")
    suspend fun selecAllItem(@FieldMap pare: Map<String, String>): BaseBean<AbstractChangeCart>?

    @FormUrlEncoded
    @POST("cancelAllItem")
    suspend fun cancelAllItem(@FieldMap pare: Map<String, String>): BaseBean<AbstractChangeCart>?

    @FormUrlEncoded
    @POST("changeCart")
    suspend fun changeCart(@FieldMap pare: Map<String, String>): BaseBean<CartDataBean>?

    @FormUrlEncoded
    @POST("batchRemoveProductFromCart")
    suspend fun removeProductFromCart(
        @Field("merchantId") merchantId: String,
        @Field("packageIds") packageIds: String,
        @Field("ids") ids: String
    ): BaseBean<EmptyBean>?

    @FormUrlEncoded
    @POST("favorite/batchcollect")
    suspend fun batchcollect(
        @Field("products") ids: String
    ): BaseBean<EmptyBean>?


    @FormUrlEncoded
    @POST("voucher/receiveVoucher")
    suspend fun getVoucher(@FieldMap pare: Map<String, String>): BaseBean<EmptyBean>?

    @GET("marketing/rebateVoucher/consumption/startActTags")
    suspend fun getStartActTags(): BaseBean<RebateVoucherStartActInfoBean>

    @FormUrlEncoded
    @POST("order/v1/preSettle")
    suspend fun preSettle(@FieldMap pare: Map<String, String>): BaseBean<SettleBean>?
}

class CartRequest {

    /**
     *  获取支付成功页面头部信息
     */
    suspend fun getCartData(merchantId: String): BaseBean<CartBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).getCartData(merchantId)
    } catch (e: Exception) {
        BaseBean<CartBean>().initWithException(e)
    }

    /**
     *  选择item商品
     */
    suspend fun selectItem(paramMap: Map<String, String>): BaseBean<AbstractChangeCart>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).selecItem(paramMap)
    } catch (e: Exception) {
        BaseBean<AbstractChangeCart>().initWithException(e)
    }

    /**
     *  取消item商品
     */
    suspend fun cancelItem(paramMap: Map<String, String>): BaseBean<AbstractChangeCart>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).cancelItem(paramMap)
    } catch (e: Exception) {
        BaseBean<AbstractChangeCart>().initWithException(e)
    }

    /**
     *  选择Shop中所有商品
     */
    suspend fun selectAllShopItem(paramMap: Map<String, String>): BaseBean<AbstractChangeCart>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).selecAllItem(paramMap)
    } catch (e: Exception) {
        BaseBean<AbstractChangeCart>().initWithException(e)
    }

    /**
     *  取消Shop中所有商品
     */
    suspend fun cancelAllShopItem(paramMap: Map<String, String>): BaseBean<AbstractChangeCart>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).cancelAllItem(paramMap)
    } catch (e: Exception) {
        BaseBean<AbstractChangeCart>().initWithException(e)
    }

    /**
     *  取消Shop中所有商品
     */
    suspend fun changeCart(paramMap: Map<String, String>): BaseBean<CartDataBean>? = try {
        var mParam: HashMap<String, String> = HashMap(paramMap)

        NetworkService.instance.mRetrofit.create(CartService::class.java).changeCart(mParam)
    } catch (e: Exception) {
        BaseBean<CartDataBean>().initWithException(e)
    }

    /**
     *  从购物车中删除商品
     */
    suspend fun removeProductFromCart(merchantId: String, packageIds: String, ids: String): BaseBean<EmptyBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).removeProductFromCart(merchantId, packageIds, ids)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

    /**
     *  收藏商品
     */
    suspend fun batchcollect(ids: String): BaseBean<EmptyBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).batchcollect(ids)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

    /**
     *  领取优惠券
     */
    suspend fun getVoucher(paramMap: Map<String, String>): BaseBean<EmptyBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).getVoucher(paramMap)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

    /**
     *  领取优惠券
     */
    suspend fun preSettle(paramMap: Map<String, String>): BaseBean<SettleBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).preSettle(paramMap)
    } catch (e: Exception) {
        BaseBean<SettleBean>().initWithException(e)
    }


}



