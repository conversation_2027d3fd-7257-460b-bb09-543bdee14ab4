package com.ybmmarket20.view;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.Html;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.YbmCommandDecodeResult;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.YbmCommand;

/**
 * 一个底部边出来的popwindows 窗口容器
 * 药口令popwindows
 */

public class YbmCommandPopWindow {
    private PopupWindow popwindow;
    private View contentView;
    protected TextView ybmCommand;
    private String command;
    private String lastKey;
    private int lastCommandType;

    public YbmCommandPopWindow() {
        this.contentView = LayoutInflater.from(YBMAppLike.getAppContext()).inflate(R.layout.ybm_command_pop, null);
        popwindow = new PopupWindow(this.contentView,
                LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT, true);
        initPop();
        initView();
    }

    private void initView() {
        ybmCommand = (TextView) contentView.findViewById(R.id.tv_command);
        TextView wx = (TextView) contentView.findViewById(R.id.tv_to_wx);
        TextView tvTips = (TextView) contentView.findViewById(R.id.tv_tips);
        tvTips.setText(Html.fromHtml(BaseYBMApp.getApp().getCurrActivity().getString(R.string.text_ybm_command_tips)));
        TextView qq = (TextView) contentView.findViewById(R.id.tv_to_qq);
        TextView sms = (TextView) contentView.findViewById(R.id.tv_to_sms);
        wx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (TextUtils.isEmpty(command)) {
                    encodeCommand(lastKey, lastCommandType);
                    return;
                }
                YbmCommand.copyAndGo(command, YbmCommand.PLATFORM_WX);
            }
        });
        qq.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (TextUtils.isEmpty(command)) {
                    encodeCommand(lastKey, lastCommandType);
                    return;
                }
                YbmCommand.copyAndGo(command, YbmCommand.PLATFORM_QQ);
            }
        });
        sms.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (TextUtils.isEmpty(command)) {
                    encodeCommand(lastKey, lastCommandType);
                    return;
                }
                YbmCommand.copyAndGo(command, YbmCommand.PLATFORM_SMS);
            }
        });
        contentView.findViewById(R.id.btn_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    private void initPop() {
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#9494A6")));
        popwindow.setAnimationStyle(R.style.mypopwindow_anim_style);
        popwindow.setFocusable(true);
        popwindow.setOutsideTouchable(true);
        popwindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                backgroundAlpha(1f);
            }
        });
    }

    private void show(String str) {
        if (TextUtils.isEmpty(str)) {
            ToastUtils.showShort("药口令生成失败");
            return;
        }
        if (popwindow == null) {
            return;
        }
        try {
            if (popwindow.isShowing()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        command = str;
        ybmCommand.setText(str);
        backgroundAlpha(0.35f);
        try {
            popwindow.showAtLocation(BaseYBMApp.getApp().getCurrActivity().getWindow().getDecorView(), Gravity.BOTTOM, 0, 0);
        } catch (Exception e) {
            return;
        }
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }

    public void encodeCommand(String key, int commandType) {
        if (TextUtils.isEmpty(key)) {
            ToastUtils.showShort("生成药口令的关键字不能为空");
            return;
        }
        lastKey = key;
        lastCommandType = commandType;
        ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).showProgress();
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.ENCODECOMMAND).addParam("commandType", commandType + "").addParam("id", key).addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();
        HttpManager.getInstance().post(params, new BaseResponse<YbmCommandDecodeResult>() {

            @Override
            public void onSuccess(String content, BaseBean<YbmCommandDecodeResult> obj, YbmCommandDecodeResult ybmCommandDecodeResult) {
                ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
                if (obj != null && obj.isSuccess() && ybmCommandDecodeResult != null) {
                    show(ybmCommandDecodeResult.shareMessage);
                }
            }


            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                ((BaseActivity) BaseYBMApp.getApp().getCurrActivity()).dismissProgress();
            }
        });
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    private void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = (BaseYBMApp.getApp().getCurrActivity()).getWindow().getAttributes();
        lp.alpha = bgAlpha; //0.0-1.0
        BaseYBMApp.getApp().getCurrActivity().getWindow().setAttributes(lp);
    }
}
