package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.TextView
import androidx.annotation.StringRes
import com.ybmmarket20.R

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/9/19 14:17
 *    desc   :
 */
class CompanyNameInputView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val tvRequired: TextView
    private val tvTitle: TextView
    private val tvDesc: TextView
    private val editText: EditText

    init {
        LayoutInflater.from(context).inflate(R.layout.view_company_input, this, true)

        tvRequired = findViewById(R.id.tv_required)
        tvTitle = findViewById(R.id.tv_title)
        tvDesc = findViewById(R.id.tv_desc)
        editText = findViewById(R.id.et_content)

        val typedArray =  context.obtainStyledAttributes(attrs, R.styleable.CompanyNameInputView)
        try {
            val required = typedArray.getBoolean(R.styleable.CompanyNameInputView_isRequired,true)
            tvRequired.visibility = if (required) View.VISIBLE else View.GONE
            val title = typedArray.getString(R.styleable.CompanyNameInputView_title)
            if (title != null) {
                tvTitle.text = title
            }

            val desc = typedArray.getString(R.styleable.CompanyNameInputView_desc)
            if (desc != null) {
                tvDesc.text = desc
            }

            val value = typedArray.getString(R.styleable.CompanyNameInputView_editHint)
            if (value != null) {
                editText.setHint(value)
            }
        } finally {
            typedArray.recycle()
        }
    }


    fun setTitle(label: String) {
        tvTitle.setText(label)
    }


    fun setDesc(label: String) {
        tvDesc.text = label
    }

    fun getCompanyName(): String {
        return editText.text.toString().trim()
    }

    /**
     * 设置输入框文本
     */
    fun setCompanyName(companyName: String) {
        editText.setText(companyName)
    }

    /**
     * 设置输入框是否可编辑
     */
    fun setEditable(editable: Boolean) {
        editText.isFocusable = editable
        editText.isFocusableInTouchMode = editable
        editText.isEnabled = editable
        editText.isClickable = editable
    }

    /**
     * 设置输入框提示文本
     */
    fun setHint(@StringRes hintResId: Int) {
        editText.hint = context.getString(hintResId)
    }

    /**
     * 设置输入框提示文本（字符串）
     */
    fun setHint(hint: String) {
        editText.hint = hint
    }
}