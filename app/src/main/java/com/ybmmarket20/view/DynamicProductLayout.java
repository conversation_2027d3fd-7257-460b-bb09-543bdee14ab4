package com.ybmmarket20.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;

import com.ybm.app.view.WrapGridLayoutManager;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.adapter.ProductGrid3Adapter;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.home.BrandFragment;
import com.ybmmarket20.utils.RoutersUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * id 2001，20003,没有横向滑动功能，有倒计时功能
 * <p>
 * style 20 以下是单列 30 以下是双 40以下是三列 ,style 单数 不显示倒计时  双数 显示倒计时
 */
public class DynamicProductLayout extends BaseDynamicLayout<RowsBean> {

    private RecyclerView listView;
    private GoodsListAdapter adapter;
    private BrandFragment.MyItemDecoration itemDecoration;
    private int style;
    private int type;//type = 1 诊所单列样式
    private boolean isWhiteBg;

    public DynamicProductLayout(Context context) {
        super(context);
    }

    public DynamicProductLayout(Context context, int type) {
        super(context);
        this.type = type;
    }

    public DynamicProductLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicProductLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public int getDefBg() {
        return -1;
    }

    @Override
    public void initViews() {
        listView = (RecyclerView) findViewById(R.id.rv_list);
        itemDecoration = new BrandFragment.MyItemDecoration();
        listView.addItemDecoration(itemDecoration);
        listView.setNestedScrollingEnabled(false);
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_product;
    }

    @Override
    public void setItemData(List<RowsBean> items) {
        for (int i = 0; i < items.size(); i++) {
            if (getContext().getClass().getName().equals("com.ybmmarket20.home.MainActivity")) {
                items.get(i).zhugeEventName = XyyIoUtil.ACTION_HOME_PRODUCT;
            } else if (getContext().getClass().getName().equals("com.ybmmarket20.activity.ClinicActivity")) {
                items.get(i).zhugeEventName = XyyIoUtil.ACTION_CLINIC_PRODUCT;
            }
        }
        adapter.setNewData(items);
    }

    public void setStyle(int style) {
        if (type > 0) {
            adapter = new GoodsListAdapter(R.layout.home_product_item_type1, new ArrayList<RowsBean>());
            adapter.setEnableLoadMore(false);
            adapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
                @Override
                public void onItemClick(RowsBean rows) {
                    RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
                }
            });
            itemDecoration.setRow(1);
            listView.setLayoutManager(new WrapLinearLayoutManager(getContext()));
            listView.setAdapter(adapter);
            return;
        }
        if (style <= 0 && getContext() != null && getContext() instanceof MainActivity) {
            style = 32;
        }
        if (this.style == style && adapter != null && isWhiteBg == isWhiteBg()) {
            return;
        }
        this.style = style;
        if (style < 30) {//双列表或者单列
            adapter = new GoodsListAdapter(R.layout.item_goods, new ArrayList<RowsBean>());
            adapter.setEnableLoadMore(false);
            adapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
                @Override
                public void onItemClick(RowsBean rows) {
                    RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
                }
            });
        } else {//三列
            isWhiteBg = isWhiteBg();
            adapter = new ProductGrid3Adapter(isWhiteBg);
            adapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
                @Override
                public void onItemClick(RowsBean rows) {
                    RoutersUtils.open("ybmpage://productdetail/" + rows.getId());
                }
            });
        }
        listView.scrollToPosition(0);//滑动停止
        if (itemDecoration == null || adapter == null || listView == null) {
            return;
        }
        if (style < 20) {
            adapter.setlayoutResId(R.layout.item_goods);
            itemDecoration.setRow(1);
            listView.setLayoutManager(new WrapLinearLayoutManager(getContext()));
            listView.setAdapter(adapter);
        } else if (style < 30) {
            adapter.setlayoutResId(R.layout.detail_gridview_item);
            itemDecoration.setRow(2);
            listView.setLayoutManager(new WrapGridLayoutManager(getContext(), 2));
            listView.setAdapter(adapter);
        } else if (style < 40) {
            itemDecoration.setRow(0);
            listView.setLayoutManager(new WrapGridLayoutManager(getContext(), 3));
            listView.setAdapter(adapter);
        }
        adapter.notifyDataSetChanged();
    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(moduleView.bgRes) || "#ffffff".equals(moduleView.bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }

    @Override
    public void onRefresh() {
        super.onRefresh();
        if (adapter != null && adapter.getData() != null && !adapter.getData().isEmpty()) {
            if (adapter.getCurrPosition() > 0 && adapter.getCurrPosition() < adapter.getData().size()) {
                adapter.notifyItemChanged(adapter.getCurrPosition());
            }
        }
    }

}
