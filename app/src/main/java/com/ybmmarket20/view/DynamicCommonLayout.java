package com.ybmmarket20.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.LinearLayout;

import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.bean.ModuleView;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用动态模块，不带缓存功能
 */
public class DynamicCommonLayout extends LinearLayout implements IBaseDynamicLayout {
    protected List<ModuleView> moduleViewlist;
    protected List<BaseDynamicLayout> moduleViews;

    protected List<IBaseDynamicIntercept> intercepts = new ArrayList<>();

    public DynamicCommonLayout(Context context) {
        this(context, null);
    }

    public DynamicCommonLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DynamicCommonLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    public List<IBaseDynamicIntercept> getIntercepts() {
        return intercepts;
    }

    public void addIntercept(IBaseDynamicIntercept intercept) {
        if (intercepts == null) {
            intercepts = new ArrayList<>();
        }
        this.intercepts.add(intercept);
    }

    public boolean removeIntercept(IBaseDynamicIntercept intercept) {
        if (intercepts == null) {
            return true;
        }
        return this.intercepts.remove(intercept);
    }


    protected void initViews() {
        setOrientation(VERTICAL);
    }

    /**
     * 更新布局数据
     *
     * @param list 数据
     */
    public void updateData(List<ModuleView> list) {
        List<ModuleView> data = new ArrayList<>();
        if (list != null) {//先清理没有用的数据
            for (ModuleView moduleView : list)
                if (isError(moduleView)) {
                    continue;
                } else {
                    data.add(moduleView);
                }
        }
        //先进行数据比较
        if (moduleViews == null || moduleViews.size() <= 0) {
            setData(data);
            return;
        }
        if (moduleViewlist == null || moduleViewlist.size() <= 0) {
            setData(data);
            return;
        }
        if (data.size() > 0) {
            int index = -1;
            int index2 = -1;
            ModuleView view = null;
            for (int a = 0; a < data.size(); a++) {
                view = data.get(a);
                index = moduleViewlist.indexOf(view);
                if (index == a) {//位置相同，模块内容相同
                    if (moduleViewlist.get(index).isEquals(view)) {//数据相同，只要更新内容部分
                        moduleViews.get(index).updateData(view);
                    } else {//数据有修改，更新数据
                        moduleViews.get(index).bindData(view);
                    }
                    moduleViewlist.remove(index);
                } else {
                    if (index > 0 && index < a) {//相同模块，相同样式多次出现
                        index2 = moduleViewlist.lastIndexOf(view);
                        if (index2 > 0 && index2 > a) {
                            index = index2;
                        }
                    }
                    if (index > 0 && index > a) {//找到了但是位置不对,从老位置取出，删除，插入到指定位置
                        BaseDynamicLayout dynamicLayout = moduleViews.get(index);
                        this.removeView(dynamicLayout);
                        moduleViews.remove(index);
                        insertModule(a, dynamicLayout, view, moduleViewlist.get(index));
                        //更新集合中引用对象
                        moduleViewlist.remove(index);
                    } else {//没有找到，新建一个然后直接插入到指定位置
                        BaseDynamicLayout module = getModuleType(view.moduleId);
                        insertModule(a, module, view, null);
                    }
                }
                moduleViewlist.add(a, view);
            }
            if (moduleViews.size() > data.size()) {//有删除的对象了
                index = data.size();
                int len = moduleViews.size() - index;
                try {
                    moduleViews = moduleViews.subList(0, index);
                    moduleViewlist = moduleViewlist.subList(0, index);
                    this.removeViews(index, len);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {//直接清空全部内容
            this.removeAllViews();
            if (moduleViews != null) {
                moduleViews.clear();
            }
            if (moduleViewlist != null) {
                moduleViewlist.clear();
            }
            return;
        }
    }


    //清除cache
    protected void cleanCache() {

    }

    //检测id 是不是定义的id 如果不是就去掉
    private boolean isError(ModuleView moduleView) {
        if (moduleView != null && (!TextUtils.isEmpty(moduleView.api) || (moduleView.items != null && moduleView.items.size() > 0)) && isSupportId(moduleView.moduleId)) {
            return false;
        }
        return true;
    }

    //检测id 是不是定义的id 如果不是就去掉
    private boolean isSupportId(int moduleId) {
        if ((moduleId >= 1000 && moduleId <= 1009) || moduleId == 2100 || (moduleId >= 2000 && moduleId <= 2037)) {
            return true;
        }
        return false;
    }


    //插入新模块
    private void insertModule(int index, BaseDynamicLayout module, ModuleView news, ModuleView old) {
        try {
            if (intercepts != null && intercepts.size() > 0 && module != null) {
                module.setIntercepts(intercepts);
            }
            addView(module, index);
            moduleViews.add(index, module);
            if (old == null || !news.isEquals(old)) {
                module.bindData(news);
            } else if (news.equals(old)) {
                module.updateData(news);
            }
        } catch (IndexOutOfBoundsException e) {//清除缓存重新下载数据
            cleanCache();
            moduleViewlist.clear();
            moduleViews.clear();
            removeAllViews();
            BugUtil.sendBug(e);
        }
    }

    /**
     * 设置数据
     *
     * @param list 数据
     */
    protected void setData(List<ModuleView> list) {
        if (list == null || list.size() <= 0) {
            setVisibility(View.GONE);
            return;
        }
        List<ModuleView> data = new ArrayList<>();
        if (list != null) {//先清理没有用的数据
            for (ModuleView moduleView : list) {
                if (isError(moduleView)) {
                    continue;
                } else {
                    data.add(moduleView);
                }
            }
        }
        removeAllViews();
        if (moduleViewlist == null) {
            moduleViewlist = new ArrayList<>();
        } else {
            moduleViewlist.clear();
        }
        moduleViewlist.addAll(data);
        setVisibility(View.VISIBLE);
        BaseDynamicLayout module = null;
        moduleViews = new ArrayList<>();
        ModuleView view = null;
        for (int a = 0; a < data.size(); a++) {
            view = moduleViewlist.get(a);
            module = getModuleType(view.moduleId);
            if (module != null) {
                insertModule(a, module, view, null);
            }
        }

    }

    public <T extends BaseDynamicLayout> T getModuleType(int moduleId) {
        switch (moduleId) {
            case 1000:
                return (T) new DynamicBannerLayout(getContext());
            case 1001:
                return (T) new DynamicShortcutLayout(getContext());
            case 1002:
                return (T) new DynamicMarqueeLayout(getContext());
            case 1003:
                return (T) new DynamicImageLayout(getContext());
            case 1004:
                return (T) new DynamicBannerLayout2(getContext());
            case 1005:
                return (T) new DynamicImageListLayout(getContext());
            case 1009:
                return (T) new DynamicImagePagerLayout(getContext());
            case 2000:
                return (T) new DynamicCheapLayout(getContext());
            case 2001:
                return (T) new DynamicProductLayout(getContext());
            case 2002:
                return (T) new DynamicComboLayout(getContext());
            case 2003:
                return (T) new DynamicProductLayout(getContext(), 1);
            case 2005:
                return (T) new DynamicSlideItemPagerLayout(getContext());
            case 2006:
                return (T) new DynamicProductMultiLayout(getContext());
            case 2037:
                return (T) new DynamicTabProductLayout(getContext());
            case 2100:
                return (T) new DynamicSeckillLayout(getContext());
        }
        return null;
    }


    @Override
    public void onResume() {
        if (moduleViews != null) {
            for (BaseDynamicLayout layout : moduleViews) {
                layout.onResume();
            }
        }
    }

    @Override
    public void onPause() {
        if (moduleViews != null) {
            for (BaseDynamicLayout layout : moduleViews) {
                layout.onPause();
            }
        }
    }

    @Override
    public void onStop() {
        if (moduleViews != null) {
            for (BaseDynamicLayout layout : moduleViews) {
                layout.onStop();
            }
        }
    }

    @Override
    public void onRefresh() {
        if (moduleViews != null) {
            for (BaseDynamicLayout layout : moduleViews) {
                layout.onRefresh();
            }
        }
    }

    @Override
    public void onDestroy() {
        if (moduleViews != null) {
            for (BaseDynamicLayout layout : moduleViews) {
                layout.onDestroy();
            }
        }
    }

}
