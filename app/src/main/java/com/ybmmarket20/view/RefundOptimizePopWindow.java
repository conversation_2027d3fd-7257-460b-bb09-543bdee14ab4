package com.ybmmarket20.view;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

public class RefundOptimizePopWindow extends BaseBottomPopWindow {

    RecyclerView rvRefund;
    RefundOptimizeAdapter refundOptimizeAdapter;
    TextView tv_btn_ok;
    ImageView iv_clear;

    private int selectedPosition = 0;
    private ArrayList<SearchFilterBean> reasons;


    public RefundOptimizePopWindow(ArrayList<SearchFilterBean> reasons) {
        this.reasons = reasons;
        initData();
    }

    private void initData() {
        rvRefund = getView(R.id.rv_refund);
        tv_btn_ok = getView(R.id.tv_btn_ok);
        iv_clear = getView(R.id.iv_clear);
        refundOptimizeAdapter = new RefundOptimizeAdapter(R.layout.item_refund_optimize, reasons);
        rvRefund.setLayoutManager(new LinearLayoutManager(rvRefund.getContext()));
        rvRefund.setAdapter(refundOptimizeAdapter);

        tv_btn_ok.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (mOnSelectListener != null) {
                    mOnSelectListener.getValue(reasons.get(selectedPosition));
                }
                dismiss();

            }
        });

        iv_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    @Override
    protected int getLayoutId() {
        return R.layout.refund_optimize_pop;
    }

    @Override
    protected void initView() {
    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight() - ConvertUtils.dp2px(268));
    }

    class RefundOptimizeAdapter extends YBMBaseAdapter<SearchFilterBean> {

        public RefundOptimizeAdapter(int layoutResId, List<SearchFilterBean> data) {
            super(layoutResId, data);
        }

        @Override
        protected void bindItemView(YBMBaseHolder baseViewHolder, SearchFilterBean s) {
            baseViewHolder.setText(R.id.tv_str, s.realName);
            baseViewHolder.setChecked(R.id.cb_item, s.isSelected);
            ConstraintLayout rb = baseViewHolder.getView(R.id.rb_01);
            rb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    reasons.get(baseViewHolder.getAdapterPosition()).isSelected = true;
                    selectedPosition = baseViewHolder.getAdapterPosition();
                    for (int i = 0; i < reasons.size(); i++) {
                        if (i != selectedPosition) {
                            reasons.get(i).isSelected = false;
                        }
                    }
                    notifyDataSetChanged();
                }
            });


        }
    }
}
