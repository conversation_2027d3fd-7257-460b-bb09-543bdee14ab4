//package com.ybmmarket20.view.taggroupview
//
//import android.content.Context
//import android.util.AttributeSet
//
//class WrapperTagContainerLayout constructor(context: Context, val attr: AttributeSet?, val defStyleAttr: Int) :
//    TagContainerLayout(context, attr, defStyleAttr) {
//
//    constructor(context: Context, attr: AttributeSet?) : this(context, attr, 0)
//    constructor(context: Context) : this(context, null, 0)
//
//    private var isExpand = false
//    private var isAddExpand = false
//
//    /**
//     * 获取最后view所在的行
//     */
//    private fun getLastViewLine(): Pair<Int, Int>?{
//        if (mViewPos == null) return null
//        var childTop = Integer.MAX_VALUE
//        var line = 0
//        mViewPos?.filterIndexed{ index, _ ->
//            index % 2 == 1
//        }?.forEach{
//            if (it != childTop) {
//                childTop = it
//                line ++
//            }
//            if (line == maxLines) {
//                val lastPositionInLine = mViewPos.indexOfLast { it == childTop }
//                return Pair(line, (lastPositionInLine - 1)/2)
//            }
//        }
//        return Pair(line, (mViewPos.size-1)/2)
//    }
//
//    /**
//     * 设置最大行数并设置成可扩展
//     */
//    fun setMaxLinesWithExpand(maxLines: Int) {
//        mMaxLines = maxLines
//        isExpand = false
//        isAddExpand = false
//        postInvalidate()
//    }
//
//    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
//        super.onLayout(changed, l, t, r, b)
//        handleExpand()
//    }
//
//    private fun handleExpand() {
//        if (isExpand || isAddExpand || childCount == 0) return
//        val result = getLastViewLine() ?: return
//        if (result.first == mMaxLines) {
//            val lastView = getChildAt(result.second)
//            val remainWidth = measuredWidth - lastView.right - horizontalInterval
//            mTags = mTags.subList(0, if(remainWidth > lastView.measuredHeight) result.second else result.second-1)
//        }
//        mTags.add("")
//        isAddExpand = true
//        tags = mTags
//        requestLayout()
//    }
//}