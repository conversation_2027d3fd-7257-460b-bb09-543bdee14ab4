package com.ybmmarket20.view.jdaddressselector;


import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.Province;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.List;


public class DefaultAddressProvider implements AddressProvider {
    @Override
    public void provideProvinces(final AddressReceiver<Province> addressReceiver) {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                .addParam("parentId", "0").build();
        HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                if (bean == null || !bean.isSuccess()) {
                    return;
                }
                List<Province> provinces = bean.data;
                if (provinces != null && provinces.size() > 0) {
                    addressReceiver.send(provinces);
                }
            }
        });
    }

    @Override
    public void provideCitiesWith(String provinceId, final AddressReceiver<Province> addressReceiver) {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                .addParam("parentId", "" + provinceId).build();
        HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                if (bean == null || !bean.isSuccess()) {
                    return;
                }
                List<Province> provinces = bean.data;
                if (provinces != null && provinces.size() > 0) {
                    addressReceiver.send(provinces);
                }
            }
        });
    }

    @Override
    public void provideCountiesWith(String cityId, final AddressReceiver<Province> addressReceiver) {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                .addParam("parentId", "" + cityId).build();
        HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                if (bean == null || !bean.isSuccess()) {
                    return;
                }
                List<Province> provinces = bean.data;
                if (provinces != null && provinces.size() > 0) {
                    addressReceiver.send(provinces);
                }
            }
        });
    } @Override
    public void provideStreetsWith(String countyId, AddressReceiver<Province> addressReceiver) {
        //  2019/11/14 优化省市区街道
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                .addParam("parentId", "" + countyId).build();
        HttpManager.getInstance().post(params, new BaseResponse<List<Province>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<Province>> bean, List<Province> data) {
                if (bean == null || !bean.isSuccess()) {
                    return;
                }
                List<Province> provinces = bean.data;
                addressReceiver.send(provinces);
            }
        });
    }
}
