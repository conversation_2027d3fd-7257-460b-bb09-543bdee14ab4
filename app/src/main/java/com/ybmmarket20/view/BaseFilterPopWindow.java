package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.UiUtils;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.POPActivity;
import com.ybmmarket20.bean.SearchFilterBean;


/**
 * 搜索筛选基类 完成弹出收起，选择
 */
public abstract class BaseFilterPopWindow {

    protected PopupWindow popwindow;
    protected OnSelectListener mOnSelectListener;
    protected LinearLayout contentView;
    protected View view;
    protected StringBuilder multiSelectedSpecStr;

    public BaseFilterPopWindow() {
        init();
        initView();
    }

    private void init() {
        if (BaseYBMApp.getApp().getCurrActivity() == null) {
            return;
        }
        LayoutInflater inflater = (LayoutInflater) BaseYBMApp.getApp().getCurrActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        contentView = (LinearLayout) inflater.inflate(R.layout.pop_layout_base_filter, null, false);
        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        view = inflater.inflate(getLayoutId(), null, false);
        if (isSetWeight()) {
            contentView.addView(view, 0, getLayoutParams());
        } else {
            contentView.addView(view, 0);
        }
    }

    protected boolean isSetWeight() {
        return true;
    }

    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0, 6);
    }

    public void setLayoutParams(LinearLayout.LayoutParams params) {
        if (view != null) {
            view.setLayoutParams(params);
        }
    }

    public void setLayoutParams(float weigth) {
        if (weigth < 0) {
            return;
        }
        if (view != null) {
            view.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 0, weigth));
        }
    }

    public void setLayoutParams(int height) {
        if (height <= 0) {
            return;
        }
        if (view != null) {
            view.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height));
        }
    }

    protected abstract int getLayoutId();

    protected abstract void initView();

    public void setOnSelectListener(OnSelectListener onSelectListener) {
        mOnSelectListener = onSelectListener;
    }

    public interface OnSelectListener {
        void getValue(SearchFilterBean show);

        void OnDismiss(String multiSelectStr);

        default void onConfirm(String multiSelectStr){

        }

        default void onShopDismiss(String shopCodeString,String shopNameString){

        }
    }

    protected void onPreDismiss() {

    }

    private void initPop(View token) {
        // 刘海屏会出现阴影高度问题，此处的高度设置没有适配刘海屏
        int height = UiUtils.getPopupWindowHeight(token);
        popwindow = new PopupWindow(contentView, UiUtils.getScreenWidth(), height, true);
        popwindow.setBackgroundDrawable(getPopWindowBackgroundDrawable());
        popwindow.setFocusable(true);
        popwindow.setOutsideTouchable(true);
        popwindow.setOnDismissListener(() -> {
            if (mOnSelectListener != null) {
                onPreDismiss();
                if (multiSelectedSpecStr!=null){
                    mOnSelectListener.OnDismiss(multiSelectedSpecStr.toString());
                }else {
                    mOnSelectListener.OnDismiss("");
                }
            }
        });
        popwindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }

    public void setMultiSelectedSpecStr(String selectedSpecStr) {
        multiSelectedSpecStr = new StringBuilder(selectedSpecStr);
    }

    /**
     * 设置阴影背景色
     *
     * @return
     */
    protected Drawable getPopWindowBackgroundDrawable(){
        return new ColorDrawable(Color.parseColor("#99222222"));
    }

    public <T extends View> T getView(int viewId) {
        if (contentView == null) {
            return null;
        }
        try {
            return (T) contentView.findViewById(viewId);
        } catch (Throwable e) {
            return null;
        }
    }

    public void show(View token) {
        if (popwindow == null) {
            initPop(token);
        }
        if (popwindow == null) {
            return;
        }
        try {
            if (isShow()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        try {
            popwindow.showAsDropDown(token, 0, 0);

        } catch (Exception e) {
            return;
        }
        popwindow.update();
    }

    public void showAtLocation(View token, int xOffset, int yOffset) {
        if (popwindow == null) {
            initPop(token);
        }
        if (popwindow == null) {
            return;
        }
        try {
            if (isShow()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        try {
            popwindow.showAtLocation(token, Gravity.NO_GRAVITY, xOffset, yOffset);
        } catch (Exception e) {
            return;
        }
        popwindow.update();
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }
}
