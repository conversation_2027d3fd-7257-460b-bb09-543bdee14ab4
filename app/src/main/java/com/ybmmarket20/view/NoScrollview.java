package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Rect;
import androidx.core.view.MotionEventCompat;
import androidx.core.widget.NestedScrollView;
import android.util.AttributeSet;
import android.view.MotionEvent;

/**
 * 不自动滑动的scrollview
 */
public class NoScrollview extends NestedScrollView  {


    private int mScrollPointerId;
    private int mInitialTouchX;
    private int mInitialTouchY;
    private boolean isUser = false;
    public NoScrollview(Context context) {
        super(context);
    }

    public NoScrollview(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NoScrollview(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    //<editor-fold desc="add scrollChangeListener">
    @Override
    protected void onScrollChanged(int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
        super.onScrollChanged(scrollX,scrollY, oldScrollX, oldScrollY);
        if(mScrollListener !=null){
            mScrollListener.onScrollChanged(scrollX,scrollY,oldScrollX,oldScrollY,isUser);
        }
    }

    private OnScrollListener mScrollListener;

    public void setOnScrollListener(OnScrollListener onScrollListener) {
        this.mScrollListener = onScrollListener;
    }


    /**
     * 滚动监听
     *
     * <AUTHOR>
     * @DateTime 2015年11月30日
     */
    public interface OnScrollListener {
        public void onScrollChanged(int scrollX, int scrollY, int oldScrollX, int oldScrollY,boolean isUser);
    }
    //</editor-fold>


    @Override
    protected int computeScrollDeltaToGetChildRectOnScreen(Rect rect) {
        return 0;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        final int action = MotionEventCompat.getActionMasked(e);
        final int actionIndex = MotionEventCompat.getActionIndex(e);

        switch (action) {
            case MotionEvent.ACTION_DOWN:
                isUser = true;
                mScrollPointerId = MotionEventCompat.getPointerId(e, 0);
                mInitialTouchX = (int) (e.getX() + 0.5f);
                mInitialTouchY = (int) (e.getY() + 0.5f);
                return super.onInterceptTouchEvent(e);
            case MotionEventCompat.ACTION_POINTER_DOWN:
                isUser = true;
                mScrollPointerId = MotionEventCompat.getPointerId(e, actionIndex);
                mInitialTouchX = (int) (MotionEventCompat.getX(e, actionIndex) + 0.5f);
                mInitialTouchY = (int) (MotionEventCompat.getY(e, actionIndex) + 0.5f);
                return super.onInterceptTouchEvent(e);
            case MotionEvent.ACTION_MOVE: {
                isUser = true;
                final int index = MotionEventCompat.findPointerIndex(e, mScrollPointerId);
                if (index < 0) {
                    return false;
                }
                final int x = (int) (MotionEventCompat.getX(e, index) + 0.5f);
                final int y = (int) (MotionEventCompat.getY(e, index) + 0.5f);
                final int dx = x - mInitialTouchX;
                final int dy = y - mInitialTouchY;
                if (Math.abs(dx) >= Math.abs(dy)) {
                    return false;
                }
                return super.onInterceptTouchEvent(e);
            }
            case  MotionEvent.ACTION_CANCEL:
            case  MotionEvent.ACTION_UP:
                isUser = false;
                return super.onInterceptTouchEvent(e);
            default:
                return super.onInterceptTouchEvent(e);
        }
    }
}
