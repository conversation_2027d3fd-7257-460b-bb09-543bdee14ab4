package com.ybmmarket20.view.cms

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.BuildConfig
import com.ybmmarket20.R
import com.ybmmarket20.adapter.SearchStartRecommendAdapter
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.SearchStartRecommendViewModel
import kotlinx.android.synthetic.main.view_search_start_recommend.view.*
import org.json.JSONObject

/**
 * 搜索启动页热卖排行榜
 */
class SearchStartRecommendView(context: Context, attrs: AttributeSet?):
    ConstraintLayout(context, attrs) {

        init {
            initData()
        }

    fun initData() {
        if (context is ComponentActivity) {
            val rootView = View.inflate(context, R.layout.view_search_start_recommend, this)
            val activity: ComponentActivity = context as ComponentActivity
            val viewModel by activity.viewModels<SearchStartRecommendViewModel>()
            viewModel.getLiveData().observe(activity, Observer {
                if (it?.rows == null || it.rows!!.isEmpty()) {
                    visibility = View.GONE
                    return@Observer
                } else visibility = View.VISIBLE
                val flowData = FlowData.withInit().apply {
                    spType = it.sptype
                    spId = it.spid
                    sId = it.sid
                }
                val rv = rootView.findViewById<RecyclerView>(R.id.rv_search_start_recommend)
                rv.layoutManager = WrapLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
                val adapter = SearchStartRecommendAdapter(it.rows?: listOf(), flowData)
                rv.adapter = adapter
            })
            viewModel.getSearchStartRecommendData()
        }
        tv_search_start_recommend_more.setOnClickListener {
            val action = if (BuildConfig.DEBUG) {
                "ybmpage://commonh5activity?cache=0&url=http://app-new.stage.ybm100.com/newstatic/#/hotRanking/index"
            } else {
                "ybmpage://commonh5activity?cache=0&url=https://app-v4.ybm100.com/newstatic/#/hotRanking/index"
            }
            RoutersUtils.open(action)
            XyyIoUtil.track("action_SearchStartup_chart_Click", JSONObject().also {
                it.put("name", "更多")
                it.put("action", action)
            })
        }
    }
}