package com.ybmmarket20.view.cms;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.view.IBaseDynamicIntercept;
import com.ybmmarket20.view.IBaseDynamicLayout;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 通用动态模块，不带缓存功能
 */
public class DynamicCommonLayoutCms extends LinearLayout implements IBaseDynamicLayout {
    protected List<ModuleBeanCms> moduleBeanlist;
    protected List<BaseDynamicLayoutCms> moduleViews;

    protected List<IBaseDynamicIntercept> intercepts = new ArrayList<>();

    public DynamicCommonLayoutCms(Context context) {
        this(context, null);
    }

    public DynamicCommonLayoutCms(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DynamicCommonLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    public List<IBaseDynamicIntercept> getIntercepts() {
        return intercepts;
    }

    public void addIntercept(IBaseDynamicIntercept intercept) {
        if (intercepts == null) {
            intercepts = new ArrayList<>();
        }
        this.intercepts.add(intercept);
    }

    public boolean removeIntercept(IBaseDynamicIntercept intercept) {
        if (intercepts == null) {
            return true;
        }
        return this.intercepts.remove(intercept);
    }


    protected void initViews() {
        setOrientation(VERTICAL);
    }

    /**
     * 更新布局数据
     *
     * @param list 数据
     */
    public void updateData(List<ModuleBeanCms> list) {
        List<ModuleBeanCms> data = new ArrayList<>();
        // 1. 先清理没有用的数据
        if (list != null) {
            for (ModuleBeanCms moduleView : list) {
                if (!isError(moduleView)) {
                    data.add(moduleView);
                }
            }
        }
        // 2. 如果是第一次加载，直接设置
        if (moduleViews == null || moduleViews.size() == 0) {
            setData(data);
            return;
        }
        if (moduleBeanlist == null || moduleBeanlist.size() == 0) {
            setData(data);
            return;
        }
        // 3. 非第一次加载，比对数据后更新
        if (data.size() > 0) {
            int existedModuleIndex = -1;
            int index2 = -1;
            ModuleBeanCms moduleBean = null;

            for (int a = 0; a < data.size(); a++) {
                moduleBean = data.get(a);
                if (a < moduleBeanlist.size()) {
                    int oldIndex = moduleBeanlist.subList(a, moduleBeanlist.size()).indexOf(moduleBean);
                    if (oldIndex >= 0) {
                        existedModuleIndex = a + oldIndex;
                    }else {
                        existedModuleIndex = -1;
                    }
                } else {
                    existedModuleIndex = -1;
                }

                if (existedModuleIndex == a) { // 位置相同，模块相同
                    if (moduleBeanlist.get(existedModuleIndex).isEquals(moduleBean)) {
                        // 模块相同，且样式相同，只需更新数据
                        //LogUtils.e("xyd", "模块相同，且样式相同，只需更新数据 :" + a);
                        moduleViews.get(existedModuleIndex).updateData(moduleBean);
                    } else {
                        // 模块相同，样式不同，需要重新绑定数据
                        //Log.e("xyd", "模块相同，样式不同 :" + a);
                        moduleViews.get(existedModuleIndex).updateExistedModuleStyle(moduleBean);
                    }
                    moduleBeanlist.remove(existedModuleIndex);
                } else {
                    //todo 这里逻辑可以优化，如果出现多个相同模块，没有最大化实现复用。
                    if (existedModuleIndex > 0 && existedModuleIndex > a) { //找到了但是位置不对,从老位置取出，删除，插入到指定位置
                        BaseDynamicLayoutCms dynamicLayout = moduleViews.get(existedModuleIndex);
                        this.removeView(dynamicLayout);
                        moduleViews.remove(existedModuleIndex);
                        insertModule(a, dynamicLayout, moduleBean, moduleBeanlist.get(existedModuleIndex));
                        //更新集合中引用对象
                        moduleBeanlist.remove(existedModuleIndex);
                    } else {//没有找到，新建一个然后直接插入到指定位置
                        BaseDynamicLayoutCms module = getModuleType(moduleBean.name);
                        insertModule(a, module, moduleBean, null);
                    }
                }
                moduleBeanlist.add(a, moduleBean);
            }


            if (moduleViews.size() > data.size()) {//有删除的对象了
                //todo view remove之后，置空一下是不是更好。
                existedModuleIndex = data.size();
                int len = moduleViews.size() - existedModuleIndex;
                try {
                    moduleViews = moduleViews.subList(0, existedModuleIndex);
                    moduleBeanlist = moduleBeanlist.subList(0, existedModuleIndex);
                    this.removeViews(existedModuleIndex, len);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {//直接清空全部内容
            this.removeAllViews();
            if (moduleViews != null) {
                moduleViews.clear();
            }
            if (moduleBeanlist != null) {
                moduleBeanlist.clear();
            }
            return;
        }
    }

    //清除cache
    public void cleanCache() {
        if (moduleBeanlist!=null){
            moduleBeanlist.clear();
        }
        if (moduleViews!=null){
            moduleViews.clear();
        }
        removeAllViews();
    }

    //检测id 是不是定义的id 如果不是就去掉   false 支持的 true 不支持的
    public boolean isError(ModuleBeanCms moduleView) {
        if (moduleView != null
                && ((moduleView.content != null))
                && isSupportId(moduleView.name)) {
            return false;
        }
        return true;
    }

    //检测id 是不是定义的id 如果不是就去掉
    private boolean isSupportId(String name) {
        if (name.equals(ModuleBeanCms.SEARCHBOX)
                || name.equals(ModuleBeanCms.FASTENTRY)
                || name.equals(ModuleBeanCms.BRAND_H)
                || name.equals(ModuleBeanCms.HEADLINE)
                || name.equals(ModuleBeanCms.STREAMER)
                || name.equals(ModuleBeanCms.ADONE)
                || name.equals(ModuleBeanCms.ADTWO)
                || name.equals(ModuleBeanCms.ADTHREE)
                || name.equals(ModuleBeanCms.ADFOUR)
                || name.equals(ModuleBeanCms.ONEPLUSTWO)
                || name.equals(ModuleBeanCms.ONEPLUSTHREE)
                || name.equals(ModuleBeanCms.ONEPLUSFOUR)
                || name.equals(ModuleBeanCms.TWOPLUSTWO)
                || name.equals(ModuleBeanCms.MOREACTIVE)
                || name.equals(ModuleBeanCms.WONDERACTIVE)
                || name.equals(ModuleBeanCms.PRODUCTEXHIBITION)
                || name.equals(ModuleBeanCms.FLOORSPACING)
                || name.equals(ModuleBeanCms.RECOMMENDLIST)
                || name.equals(ModuleBeanCms.SECKILL)

        ) {
            return true;
        }
        return false;
    }


    // 插入新模块
    private void insertModule(int index, BaseDynamicLayoutCms module, ModuleBeanCms news, ModuleBeanCms old) {
        try {
            if (intercepts != null && intercepts.size() > 0 && module != null) {
                module.setIntercepts(intercepts);
            }
            addView(module, index);
            moduleViews.add(index, module);
            if (old == null || !news.isEquals(old)) {
                module.bindData(news);
            } else if (news.isEquals(old)) {
                // todo 这里如果数据完全一样，可以不做任何更新, isEquals 方法需要改进
                module.updateData(news);
            }
        } catch (IndexOutOfBoundsException e) {//清除缓存重新下载数据
            cleanCache();
            moduleBeanlist.clear();
            moduleViews.clear();
            removeAllViews();
            BugUtil.sendBug(e);
        }
    }

    /**
     * 第一次加载的时候，直接设置数据
     *
     * @param list 数据
     */
    protected void setData(List<ModuleBeanCms> list) {
        if (list == null || list.size() == 0) {
            setVisibility(View.GONE);
            return;
        }
        List<ModuleBeanCms> data = new ArrayList<>();
        // 先清理没有用的数据
        for (ModuleBeanCms moduleBean : list) {
            if (!isError(moduleBean)) {
                data.add(moduleBean);
            }
        }

        moduleBeanlist = new LinkedList<>();
        moduleBeanlist.addAll(data);
        setVisibility(View.VISIBLE);
        BaseDynamicLayoutCms moduleView = null;
        moduleViews = new LinkedList<>();
        ModuleBeanCms bean = null;
        for (int a = 0; a < moduleBeanlist.size(); a++) {
            bean = moduleBeanlist.get(a);
            moduleView = getModuleType(bean.name);
            if (moduleView != null) {
                insertModule(a, moduleView, bean, null);
            }
        }

    }

    public <T extends BaseDynamicLayoutCms> T getModuleType(String name) {
        switch (name) {
            case ModuleBeanCms.SEARCHBOX:
                return (T) new DynamicHotKeyAndBannerLayoutCms(getContext());
            case ModuleBeanCms.FASTENTRY:
                return (T) new DynamicShortcutLayoutCms(getContext());
            case ModuleBeanCms.HEADLINE:
                return (T) new DynamicMarqueeLayoutCms(getContext());
            case ModuleBeanCms.STREAMER:
            case ModuleBeanCms.ADONE:
            case ModuleBeanCms.ADTWO:
            case ModuleBeanCms.ADTHREE:
            case ModuleBeanCms.ADFOUR:
            case ModuleBeanCms.ONEPLUSTWO:
            case ModuleBeanCms.ONEPLUSTHREE:
            case ModuleBeanCms.ONEPLUSFOUR:
            case ModuleBeanCms.TWOPLUSTWO:
                return (T) new DynamicImageLayoutCms(getContext());
            case ModuleBeanCms.BRAND_H:
                return (T) new DynamicImageListLayoutCms(getContext());
            case ModuleBeanCms.PRODUCTEXHIBITION:
                return (T) new DynamicProductMultiLayoutCms(getContext());
            case ModuleBeanCms.RECOMMENDLIST:
                return (T) new DynamicSlideItemPagerLayoutCms(getContext());
            case ModuleBeanCms.FLOORSPACING:
                return (T) new DynamicFloorSpacingLayout(getContext());
            case ModuleBeanCms.MOREACTIVE:
                return (T) new DynamicMoreActiveLayoutCms(getContext());
            case ModuleBeanCms.WONDERACTIVE:
                return (T) new DynamicWonderActiveLayoutCms(getContext());
            case ModuleBeanCms.SECKILL:
                return (T) new DynamicSeckillLayoutCms(getContext());
        }
        return null;
    }


    @Override
    public void onResume() {
        if (moduleViews != null) {
            for (BaseDynamicLayoutCms layout : moduleViews) {
                layout.onResume();
            }
        }
    }

    @Override
    public void onPause() {
        if (moduleViews != null) {
            for (BaseDynamicLayoutCms layout : moduleViews) {
                layout.onPause();
            }
        }
    }

    @Override
    public void onStop() {
        if (moduleViews != null) {
            for (BaseDynamicLayoutCms layout : moduleViews) {
                layout.onStop();
            }
        }
    }

    @Override
    public void onRefresh() {
        if (moduleViews != null) {
            for (BaseDynamicLayoutCms layout : moduleViews) {
                layout.onRefresh();
            }
        }
    }

    @Override
    public void onDestroy() {
        if (moduleViews != null) {
            for (BaseDynamicLayoutCms layout : moduleViews) {
                layout.onDestroy();
            }
        }
    }

}
