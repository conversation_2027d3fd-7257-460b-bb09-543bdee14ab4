package com.ybmmarket20.view.cms;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.cms.ModuleBeanCms;
import com.ybmmarket20.bean.cms.ModuleItemHeadlineBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.ImageUtil;

import java.util.List;

/**
 * 动态头条轮播布局
 */
public class DynamicMarqueeLayoutCms extends BaseDynamicLayoutCms<ModuleItemHeadlineBean> {
    private LinearLayout llItem;
    private ImageView ivMarqueeTitle;
    private MarqueeViewCms marquee_view;
    private List<ModuleItemHeadlineBean> data;

    public DynamicMarqueeLayoutCms(Context context) {
        this(context, null);
    }

    public DynamicMarqueeLayoutCms(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DynamicMarqueeLayoutCms(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        llItem = findViewById(R.id.ll_item);
        ivMarqueeTitle = findViewById(R.id.iv_title);
        marquee_view = findViewById(R.id.marquee_view);
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_marquee_cms;
    }

    /**
     * @param data 数据
     */
    @Override
    public void setItemData(ModuleBeanCms moduleBean, List<ModuleItemHeadlineBean> data, boolean isUpdate) {

        if (isUpdate){
            this.data = data;
            ImageUtil.load(getContext(), ImageUtil.getImageUrl(moduleBean.content.icon), ivMarqueeTitle);
            if (!TextUtils.isEmpty(moduleBean.content.inside_bgRes)) {
                setNetBackground(llItem, moduleBean.content.inside_bgRes);
            }
            marquee_view.startWithList(data, 0);
        }

    }

    @Override
    public void setStyle(int style) {

    }

    @Override
    public void setImageView(ImageView view, ModuleItemHeadlineBean bean) {

    }

    @Override
    public boolean needUpdateItem(ModuleBeanCms<ModuleItemHeadlineBean> moduleBean, List<ModuleItemHeadlineBean> data) {

        if (content.icon!=null && !content.icon.equals(moduleBean.content.icon)){
            ImageUtil.load(getContext(), ImageUtil.getImageUrl(moduleBean.content.icon), ivMarqueeTitle);
        }

        if (content.inside_bgRes!=null && !content.inside_bgRes.equals(moduleBean.content.inside_bgRes)){
            if (!TextUtils.isEmpty(moduleBean.content.inside_bgRes)) {
                setNetBackground(llItem, moduleBean.content.inside_bgRes);
            }
        }

        boolean keepOld = true;
        if (this.content!=null && moduleBean.content!=null && this.content.list!=null && moduleBean.content.list!=null && this.content.list.size() == moduleBean.content.list.size()){
            for (int a = 0; a< content.list.size();a++){
                if (!this.moduleBean.content.list.get(a).getContent().equals(data.get(a).getContent())) {
                    keepOld = false;
                    break;
                }
            }
        }else {
            keepOld = false;
        }
        return !keepOld;
    }
}
