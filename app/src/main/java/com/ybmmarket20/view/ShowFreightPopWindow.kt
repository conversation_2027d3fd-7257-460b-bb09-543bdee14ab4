package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.cart.CartBean
import com.ybmmarket20.bean.cart.FreightInfoBeanWraper
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.home.CartFragmentV3
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

class ShowFreightPopWindow {

    var context: Context? = null

    val tvTitle: TextView? = null
    var ivClose: ImageView? = null
    var tvContent: TextView? = null
    var btnLeft: TextView? = null
    var btnRight: TextView? = null
    var btnGroup: Group? = null
    var btnOnly: TextView? = null
    var rvfreight: RecyclerView? = null
    var freightAdapter: YBMBaseAdapter<*>? = null
    var mListener: OnButtonClickListener? = null

    var popwindow: PopupWindow? = null
    var contentView: View? = null
    var cartBean: CartBean? = null
    lateinit var dataList: MutableList<FreightInfoBeanWraper>

    var notSubmitOrderOrgIds: MutableList<String> = mutableListOf()

    constructor(context: Context) : super() {
        this.context = context
        initDadaAndView()
    }

    private fun initDadaAndView() {

        val inflater = BaseYBMApp.getApp().currActivity.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        contentView = inflater.inflate(R.layout.popwindow_show_package_tips, null, false)
        popwindow = PopupWindow(
            contentView, RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.MATCH_PARENT, true
        )
        popwindow?.setBackgroundDrawable(ColorDrawable(Color.parseColor("#00000000")))
        popwindow?.setOutsideTouchable(false)
        initView()
    }

    private fun initView() {
        ivClose = getView<ImageView>(R.id.iv_close)
        tvContent = getView<TextView>(R.id.tv_content)
        btnLeft = getView<TextView>(R.id.btn_left)
        btnRight = getView<TextView>(R.id.btn_right)
        btnGroup = getView<Group>(R.id.btn_group)
        btnOnly = getView<TextView>(R.id.btn_only)
        rvfreight = getView<RecyclerView>(R.id.rv_freight)
        dataList = arrayListOf()
        freightAdapter = FreightAdapter(dataList)

        rvfreight?.setAdapter(freightAdapter)
        rvfreight?.setLayoutManager(LinearLayoutManager(popwindow?.getContentView()?.getContext()))

        ivClose?.setOnClickListener({ v: View? ->
            dismiss()
        })
        btnRight?.setOnClickListener({ v: View? ->
            mListener?.onRightClick(notSubmitOrderOrgIds.joinToString(separator = ","))
        })
    }

    fun setOutsideTouchable(outsideTouchable: Boolean): ShowFreightPopWindow {
        popwindow?.isOutsideTouchable = outsideTouchable
        popwindow?.isFocusable = outsideTouchable
        return this
    }


    fun setContent(cartBean: CartBean?): ShowFreightPopWindow {
        this.cartBean = cartBean
        refreshData(cartBean)
        return this
    }


    private fun refreshData(cartBean: CartBean?) {

        if (cartBean?.canSettle == 3) {
            btnGroup?.visibility = View.GONE
            btnOnly?.visibility = View.VISIBLE
            btnOnly?.setOnClickListener {
                RoutersUtils.open("ybmpage://searchproductorderbundling?shopCodes=${getShopCodesStr()}&isFromCart=1")
                dismiss()
            }
        } else if (cartBean?.canSettle == 2) {
            btnGroup?.visibility = View.VISIBLE
            btnOnly?.visibility = View.GONE
            btnLeft?.setOnClickListener {
                RoutersUtils.open("ybmpage://searchproductorderbundling?shopCodes=${getShopCodesStr()}&isFromCart=1")
                dismiss()
            }
        } else {
            return
        }

        cartBean?.let {
            notSubmitOrderOrgIds.clear()
            dataList.clear()
            cartBean.unsatisfiedStartPriceList?.takeIf { it.size > 0 }?.let {
                dataList.add(FreightInfoBeanWraper().apply { itemType = FreightInfoBeanWraper.UnStartTitleType })
            }
            cartBean.unsatisfiedStartPriceList?.forEach {
                dataList.add(FreightInfoBeanWraper().apply {
                    itemType = FreightInfoBeanWraper.UnStartBeanType
                    companyName = it.companyName
                    freightJumpUrl = it.freightJumpUrl
                    isSelfCompany = it.isSelfCompany
                    freightTips = it.freightTips
                    orgId = it.orgId
                    freightPriceTag = it.freightPriceTag
                    shopCode = it.shopCode
                })
                notSubmitOrderOrgIds.add(it.orgId)
            }
            cartBean.unsatisfiedFreeShippingList?.takeIf { it.size > 0 }?.let {
                dataList.add(FreightInfoBeanWraper().apply { itemType = FreightInfoBeanWraper.UnFreeShippingTitleType })
            }
            cartBean.unsatisfiedFreeShippingList?.forEach {
                dataList.add(FreightInfoBeanWraper().apply {
                    itemType = FreightInfoBeanWraper.UnFreeShippingBeanType
                    companyName = it.companyName
                    freightJumpUrl = it.freightJumpUrl
                    isSelfCompany = it.isSelfCompany
                    freightTips = it.freightTips
                    orgId = it.orgId
                    freightPriceTag = it.freightPriceTag
                    shopCode = it.shopCode
                })
            }
            freightAdapter?.notifyDataSetChanged()
        }

    }

    fun getShopCodesStr():String{
        var shopCodesStr = ""
        cartBean?.unsatisfiedStartPriceList?.forEach {
            if (!it.shopCode.isNullOrEmpty()){
                shopCodesStr = shopCodesStr + it.shopCode + ","
            }
        }
        cartBean?.unsatisfiedFreeShippingList?.forEach {
            if (!it.shopCode.isNullOrEmpty()){
                shopCodesStr = shopCodesStr + it.shopCode + ","
            }
        }
        if (shopCodesStr.isNotEmpty() && shopCodesStr.last() == ','){
            shopCodesStr.replace(shopCodesStr.last().toString(), "")
        }
        return shopCodesStr
    }


    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    fun backgroundAlpha(bgAlpha: Float) {
        val lp = (contentView!!.context as BaseActivity).window.attributes
        lp.alpha = bgAlpha //0.0-1.0
        lp.dimAmount = 1 - bgAlpha
        (contentView?.context as BaseActivity).window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        (contentView?.context as BaseActivity).window.attributes = lp
    }

    fun dismiss() {
        if (popwindow != null) {
            try {
                backgroundAlpha(1f)
                popwindow?.dismiss()
            } catch (e: Exception) {
            }
        }
    }

    fun show(token: View?) {
        if (popwindow == null) {
            return
        }
        try {
            if (isShow()) {
                popwindow?.dismiss()
            }
        } catch (e: java.lang.Exception) {
            return
        }
        try {
            popwindow?.showAtLocation(token, Gravity.BOTTOM, 0, 0)
            // 设置popWindow的显示和消失动画
            popwindow?.animationStyle = R.style.mypopwindow_anim_style
        } catch (e: java.lang.Exception) {
            return
        }
        backgroundAlpha(0.5f)
        popwindow!!.update()
    }

    fun isShow(): Boolean {
        return popwindow?.isShowing ?: false
    }

    fun <T : View?> getView(viewId: Int): T? {
        return if (contentView == null) {
            null
        } else try {
            contentView!!.findViewById<View>(viewId) as T
        } catch (e: Throwable) {
            null
        }
    }

    fun setOnButtonClickListener(listener: OnButtonClickListener?) {
        mListener = listener
    }

    interface OnButtonClickListener {
        fun onRightClick(notSubmitOrderOrgIds: String)
    }

    inner class FreightAdapter :
        YBMBaseMultiItemAdapter<FreightInfoBeanWraper> {
        constructor(data: MutableList<FreightInfoBeanWraper>?) : super(data) {
            addItemType(FreightInfoBeanWraper.UnStartTitleType, R.layout.item_package_title)
            addItemType(FreightInfoBeanWraper.UnStartBeanType, R.layout.item_package_gather)
            addItemType(FreightInfoBeanWraper.UnFreeShippingTitleType, R.layout.item_package_title)
            addItemType(FreightInfoBeanWraper.UnFreeShippingBeanType, R.layout.item_package_gather)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: FreightInfoBeanWraper) {
            when (t.itemType) {
                FreightInfoBeanWraper.UnStartTitleType -> bindUnStartTitleView(baseViewHolder, t)
                FreightInfoBeanWraper.UnStartBeanType -> bindUnStartView(baseViewHolder, t)
                FreightInfoBeanWraper.UnFreeShippingTitleType -> bindUnFreeShippingTitleView(baseViewHolder, t)
                FreightInfoBeanWraper.UnFreeShippingBeanType -> bindUnFreeShippingView(baseViewHolder, t)
            }
        }


        private fun bindUnStartTitleView(baseViewHolder: YBMBaseHolder, t: FreightInfoBeanWraper) {
            baseViewHolder.setText(R.id.tv_pacakge_title, "以下店铺加购商品不够起送门槛，不能提交订单")
            baseViewHolder.setBackgroundColor(R.id.cl_container, UiUtils.getColor(R.color.color_fff7ef))
        }

        private fun bindUnStartView(baseViewHolder: YBMBaseHolder, t: FreightInfoBeanWraper) {

            baseViewHolder.setBackgroundColor(R.id.cl_container, UiUtils.getColor(R.color.color_fff7ef))
            bindContentView(baseViewHolder, t)
        }

        private fun bindUnFreeShippingTitleView(baseViewHolder: YBMBaseHolder, t: FreightInfoBeanWraper) {
            baseViewHolder.setText(R.id.tv_pacakge_title, "以下店铺加购商品不够包邮门槛，需另付运费")
            baseViewHolder.setBackgroundColor(R.id.cl_container, UiUtils.getColor(R.color.white))

        }

        private fun bindUnFreeShippingView(baseViewHolder: YBMBaseHolder, t: FreightInfoBeanWraper) {
            baseViewHolder.setBackgroundColor(R.id.cl_container, UiUtils.getColor(R.color.white))
            bindContentView(baseViewHolder, t)
        }

        private fun bindContentView(baseViewHolder: YBMBaseHolder, freightInfoBean: FreightInfoBeanWraper) {
            val tvShopName = baseViewHolder.getView<TextView>(R.id.tv_shop_name)
            tvShopName.text = freightInfoBean.companyName
            if (freightInfoBean.isSelfCompany == 1) {
                tvShopName.setCompoundDrawablesWithIntrinsicBounds(mContext.resources.getDrawable(R.drawable.icon_shop_style), null, null, null)
            } else {
                tvShopName.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
            }
            baseViewHolder.setText(R.id.tv_pacakge_content, freightInfoBean.freightTips)

            if (!TextUtils.isEmpty(freightInfoBean.freightPriceTag)) {
                baseViewHolder.getView<TextView>(R.id.tv_pacakge_add).visibility = View.VISIBLE
                baseViewHolder.setText(R.id.tv_pacakge_add, freightInfoBean.freightPriceTag)
            } else {
                baseViewHolder.getView<TextView>(R.id.tv_pacakge_add).visibility = View.GONE
            }
            baseViewHolder.setOnClickListener(R.id.tv_go_shoping) { v: View? ->
                RoutersUtils.open(freightInfoBean.freightJumpUrl)
                dismiss()
            }
        }

    }

}