package com.ybmmarket20.view

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.ybmmarket20.R

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/29 14:21
 *    desc   :
 */
class RemindSubmitSuccessPopWindow(var content: String) : BaseBottomPopWindow() {
    interface RemindSubmitSuccessListener {
        fun goOnLineService()
    }

    private var remindSubmitSuccessListener: RemindSubmitSuccessListener? = null
    var tvContent: TextView? = null
    var confirm: TextView? = null
    var goLineService: TextView? = null
    var close: ImageView? = null
    fun setRemindSubmitSuccessListener(remindSubmitSuccessListener1: RemindSubmitSuccessListener) {
        remindSubmitSuccessListener = remindSubmitSuccessListener1
    }

    override fun getLayoutId() = R.layout.remind_submit_success_pop

    override fun initView() {
        val lp = LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            0f
        )
        layoutParams = lp
        tvContent = getView<TextView>(R.id.tv_content)
        goLineService = getView<TextView>(R.id.tv_go_line)
        confirm = getView<TextView>(R.id.tv_confirm)
        close = getView<ImageView>(R.id.iv_close)
        tvContent?.setText(content)

        close?.setOnClickListener(View.OnClickListener { v: View? -> dismiss() })
        confirm?.setOnClickListener(View.OnClickListener { v: View? -> dismiss() })
        goLineService?.setOnClickListener(View.OnClickListener { v: View? -> remindSubmitSuccessListener?.goOnLineService() })
    }
}