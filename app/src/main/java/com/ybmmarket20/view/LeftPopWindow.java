package com.ybmmarket20.view;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.YBMAppLike;


/**
 * 一个左边出来的popwindows 窗口容器
 * 使用：new LeftPopWindow(R.layout.view).show
 */

public abstract class LeftPopWindow<T> {
    private PopupWindow popwindow;
    private View contentView;
    private Listener listener;
    private boolean isClassify;
    private View.OnTouchListener onTouchListener;

    public LeftPopWindow(int layouRes) {
        this(LayoutInflater.from(BaseYBMApp.getApp().getCurrActivity()).inflate(layouRes, null));
    }

    public LeftPopWindow(View contentView) {
        this.contentView = contentView;
        popwindow = new PopupWindow(this.contentView,
                LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, true);
        initPop();
        initView(contentView);
    }

    protected abstract void initView(View contentView);

    public void hiddenShopService(boolean isHidden) {
        if (contentView != null) {
            try {
                LinearLayout shop_service_options = contentView.findViewById(R.id.shop_service_options);
                TextView shop_service_title = contentView.findViewById(R.id.shop_service_title);
                shop_service_options.setVisibility(isHidden? View.GONE: View.VISIBLE);
                shop_service_title.setVisibility(isHidden? View.GONE: View.VISIBLE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public interface Listener<T> {
        void onDismiss();

        void onResult(T t);
    }

    public void setOnTouchListener(View.OnTouchListener listener) {
        this.onTouchListener = listener;
        if (popwindow != null) {
            popwindow.setTouchInterceptor(onTouchListener);
        }
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public void setClassify(boolean classify) {
        isClassify = classify;
    }

    private void initPop() {
        popwindow.setAnimationStyle(R.style.pop_ani_bottom_top);
        popwindow.setFocusable(true);
        popwindow.setOutsideTouchable(true);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#00000000")));
        popwindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                if (isClassify) {
                    setDim(1);
                }

                if (listener != null) {
                    listener.onDismiss();
                }
            }
        });
        popwindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
        popwindow.setTouchInterceptor(onTouchListener);
        //主题跟着动态变化，哎。。。
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, contentView.findViewById(R.id.ll_title));
    }

    public void onResult(T t) {
        if (listener != null) {
            listener.onResult(t);
        }
    }

    public void show() {
        if (popwindow == null) {
            return;
        }
        try {
            if (popwindow.isShowing()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            BugUtil.sendBug(e);
            return;
        }

        View token = null;
        if (BaseYBMApp.getApp().getCurrActivity() != null) {
            if (BaseYBMApp.getApp().getCurrActivity().getWindow().isActive()) {
                token = BaseYBMApp.getApp().getCurrActivity().getWindow().getDecorView();
            }
            if (token == null || !token.isAttachedToWindow()) {
                return;
            }
        } else {
            return;
        }
        try {
            popwindow.showAtLocation(token, Gravity.TOP | Gravity.START, 0, 0);
        } catch (Exception e) {
            BugUtil.sendBug(e);
            return;
        }
        setDim(0.3f);
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                popwindow.dismiss();
            } catch (Exception e) {
            }
        }
    }

    public void dismiss(boolean isClassify) {
        this.isClassify = isClassify;
        dismiss();
    }

    public void setDim(float alp) {
        WindowManager.LayoutParams lp = BaseYBMApp.getApp().getCurrActivity().getWindow().getAttributes();
        lp.alpha = alp;
        lp.dimAmount = 1 - alp;
        BaseYBMApp.getApp().getCurrActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        BaseYBMApp.getApp().getCurrActivity().getWindow().setAttributes(lp);
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }

}
