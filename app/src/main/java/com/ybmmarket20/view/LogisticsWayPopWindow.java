package com.ybmmarket20.view;

import androidx.recyclerview.widget.DividerItemDecoration;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.LogisticsWayBean;
import com.ybmmarket20.bean.PromiseListBean;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 物流方式-底部弹出popWindow
 */

public class LogisticsWayPopWindow extends BaseBottomPopWindow {

    private CommonRecyclerView mRecyclerView;
    private List<LogisticsWayBean> mData;
    private String mCurrentName;
    private LogisticsWayAdapter mAdapter;
    private ItemSelectListener mListener;

    public void setData(List<LogisticsWayBean> data) {
        this.mData = data;
        mAdapter.setNewData(data);
    }

    public void setCurrentName(String currentName) {
        this.mCurrentName = currentName;
        mAdapter.notifyDataSetChanged();
    }


    public void setListener(ItemSelectListener listener) {
        this.mListener = listener;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.pop_logistics_way;
    }

    @Override
    protected void initView() {
        mRecyclerView = getView(R.id.mRecyclerView);
        contentView.findViewById(R.id.mCloseIv).setOnClickListener(v -> dismiss());
        contentView.findViewById(R.id.bg).setOnClickListener(v -> dismiss());
        mAdapter = new LogisticsWayAdapter(mData);
        mAdapter.setOnItemClickListener((baseQuickAdapter, view, position) -> {
            LogisticsWayBean bean = mData.get(position);
            setCurrentName(bean.getName());
            if (mListener != null) {
                mListener.onItemSelected(position, bean);
                dismiss();
            }
        });
        mRecyclerView.addItemDecoration(new DefaultItemDecoration(contentView.getContext()));
        mRecyclerView.setAdapter(mAdapter);
        mRecyclerView.setEmptyView(R.layout.page_error);
        mRecyclerView.setEnabled(false);
        mRecyclerView.setShowAutoRefresh(false);
        mAdapter.setEnableLoadMore(false);
    }

    class LogisticsWayAdapter extends BaseQuickAdapter<LogisticsWayBean,BaseViewHolder> {

        public LogisticsWayAdapter(List<LogisticsWayBean> data) {
            super(R.layout.item_logistics_way, data);
        }

        @Override
        protected void convert(BaseViewHolder baseViewHolder, LogisticsWayBean logisticsWayBean) {
            baseViewHolder.setText(R.id.mNameTv, logisticsWayBean.getName());
            if (logisticsWayBean.getName().equals(mCurrentName)) {
                baseViewHolder.setImageResource(R.id.mCheckIv, R.drawable.checkbox_select);
            } else {
                baseViewHolder.setImageResource(R.id.mCheckIv, R.drawable.checkbox_normal);
            }
        }
    }

    public interface ItemSelectListener {
        void onItemSelected(int position, LogisticsWayBean bean);
    }
}
