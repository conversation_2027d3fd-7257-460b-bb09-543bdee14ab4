package com.ybmmarket20.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ScrollView;

/**
 * Created by asus on 2016/9/23.
 */

public class CommonScrollView extends ScrollView {

    public CommonScrollView(Context context) {
        super(context);
    }

    public CommonScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CommonScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
//		super.onScrollChanged();
        if (this.mScrollListener != null)
            this.mScrollListener.onScrollChanged(l, t);
    }

    private OnScrollListener mScrollListener;

    public void setOnScrollListener(OnScrollListener onScrollListener) {
        this.mScrollListener = onScrollListener;
    }

    /**
     * 滚动监听
     *
     * <AUTHOR>
     * @DateTime 2015年11月30日
     */
    public interface OnScrollListener {
        public void onScrollChanged(int x, int y);
    }
}
