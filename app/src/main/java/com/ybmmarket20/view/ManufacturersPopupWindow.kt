package com.ybmmarket20.view

import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.mcxtzhang.indexlib.suspension.SuspensionDecoration
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.utils.UiUtils
import com.ybm.app.view.CommonRecyclerView
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.ManufacturersBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.common.util.ConvertUtils

/**
 * 搜索厂家下拉框
 */
class ManufacturersPopupWindow(private val mMasterStandardProductId: String?): BaseFilterPopWindow() {

    private val mDatas: MutableList<ManufacturersBean?> = ArrayList()
    private var lastNames: MutableList<String>? = ArrayList()
    private var mDecoration: SuspensionDecoration? = null
    private var adapter: YBMBaseAdapter<*>? = null

    private lateinit var mEtSearch: EditText
    private lateinit var mIvDel: ImageView
    private lateinit var mRvList: CommonRecyclerView
    private lateinit var mIndexBar: IndexBar
    private lateinit var mTvSideBarHint: TextView
    private lateinit var mBtnReset: Button
    private lateinit var mBtnAffirm: Button
    private lateinit var mSearchFilterData: SearchFilterData

    private var drugClassification = ""
    private var shopCodes: String? = null

    override fun getLayoutId(): Int {
        return R.layout.manufacturers2_pop
    }

    override fun initView() {
        mEtSearch = getView(R.id.et_search)
        mIvDel = getView(R.id.iv_del)
        mRvList = getView(R.id.rv_list)
        mIndexBar = getView(R.id.indexBar)
        mTvSideBarHint = getView(R.id.tvSideBarHint)
        mBtnReset = getView(R.id.btn_reset)
        mBtnAffirm = getView(R.id.btn_affirm)
        contentView.findViewById<View>(R.id.bg).setOnClickListener { setBtnAffirm() }

        //重置
        mBtnReset.setOnClickListener { reset(false) }

        //确定
        mBtnAffirm.setOnClickListener { setBtnAffirm() }
        mIvDel.setOnClickListener { mEtSearch.setText("") }
        mEtSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (s.isNotEmpty()) { //输入文字
                    mIvDel.visibility = View.VISIBLE
                    if (mRvList.isEnabled) { //禁止下拉刷新
                        mRvList.isEnabled = false
                    }
                    searchName(s.toString())
                } else { //清空
                    mIvDel.setVisibility(View.GONE)
                    setNewData(true, mDatas)
                }
            }

            override fun afterTextChanged(s: Editable) {}
        })
        mDecoration = SuspensionDecoration(mRvList.context, mDatas)
        mDecoration!!.setColorTitleBg(UiUtils.getColor(R.color.choose_eara_item_press_color))
        mDecoration!!.setColorTitleFont(UiUtils.getColor(R.color.text_9494A6))
        mRvList.addItemDecoration(mDecoration)
        mRvList.setShowAutoRefresh(false)
        adapter = object : YBMBaseAdapter<ManufacturersBean>(R.layout.choose_item_shop, mDatas) {
                override fun bindItemView(holder: YBMBaseHolder, bean: ManufacturersBean) {
                    val tv = holder.getView<TextView>(R.id.tv)
                    tv.isActivated = lastNames!!.contains(bean.manufacturer)
                    tv.text = bean.manufacturer
                    tv.setOnClickListener { view ->
                        val activated = view.isActivated
                        if (activated) {
                            view.isActivated = false
                            if (lastNames != null) {
                                lastNames!!.remove(bean.manufacturer)
                            }
                        } else {
                            view.isActivated = true
                            if (lastNames != null) {
                                lastNames!!.add(bean.manufacturer)
                            }
                            notifyDataSetChanged()
                        }
                    }
                }
            }
        mRvList.setAdapter(adapter)
        mRvList.setLoadMoreEnable(false)
        //indexbar初始化
        mIndexBar.setmPressedShowTextView(mTvSideBarHint) //设置HintTextView
            .setNeedRealIndex(true) //设置需要真实的索引
            .setDataHelper(IndexBarDataHelper())
            .setSourceDatasAlreadySorted(false)
            .setmLayoutManager(mRvList.getLayoutManager() as WrapLinearLayoutManager)
            .setIndexColor(R.color.base_colors_new)
            .setPadding(ConvertUtils.dp2px(6f)).headerViewCount = 1 //设置RecyclerView的LayoutManager
    }

    private fun setBtnAffirm() {
        if (mOnSelectListener != null) {
            mOnSelectListener.getValue(SearchFilterBean(lastNames))
        }
        dismiss()
    }

    private fun searchName(name: String) {
        var name = name.trim()
        if (mDatas.isEmpty() || TextUtils.isEmpty(name)) {
            return
        }
        name = name.trim { it <= ' ' }
        val size = mDatas.size
        val temp: MutableList<ManufacturersBean?> = ArrayList()
        var bean: ManufacturersBean? = null
        for (a in 0 until size) {
            bean = mDatas[a]
            if (bean?.manufacturer != null && bean.manufacturer.contains(name)) {
                temp.add(bean)
            }
        }
        setNewData(false, temp)
    }

    private fun setNewData(isAll: Boolean, list: List<ManufacturersBean?>?) {
        if (isAll) {
            mRvList.recyclerView.removeItemDecoration(mDecoration!!)
            mRvList.addItemDecoration(mDecoration)
            if (list!!.isNotEmpty()) {
                mIndexBar.visibility = View.VISIBLE
            } else {
                mIndexBar.visibility = View.GONE
            }
            mIndexBar.setmSourceDatas(list) //设置数据
            mDecoration!!.setmDatas(list)
            mIndexBar.requestLayout()
        } else {
            mRvList.recyclerView.removeItemDecoration(mDecoration!!)
            mIndexBar.visibility = View.GONE
        }
        adapter!!.setNewData(list)
    }

    fun setData(list: List<ManufacturersBean>?) {
        if (list?.isNotEmpty() == true) {
            mDatas.clear()
            mDatas.addAll(list)
            setNewData(true, mDatas)
        }
    }

    fun setShopCodes(shopCodes: String?) {
        this.shopCodes = shopCodes
    }

    fun setDataType(searchFilterData: SearchFilterData) {
        mSearchFilterData = searchFilterData
        if (lastNames == null) {
            lastNames = ArrayList()
        }
        lastNames!!.clear()
        lastNames!!.addAll(searchFilterData.list!!)
        mDatas.clear()
        mEtSearch.setText("")
        adapter!!.setNewData(mDatas)
    }

    fun reset(isBrand: Boolean) {
        if (lastNames == null) {
            lastNames = ArrayList()
        }
        lastNames!!.clear()

        if (isBrand) {
            mSearchFilterData = SearchFilterData()
            drugClassification = ""
        }
        adapter!!.notifyDataSetChanged()
    }

    override fun show(token: View?) {
        super.show(token)
    }

    private fun setDrugsClass(): String {
        val drugsClassList = mutableListOf<String>()
        if (mSearchFilterData.isClassA) {
            drugsClassList.add("1")
        }
        if (mSearchFilterData.isClassB) {
            drugsClassList.add("2")
        }
        if (mSearchFilterData.isClassRx) {
            drugsClassList.add("3")
        }
        if (mSearchFilterData.isClassElse) {
            drugsClassList.add("4")

        }
        if (drugsClassList.isNotEmpty()) {
            return drugsClassList.joinToString(",")
        }
        return ""
    }

    /**
     * 获取厂家数据的参数
     */
    data class SearchFilterData(
        var categoryId: String,
        var commonName: String,
        var isAvailable: Boolean,
        var isPromotion: Boolean,
        var isClassA: Boolean,
        var isClassB: Boolean,
        var isClassRx: Boolean,
        var isClassElse: Boolean,
        var priceRangeFloor: String,
        var priceRangeTop: String,
        @Transient var list: List<String>?
    ) {
        constructor(): this("", "", false, false, false, false, false, false, "", "", null)
    }
}