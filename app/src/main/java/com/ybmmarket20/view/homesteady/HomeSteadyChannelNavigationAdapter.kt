package com.ybmmarket20.view.homesteady

import android.content.Context
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.ChannelNavigation
import com.ybmmarket20.utils.RoutersUtils

/**
 * <AUTHOR>
 * @date 2020-05-13
 * @description 频道页头部导航adapter
 */

class HomeSteadyChannelNavigationAdapter (
        var navigationContext: Context,
        var list: MutableList<ChannelNavigation>,
        var title: String?
) : YBMBaseAdapter<ChannelNavigation>(R.layout.item_home_steady_channel_navigation, list) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ChannelNavigation?) {
        t?.apply {
            baseViewHolder?.setText(R.id.tv_item_channel_navigation, t.title)
            ImageHelper.with(navigationContext).load(t.url).placeholder(R.drawable.icon_home_steady_fast_entry_placehold)
                .error(R.drawable.icon_home_steady_fast_entry_placehold).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate().into(baseViewHolder?.getView(R.id.iv_item_channel_navigation))
            baseViewHolder?.getConvertView()?.setOnClickListener {
                RoutersUtils.open("ybmpage://homeSteadyChannel?strategyId=${t.strategyId}&title=$title")
            }
        }
    }

}