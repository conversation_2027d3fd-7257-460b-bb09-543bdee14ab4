package com.ybmmarket20.view.homesteady

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent

/**
 * <AUTHOR>
 * @date 2020-05-11
 * @description 首页快捷入口adapter
 */

const val CUSTOMIZED_TYPE_H5 = 1
const val CUSTOMIZED_TYPE_NATIVE = 2

class HomeSteadyFastEntryAdapter(
        var fastEntryContext: Context,
        var list: MutableList<FastEntryItem>,
        var mSuperData: FastEntry? = null,
        var layout: Int = R.layout.item_home_steady_fast_entry
) : HomeSteadyFastEntryAnalysisAdapter(list, layout) {

    private var analysisCallback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)? = null
    private var mView: View? = null
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: FastEntryItem?) {
        super.bindItemView(baseViewHolder, t)
        t?.also {
            baseViewHolder?.getView<TextView>(R.id.tv_item_fast_entry)?.text = t.entry
            ImageHelper.with(fastEntryContext).load(t.icon).placeholder(R.drawable.icon_home_steady_fast_entry_placehold)
                    .error(R.drawable.icon_home_steady_fast_entry_placehold).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate().into(baseViewHolder?.getView(R.id.iv_item_fast_entry))
            if(t.itemType == HOME_STEADY_LAYOUT_REAL) {
                baseViewHolder?.getConvertView()?.setOnClickListener{

                    var offset = "" //1排1页为1,2,3,4,5依次类推，1排2页为1,2,3,4,5，..9； 2排位1-1；1-2；2-1；2-5;依次类推

                    when(t.pages){
                        1 ->{ //仅一页

                            if (t.pageItemCount <=5){ //只有一行
                                offset = (baseViewHolder.bindingAdapterPosition+1).toString()
                            }else{ //有两行
                                if (baseViewHolder.bindingAdapterPosition>5){ //第一页第二行
                                    offset = "2-${baseViewHolder.bindingAdapterPosition-4}" //position下标+1 又减去5  5个一行
                                }else{  //第一行
                                    offset = "1-${baseViewHolder.bindingAdapterPosition+1}"
                                }
                            }
                        }
                        2->{  //两页

                            when(t.pageIndex){
                                0->{
                                    offset = (baseViewHolder.bindingAdapterPosition+1).toString()
                                }

                                else ->{
                                    offset = (baseViewHolder.bindingAdapterPosition+6).toString() //position下标+1 又加5
                                }
                            }
                        }
                    }

                    if (t.customizedType == CUSTOMIZED_TYPE_H5) {
                        var mUrl = addTrackField2Url(t.action?:"",t.trackPageId,t.trackModule,offset)
                        RoutersUtils.open(mUrl)
                    } else if(!TextUtils.isEmpty(t.strategyId)){
                        var mUrl = "ybmpage://homeSteadyChannel?strategyId=${t.strategyId}&title=${t.entry}"
                        mUrl = addTrackField2Url(mUrl,t.trackPageId,t.trackModule,offset)
                        RoutersUtils.open(mUrl)
                    }
                    clickEvent(t, baseViewHolder.layoutPosition)
                }
            }
        }
    }

    private fun addTrackField2Url(url:String,pageId:Int,module:Int,offset: String):String{
        val mUrl = url.trim()
        return if (mUrl.endsWith("html")){
            "$mUrl?&page_id=$pageId&module=$module&offset=$offset"
        }else{
            "$mUrl&page_id=$pageId&module=$module&offset=$offset"
        }
    }

    /**
     * 快捷入口点击事件
     */
    private fun clickEvent(item: FastEntryItem?, position: Int) {
        item?.also {
            try {
                val action = if (it.customizedType == CUSTOMIZED_TYPE_H5) {
                    it.action
                } else if(!TextUtils.isEmpty(it.strategyId)){
                    "ybmpage://homeSteadyChannel?strategyId=${it.strategyId}&title=${it.entry}"
                } else ""
                if (mView != null) {
                    analysisCallback?.invoke(action?: "", position, it, mView!!, itemCount, item.icon?: "")
                }
                SpmLogUtil.print("首页-组件-快捷入口点击")
                HomeReportEvent.trackHomeSubComponentClick(mContext, item.trackData?.spmEntity, item.trackData?.scmEntity)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun setAnalysisCallback(callback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)?, fastEntryView: View) {
        analysisCallback = callback
        mView = fastEntryView
    }
}