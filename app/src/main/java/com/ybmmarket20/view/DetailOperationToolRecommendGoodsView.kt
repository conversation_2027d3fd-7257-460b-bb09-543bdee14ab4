package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetailBean
import com.ybmmarket20.bean.ProductDetailBeanWrapper.ShopInfo
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_ADD_TO_CART
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_RIGHT_NOW
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_SELECT_GOODS
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport
import kotlinx.android.synthetic.main.detail_operation_tool_recommend_goods.view.*

/**
 * 商祥随心拼底部工具栏
 */
class DetailOperationToolRecommendGoodsView(context: Context, attr: AttributeSet) :
    ConstraintLayout(context, attr) {

    private var mJumpCallback: (() -> Unit)? = null

    var shopCode = ""
    var orgId = ""
    var mainGoodsSkuId = ""
    var isThirdCompany = 0
    var mainGoodsCount = ""
    var mainGoodsPId: String? = null
    var productDetail: ProductDetailBean? = null
        set(value) {
            setControlGoodsBtnStyle(value)
            field = value
        }
    var shopInfo: ShopInfo? = null
    var SPELL_GROUP_RECOMMEND_TYPE = 0 // 跳转类型
    var canAddToCart = false // 拼团商品可加购

    init {
        View.inflate(context, R.layout.detail_operation_tool_recommend_goods, this)
        rtv_spell_group_right_now.setOnClickListener {
            //立即参团
            SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_RIGHT_NOW
            registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_RIGHT_NOW)

            CommodityDetailReport.bottomBtnClickRight(context, 1, rtv_spell_group_right_now.text.toString())
        }
        rtv_spell_group_recommend_goods.setOnClickListener {
            if (canAddToCart) {
                // 拼团商品可加购
                SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_ADD_TO_CART
                registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_ADD_TO_CART)
            } else {
                //随心拼
                SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_SELECT_GOODS
                registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_SELECT_GOODS)
            }

            CommodityDetailReport.bottomBtnClickRight(context, 2, rtv_spell_group_recommend_goods.text.toString())
        }
    }

    /**
     * 设置控销品按钮样式
     */
    private fun setControlGoodsBtnStyle(pd: ProductDetailBean?) {
        if (pd?.isControlGoods == true) {
            rtv_spell_group_right_now.isEnabled = false
            rtv_spell_group_recommend_goods.isEnabled = false
            rtv_spell_group_right_now.setTextColor(Color.parseColor("#AAACB9"))
            rtv_spell_group_right_now.setStrokeColor(Color.parseColor("#AAACB9"))
            rtv_spell_group_recommend_goods.setTextColor(Color.parseColor("#FFFFFF"))
            rtv_spell_group_recommend_goods.setBackgroundColor(Color.parseColor("#AAACB9"))
        }
    }

    /**
     * 设置商品可加购物车样式
     */
    fun setCanAddToCart(pd: ProductDetailBean?) {
        if (pd?.canAddToCart == true) {
            rtv_spell_group_recommend_goods.text = "加入购物车"
            tvSuiXinPinBubble.visibility = View.GONE
        }
    }

    /**
     * 注册随心拼跳转类型
     */
    fun registerRecommendGoodsJumpType(jumpType: Int, isShowPopupWindow: Boolean = true) {
        if (context is ComponentActivity) {
            if (isShowPopupWindow) {
                mJumpCallback?.invoke()
            }
            val viewModel: SpellGroupRecommendGoodsViewModel = ViewModelProvider(GlobalViewModelStore.get().getGlobalViewModelStore(), SavedStateViewModelFactory(
                (context as ComponentActivity).application,
                context as ComponentActivity
            )).get(SpellGroupRecommendGoodsViewModel::class.java)
            viewModel.shopCode = shopCode
            viewModel.orgId = orgId
            viewModel.mainGoodsSkuId = mainGoodsSkuId
            viewModel.isThirdCompany = isThirdCompany
            viewModel.mainGoodsCount = mainGoodsCount
            viewModel.mainGoodsPId = mainGoodsPId
            viewModel.registerJumpType(jumpType)
        }
    }

    fun setSuiXinPinBubble(text: String?) {
        tvSuiXinPinBubble.visibility = if (text.isNullOrEmpty()) View.GONE else View.VISIBLE
        tvSuiXinPinBubble.text = text
    }

    /**
     * 点击跳转监听
     */
    fun setOnJumpClickCallback(jumpCallback: (() -> Unit)?) {
        mJumpCallback = jumpCallback
    }

    /**
     * 设置活动类型，拼团或批购包邮
     */
    fun setActPtType(isSpellGroup: Boolean, isWholeSale: Boolean) {
        if (isSpellGroup) {
            rtv_spell_group_right_now.text = "立即参团"
        }
        if (isWholeSale) {
            rtv_spell_group_right_now.text = "去抢购"
        }
    }
}