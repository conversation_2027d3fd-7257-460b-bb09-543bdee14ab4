package com.ybmmarket20.view

import android.app.Dialog
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Bitmap
import android.provider.ContactsContract.CommonDataKinds.Organization
import android.text.SpannableStringBuilder
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.drawToBitmap
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetailBeanWrapper
import com.ybmmarket20.bean.RangePriceBean
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundConstraintLayout
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.ShareUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.GoodsDetailShareViewModel
import com.ybmmarket20.viewmodel.generateShareUrl


/**
 * 商详分享弹窗
 */

typealias ItemAnalysisClickCallback = (String?) ->Void

class GoodsDetailShareDialog(var mContext: Context) :
        Dialog(mContext, R.style.dialog_confirm_style), View.OnClickListener {

    private var mViewModel: GoodsDetailShareViewModel? = null
    private var mData: GoodsDetailShareDialogResultBean? = null
    private var mDetailBean: GoodsDetailShareDialogRequestBean? = null
    private var mShareListener: ((String) -> Unit)? = null
    private var isSetSize = false
    private var mIsWholeSale = false
    private var mRootView: View =
            View.inflate(context, R.layout.dialog_product_detail_share_img, null)
    var weChatForImageAnalysisClickCallback: ItemAnalysisClickCallback? = null
    var weChatForLinkAnalysisClickCallback: ItemAnalysisClickCallback? = null
    var weMomentForLinkAnalysisClickCallback: ItemAnalysisClickCallback? = null
    var weComForImageAnalysisClickCallback: ItemAnalysisClickCallback? = null
    var weComForLinkAnalysisClickCallback: ItemAnalysisClickCallback? = null
    var copyLinkAnalysisClickCallback: ItemAnalysisClickCallback? = null

    init {
        if (mContext !is ComponentActivity) {
            dismiss()
        } else {
            setContentView(mRootView)
            if (window != null) {
                val p = window!!.attributes
                p.width = UiUtils.getScreenWidth()
                window!!.attributes = p
                window!!.setGravity(Gravity.BOTTOM)
//                window!!.setWindowAnimations(R.style.AnimBottom)
            }
            val androidViewModelFactory = ViewModelProvider.AndroidViewModelFactory.getInstance((mContext as ComponentActivity).application)
            mViewModel = ViewModelProvider(mContext as ComponentActivity, androidViewModelFactory).get(GoodsDetailShareViewModel::class.java)
            mViewModel?.shareDataLiveData?.observe(mContext as ComponentActivity, Observer(::setData))
        }
    }

    /**
     * 设置图片显示的尺寸，防止超出屏幕
     */
    private fun setSize() {
        if (isSetSize) return
        isSetSize = true
        val rat = 1.5f
        val rclShareImg = mRootView.findViewById<RoundConstraintLayout>(R.id.rcl_share_img)
        val screenHeight = ScreenUtils.getScreenHeight(mContext)
        val screenWidth = ScreenUtils.getScreenWidth(mContext)
        val imgRemainHeight = screenHeight - ScreenUtils.dip2px(context, 300f)
        var usedHeight = (imgRemainHeight - ScreenUtils.dip2px(context, 15f)).toFloat()
        var usedWidth = usedHeight / rat
        val maxWidth = screenWidth - ScreenUtils.dip2px(context, 30f) * 2f
        if (usedWidth > maxWidth) {
            usedWidth = maxWidth
            usedHeight = usedWidth * rat
        }
        val lp = rclShareImg.layoutParams as ConstraintLayout.LayoutParams
        lp.width = usedWidth.toInt()
        lp.height = usedHeight.toInt()
        rclShareImg.layoutParams = lp

        val resultRat = usedWidth / ScreenUtils.dip2px(context, 280f)
        (0 until rclShareImg.childCount).forEach { index ->
            val child = rclShareImg.getChildAt(index)
            if (child.id == R.id.rtv_bg
                    || child.id == R.id.v_line_left
                    || child.id == R.id.v_line_right
                    || child.id == R.id.tv_price
                    || child.id == R.id.tv_goods_name) return@forEach
            handleViewSizeByRat(child, resultRat, child.width, child.height)
            if (child is TextView) {
                val textSize = child.textSize * resultRat
                child.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize)
            }
        }
    }

    private fun handleViewSizeByRat(view: View, rat: Float, widthPx: Int, heightPx: Int) {
        val lp = view.layoutParams as ConstraintLayout.LayoutParams
        val viewWidth = widthPx * rat
        val viewHeight = heightPx * rat
        if (view.id != R.id.rtv_tip_bg) {
            lp.width = viewWidth.toInt()
        }
        lp.height = viewHeight.toInt()
        view.layoutParams = lp
    }

    override fun show() {
        if (mContext !is ComponentActivity) return
        super.show()
    }

    fun setData(data: GoodsDetailShareDialogResultBean) {
        mData = data
        val tvShopName = mRootView.findViewById<TextView>(R.id.tv_shop_name)
        val ivGoodsImage = mRootView.findViewById<ImageView>(R.id.iv_goods_img)
        val tvTitle = mRootView.findViewById<TextView>(R.id.tv_goods_name)
        val tvPrice = mRootView.findViewById<TextView>(R.id.tv_price)
        val tvControlUn = mRootView.findViewById<TextView>(R.id.tv_control_un)
        val tvAfterSale = mRootView.findViewById<TextView>(R.id.tv_after_sale)
        val ivQrCode = mRootView.findViewById<ImageView>(R.id.iv_qrcode)
        var tagImage = mRootView.findViewById<ImageView>(R.id.iv_marker)
        var clTextView = mRootView.findViewById<ConstraintLayout>(R.id.cl_textview)
        findViewById<RoundTextView>(R.id.rtv_cancel).setOnClickListener { dismiss() }
        val rv = mRootView.findViewById<RecyclerView>(R.id.rvShare)
        rv.layoutManager = GridLayoutManager(context, 3)
        rv.adapter = ShareAdapter()
        tvShopName.text = data.shopName
        tvTitle.text = data.goodsTitle
        tvPrice.text = data.price
        tvAfterSale.text = data.afterDiscount
        ivGoodsImage.setImageBitmap(data.goodsImage)
        ivQrCode.setImageBitmap(data.qrCode)
        if (data.tagBitmap != null) {
            tagImage.setImageBitmap(data.tagBitmap)
        }

        if (data.isShowPrice){
            tvPrice.visibility = View.VISIBLE
        }else{
            tvPrice.visibility = View.GONE
        }

        // 签署协议后可购买标签
        if (data.iscontrolType) {
            tvControlUn.visibility = View.VISIBLE
            tvControlUn.text = "签署协议后可买"
        } else {
            tvControlUn.visibility = View.GONE
        }

        // 折后价标签判断
        if (data.afterDiscount.isNullOrEmpty()) {
            tvAfterSale.visibility = View.GONE
        }

        // 判断是否需要移除整个标签
        if ((data.afterDiscount.isNullOrEmpty()) && !data.iscontrolType) {
            clTextView.visibility = View.GONE
        }
        setSize()
        show()
    }

    fun load(detailBean: GoodsDetailShareDialogRequestBean, isWholeSale: Boolean) {
        mDetailBean = detailBean
        mViewModel?.getGoodDetailShareData(detailBean, isWholeSale = isWholeSale)
        mIsWholeSale = isWholeSale
    }

    fun setShareListener(shareListener: (String) -> Unit) {
        mShareListener = shareListener
    }

    data class GoodsDetailShareDialogResultBean(
            var shopName: String,
            var goodsImage: Bitmap?,
            var qrCode: Bitmap?,
            var goodsTitle: String,
            var price: SpannableStringBuilder,
            var afterDiscount: String?,
            var isSpellGroup: Boolean,
            var tagBitmap: Bitmap?,
            val isShowPrice: Boolean, //是否显示价格（未签署协议时，不显示价格）
            val iscontrolType : Boolean // 是否是未签署协议类型
    )

    data class GoodsDetailShareDialogRequestBean(
            val shopName: String,
            val imageUrl: String?,
            val goodsName: String?,
            val price: String?,
            val id: String,
            val shareUrl: String?,
            val shareTitle: String?,
            val desc: String,
            val isSpellGroup: Boolean,
            val productDetail: ProductDetailBeanWrapper,
            val isStepPrice: Boolean,
            val promoType: String?,
            val promoId: String?,
            var tagUrl: String? = "", //标签
            val isShowPrice: Boolean, //是否显示价格（未签署协议时，不显示价格）
            val iscontrolType : Boolean, // 是否是未签署协议类型
            val rangeStepPrice: RangePriceBean? = null //普通品阶梯价
    )

    override fun onClick(v: View?) {
//        when (v?.id) {
//            R.id.iv_share_img -> {
//                //分享图片
////                ShareUtil.shareWXWithImage(0, getViewCacheBitmap(), 0, 0)
//                ShareUtil.shareWeworkWithImage(0, getViewCacheBitmap(), 0, 0)
//                mShareListener?.invoke("img")
//                trackData("wx", "1")
//            }
//
//            R.id.iv_share_wx -> {
//                sharedForWx(mData?.goodsImage)
//                mShareListener?.invoke("wx")
//                trackData("wx", "2")
//            }
//
//            R.id.iv_share_wx_pyq -> {
//                sharedForWxpyq(mData?.goodsImage)
//                mShareListener?.invoke("wxpyq")
//                trackData("wxpyq", "3")
//            }
//        }
    }

    /**
     * 分享埋点
     * @param shareType 分享类型：图片分享微信好友；链接分享微信好友；链接分享至微信朋友圈,枚举值：1,2,3
     */
    private fun trackData(shareChannel: String, shareType: String) {
        XyyIoUtil.track(XyyIoUtil.ACTION_SKU_SHARE_CLICK, hashMapOf(
                "skuId" to mDetailBean?.id,
                "promo_Type" to mDetailBean?.promoType,
                "promo_Id" to mDetailBean?.promoId,
                "share_Channel" to shareChannel,
                "share_Type" to shareType
        ))
    }

    /**
     * 分享到微信
     */
    private fun sharedForWx(bitmap: Bitmap?) {
        if (mData == null || bitmap == null) return
        ShareUtil.shareWXPage(0, mDetailBean?.shareTitle, generateShareUrl(mDetailBean?.shareUrl
                ?: "", "1"), mDetailBean?.desc, bitmap)
    }

    /**
     * 分享到企微
     */
    private fun sharedForWw(bitmap: Bitmap?) {
        if (mData == null || bitmap == null) return
        ShareUtil.shareWeworkPage(0, mDetailBean?.shareTitle, generateShareUrl(mDetailBean?.shareUrl
            ?: "", "1"), mDetailBean?.desc, bitmap)
    }

    /**
     * 分享到微信朋友圈
     */
    private fun sharedForWxpyq(bitmap: Bitmap?) {
        if (mData == null || bitmap == null) return
        ShareUtil.shareWXPage(1, mDetailBean?.shareTitle, generateShareUrl(mDetailBean?.shareUrl
                ?: "", "1"), mDetailBean?.desc, bitmap)
    }

    private fun getViewCacheBitmap(): Bitmap {
        val rclShareImg = mRootView.findViewById<RoundConstraintLayout>(R.id.rcl_share_img)
        return rclShareImg.drawToBitmap()
    }

    /**
     * 获取分享项
     */
    private fun getShareItems(): List<ShareItem> {
        return mutableListOf<ShareItem>().apply {
            add(ShareItem(R.drawable.icon_share_wechat, "图片分享微信", SHARE_TYPE_WECHAT_IMG))
            add(ShareItem(R.drawable.icon_share_wechat, "链接分享微信", SHARE_TYPE_WECHAT_LINK))
            add(ShareItem(R.drawable.icon_share_pyq, "链接分享朋友圈", SHARE_TYPE_WECHAT_PYQ))
            if (ShareUtil.isWeworkInstall()) {
                add(ShareItem(R.drawable.icon_share_wework, "图片分享企微", SHARE_TYPE_WEWORK_IMG))
                add(ShareItem(R.drawable.icon_share_wework, "链接分享企微", SHARE_TYPE_WEWORK_LINK))
            }
            add(ShareItem(R.drawable.icon_copy_link, "复制链接", SHARE_TYPE_COPY_LINK))
        }
    }

    companion object {
        //微信分享图片
        const val SHARE_TYPE_WECHAT_IMG = 0
        //微信分享链接
        const val SHARE_TYPE_WECHAT_LINK = 1
        //微信分享到朋友圈
        const val SHARE_TYPE_WECHAT_PYQ = 2
        //企微分享图片
        const val SHARE_TYPE_WEWORK_IMG = 3
        //企微分享链接
        const val SHARE_TYPE_WEWORK_LINK = 4
        //复制链接
        const val SHARE_TYPE_COPY_LINK = 5
    }

    data class ShareItem(
        val icon: Int,
        val title: String,
        val shareType: Int
    )

    inner class ShareAdapter: YBMBaseAdapter<ShareItem>(R.layout.item_share_product, getShareItems()) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShareItem?) {
            whenAllNotNull(baseViewHolder, t) {holder, bean ->
                holder.getView<ImageView>(R.id.ivShare).setImageResource(bean.icon)
                holder.getView<TextView>(R.id.tvShare).text = bean.title
                holder.itemView.setOnClickListener {
                    when(bean.shareType) {
                        SHARE_TYPE_WECHAT_IMG -> {
                            ShareUtil.shareWXWithImage(0, getViewCacheBitmap(), 0, 0)
                            trackData("wx", "1")
                            weChatForImageAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                        SHARE_TYPE_WECHAT_LINK -> {
                            sharedForWx(<EMAIL>?.goodsImage)
                            trackData("wx", "2")
                            weChatForLinkAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                        SHARE_TYPE_WECHAT_PYQ -> {
                            sharedForWxpyq(<EMAIL>?.goodsImage)
                            trackData("wxpyq", "3")
                            weMomentForLinkAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                        SHARE_TYPE_WEWORK_IMG -> {
                            ShareUtil.shareWeworkWithImage(0, getViewCacheBitmap(), 0, 0)
                            weComForImageAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                        SHARE_TYPE_WEWORK_LINK -> {
                            sharedForWw(<EMAIL>?.goodsImage)
                            weComForLinkAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                        SHARE_TYPE_COPY_LINK -> {
                            val shareUrl = generateShareUrl(mDetailBean?.shareUrl ?: "", "1")
                            val cm: ClipboardManager =
                                context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            cm.setText(shareUrl)
                            ToastUtils.showShort("复制链接成功，快去分享吧！")
                            dismiss()
                            copyLinkAnalysisClickCallback?.invoke(mDetailBean?.id)
                        }
                    }
                }
            }
        }
    }
}