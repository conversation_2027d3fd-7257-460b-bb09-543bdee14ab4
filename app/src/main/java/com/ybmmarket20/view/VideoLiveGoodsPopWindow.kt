package com.ybmmarket20.view

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import com.ybmmarket20.R
import com.ybmmarket20.bean.im.VideoLiveProductMessageInfo
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.analysis.XyyIoUtil

class VideoLiveGoodsPopWindow {
    private lateinit var contentView: View
    private var popwindow: PopupWindow? = null
    private lateinit var tvPrice: TextView
    private lateinit var tvTitle: TextView
    private lateinit var ivPic: ImageView
    private lateinit var flContainer: FrameLayout
    private lateinit var tvPriceTips: TextView
    private lateinit var rlPrice: RelativeLayout
    private lateinit var tvBuy: TextView
    private lateinit var el_edit: ProductEditLayout
    private var discountGoodsProductMessageInfo: VideoLiveProductMessageInfo? = null
    private var rxTimer: RxTimer? = null
    private var licenseStatus: Int = 0

    init {
        initPop()
    }

    private fun initPop() {
        contentView = LayoutInflater.from(YBMAppLike.getApp().currActivity).inflate(R.layout.layout_tv_live_goods_popwindow, null)
        tvPrice = contentView.findViewById<TextView>(R.id.tv_price)
        tvTitle = contentView.findViewById<TextView>(R.id.tv_title)
        ivPic = contentView.findViewById<ImageView>(R.id.iv_pic)
        flContainer = contentView.findViewById<FrameLayout>(R.id.fl_container)
        tvPriceTips = contentView.findViewById<TextView>(R.id.tv_price_tips)
        rlPrice = contentView.findViewById<RelativeLayout>(R.id.rl_price)
        tvBuy = contentView.findViewById<RoundTextView>(R.id.tv_buy)
        el_edit = contentView.findViewById<ProductEditLayout>(R.id.el_edit)

//        contentView.findViewById<RoundTextView>(R.id.tv_buy).setOnClickListener {
//            //去购买
//            RoutersUtils.open("ybmpage://productdetail/" + discountGoodsProductMessageInfo?.id)
//            dismiss()
//        }
        contentView.findViewById<LinearLayout>(R.id.ll_container).setOnClickListener {
            //去购买
            RoutersUtils.open("ybmpage://productdetail/" + discountGoodsProductMessageInfo?.id)
            dismiss()
        }

        popwindow = PopupWindow(this.contentView, LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT, true)
        popwindow?.setBackgroundDrawable(ColorDrawable(Color.parseColor("#00000000")))
        popwindow?.isFocusable = true
        popwindow?.isOutsideTouchable = true
    }

    fun isShow(): Boolean {
        return popwindow?.isShowing ?: false
    }

    fun show(view: View) {

        if (view?.context == null) return

        if (popwindow == null) {
            initPop()
        }
        try {
            if (popwindow != null && popwindow!!.isShowing) {
                popwindow?.dismiss()
            }
        } catch (e: Exception) {
            return
        }

        try {
            popwindow?.showAsDropDown(view, 0, ConvertUtils.dp2px(-160f))
            // 设置popWindow的显示和消失动画
            popwindow?.animationStyle = R.style.AnimCanter
        } catch (e: Exception) {
            return
        }

        //backgroundAlpha(0.3f)
        popwindow?.update()
    }

    fun dismiss() {
        if (contentView?.context != null && isShow()) {
            popwindow?.dismiss()
        }
    }

    fun newData(discountGoodsProductMessageInfo: VideoLiveProductMessageInfo?, licenseStatus: Int) {
        if (discountGoodsProductMessageInfo == null) {
            dismiss()
            return
        }
        this.licenseStatus = licenseStatus
        this.discountGoodsProductMessageInfo = discountGoodsProductMessageInfo

        tvTitle.text = discountGoodsProductMessageInfo.showName
        tvPrice.text = handleAmount(discountGoodsProductMessageInfo.fob.toString())

        var imgUrl = ""
        if (discountGoodsProductMessageInfo.imageUrl != null && !discountGoodsProductMessageInfo.imageUrl?.startsWith("http")!!) {
            imgUrl = AppNetConfig.LORD_IMAGE + discountGoodsProductMessageInfo.imageUrl
        }
        ImageLoader.loadImage(YBMAppLike.getAppContext(), ivPic, imgUrl, R.drawable.jiazaitu_min)

        // licenseStatus 1和5价格认证资质可见
        val isShowOEM = discountGoodsProductMessageInfo.isOEM && (discountGoodsProductMessageInfo.signStatus == 0)

        if (licenseStatus == 1 || licenseStatus == 5 || isShowOEM) {//显示价格认证资质可见或 oem
            rlPrice.visibility = View.GONE
            tvPriceTips.visibility = View.VISIBLE
            if (licenseStatus == 1 || licenseStatus == 5){
                tvPriceTips.text = "价格认证资质可见"
                return
            }
            if (isShowOEM){
                tvPriceTips.text = "价格签署协议可见"
            }
        } else {
            rlPrice.visibility = View.VISIBLE
            tvPriceTips.visibility = View.GONE
        }

        //加减按钮
        el_edit?.let {
            it.bindData(discountGoodsProductMessageInfo.id,
                    discountGoodsProductMessageInfo.status,
                    discountGoodsProductMessageInfo.isControl != 1 || (discountGoodsProductMessageInfo.isControl == 1 && discountGoodsProductMessageInfo.isPurchase),
                    ProductEditLayout.FROMPAGE_TV_LIVE_DIALOG,
                    true,
                    discountGoodsProductMessageInfo.mediumPackageNum,
                    discountGoodsProductMessageInfo.isSplit == 1)
        }

        tvTitle.post {
            val lp: RelativeLayout.LayoutParams = flContainer.layoutParams as RelativeLayout.LayoutParams
            if (tvTitle.lineCount >= 2) {
                lp.topMargin = ConvertUtils.dp2px(5.5f)
            } else {
                lp.topMargin = ConvertUtils.dp2px(28f)
            }
            flContainer.layoutParams = lp
        }
        if (discountGoodsProductMessageInfo.isHaveVoucherTemplate) {
            tvBuy.visibility = View.VISIBLE
            el_edit.visibility = View.GONE
        } else {
            tvBuy.visibility = View.GONE
            el_edit.visibility = View.VISIBLE
        }

        //展示时长
        rxTimer = RxTimer()
        rxTimer?.timer((discountGoodsProductMessageInfo.showtime * 1000).toLong()) { dismiss() }
    }
}