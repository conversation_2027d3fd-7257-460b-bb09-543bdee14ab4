package com.ybmmarket20.view;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.view.WrapGridLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ClinicTypeViewItem;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 诊所头部分类
 */

public class ClinicHeaderLayout extends RecyclerView {
    private int selPostion;
    private YBMBaseAdapter<ClinicTypeViewItem> adapter;
    private List<ClinicTypeViewItem> list = new ArrayList<>();
    private ItemClick itemClick;
    private String imgHeader = AppNetConfig.CDN_HOST;
    public interface ItemClick{
         void onItemClick(int position,ClinicTypeViewItem bean);
    }

    public ClinicHeaderLayout(Context context) {
        this(context,null);
    }

    public ClinicHeaderLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        adapter = new YBMBaseAdapter<ClinicTypeViewItem>(R.layout.clinic_view_item,list) {
            @Override
            protected void bindItemView(YBMBaseHolder holder, ClinicTypeViewItem bean) {
                if(list.size()<=selPostion){
                    selPostion = 0;
                }
                holder.setImageUrl(R.id.iv_icon,imgHeader+(holder.getAdapterPosition() == selPostion?bean.iconChecked:bean.icon),R.drawable.clinic_def_icon);
            }

        };
        adapter.setEnableLoadMore(false);
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                selPostion = position;
                if(itemClick !=null){
                    itemClick.onItemClick(selPostion,list.get(selPostion));
                }
                adapter.notifyDataSetChanged();
            }
        });
        setLayoutManager(new WrapGridLayoutManager(getContext(), 4));
        setAdapter(adapter);
        setPadding(ConvertUtils.dp2px(8),0, ConvertUtils.dp2px(8),0);
    }

    public void setItemClick(ItemClick click){
        this.itemClick = click;
    }

    public void setData(List<ClinicTypeViewItem> list){
        if(list == null){
            list = new ArrayList<>();
        }
        this.list.clear();
        this.list.addAll(list);
        if(selPostion>list.size()){
            selPostion = 0;
        }
        adapter.notifyDataSetChanged();
        for(ClinicTypeViewItem item:list){//先缓存图片，加快显示
            ImageHelper.with(getContext()).load(getImgUrl(item.iconChecked)).downloadOnly(1, 1);
            ImageHelper.with(getContext()).load(getImgUrl(item.icon)).downloadOnly(1, 1);
        }
    }

    public List<ClinicTypeViewItem> getList(){
        return list;
    }

    private String getImgUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return "";
        }
        if (url.startsWith("http") || url.startsWith("Http")) {
            return url;
        } else {
            return imgHeader + url;
        }
    }


    public void selPostion(int index){
        if(index<0){
            return;
        }
        selPostion = index;
        adapter.notifyDataSetChanged();
    }

}
