package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Group
import androidx.core.widget.doAfterTextChanged
import com.ybmmarket20.R
import com.ybmmarket20.constant.*
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.BaseHomeSteadyView
import com.ybmmarket20.view.homesteady.IHomeSteady
import kotlinx.android.synthetic.main.common_search_back_scan_voice_cart.view.*

class CommonSearchView(context: Context, attr: AttributeSet) : BaseHomeSteadyView(context, attr), View.OnClickListener, IHomeSteady {

//    override fun initialize() {
//        super.initialize()
////        findViewById<EditText>(R.id.tv_title_et).doAfterTextChanged {
////            ivClearEdit.visibility = if (it?.isEmpty() == true) View.GONE else View.VISIBLE
////            ivVoice.visibility = if (it?.isEmpty() == true) View.GONE else View.VISIBLE
////        }
//    }

    var clickCallback: ((content:String) -> Unit)? = null

    fun getEditTextStr(): String = tv_title_et.text.toString()

    override fun getLayoutId(): Int = R.layout.common_search_back_scan_voice_cart

    override fun onClick(v: View?) {
        when (v?.id) {
            //扫码
            R.id.iv_scan -> {
                clickCallback?.invoke("搜索框-扫一扫")
                if (openNextPageUrl?.isEmpty() == true) {
                    RoutersUtils.open(ROUTER_CAPTURE)
                } else {
                    RoutersUtils.open("${ROUTER_CAPTURE}?${OPENNEXTPAGEURLKEY}=${RoutersUtils.urlEncode(openNextPageUrl)}")
                }

            }

            //语音搜索
            R.id.iv_voice -> {

                clickCallback?.invoke("搜索框-语音")
                val content = HashMap<String, String>()
                content["entry"] = entry ?: "Undefined"
                content["merchantId"] = SpUtil.getMerchantid()
                XyyIoUtil.track(XyyIoUtil.ACTION_VOICE_CLICK, content)

                if (openNextPageUrl?.isNullOrEmpty() == true) {
                    RoutersUtils.open(ROUTER_VOICE_SEARCH_PRODUCT)
                } else {
                    RoutersUtils.open("${ROUTER_VOICE_SEARCH_PRODUCT}?${OPENNEXTPAGEURLKEY}=${RoutersUtils.urlEncode(openNextPageUrl)}")
                }

            }
        }
    }

    var openNextPageUrl: String? = null
    var entry: String? = null

    var clSearchContainer: ConstraintLayout = findViewById(R.id.cl_search_container)
    var ivScan: ImageView = findViewById(R.id.iv_scan)
    var ivVoice: ImageView = findViewById(R.id.iv_voice)
    var ivClearEdit: ImageView = findViewById(R.id.iv_clear_edit)
    var tvTitleEdit: EditText = findViewById(R.id.tv_title_et)
    var tvCancel: TextView = findViewById(R.id.tv_search_history_cancel)

    /**
     * 获取消息气泡view
     */
    fun getBubbleView(): TextView = findViewById(R.id.tv_num)


}