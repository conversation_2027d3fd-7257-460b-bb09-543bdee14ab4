package com.ybmmarket20.view;

import android.content.Context;
import androidx.appcompat.widget.AppCompatButton;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.widget.EditText;

import java.util.ArrayList;

/**
 * 观察者模式实现button
 */

public class ButtonObserver extends AppCompatButton implements TextWatcher {

    ArrayList<EditText> list = new ArrayList<>();

    public ButtonObserver(Context context) {
        this(context, null);
    }

    public ButtonObserver(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ButtonObserver(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * watch 传递过来的editView
     *
     * @param ets EditText 为可变参数
     */
    public void observer(EditText... ets) {
        //遍历所有的et
        for (EditText et : ets) {
            et.addTextChangedListener(this);
            list.add(et);
        }
    }

    public void unObserver(EditText... ets) {
        try {
            for (EditText et : ets) {
                et.addTextChangedListener(this);
                list.add(et);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void unObserverAll() {
        try {
            for (EditText et : list) {
                et.removeTextChangedListener(this);
            }
            list.clear();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        checkButtonStatus();
    }

    /**
     * 检测按钮状态
     */
    public void checkButtonStatus() {
        if (mOnButtonObserverListener != null) {
            mOnButtonObserverListener.onButtonObserver(checkEmpty());
        }
    }

    /**
     * 检查eidtview是否为空
     *
     * @return
     */
    public boolean checkEmpty() {
        boolean isFlag = true;

        for (EditText et : list) {
            if (TextUtils.isEmpty(et.getText().toString().trim())) {
                isFlag = false;
                break;
            }
        }
        return isFlag;
    }

    public interface OnButtonObserverListener {
        void onButtonObserver(boolean isFlag);
    }

    private OnButtonObserverListener mOnButtonObserverListener;

    public void setOnItemClickListener(OnButtonObserverListener listener) {
        this.mOnButtonObserverListener = listener;
    }
}
