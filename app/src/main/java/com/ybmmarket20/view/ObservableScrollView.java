package com.ybmmarket20.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ScrollView;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/4/18.
 */
public class ObservableScrollView extends ScrollView {


    private ScrollListener scrollViewListener = null;

    public ObservableScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    private void init() {

    }

    public ObservableScrollView(Context context, AttributeSet attrs) {
        super(context, attrs, 0);
    }

    public ObservableScrollView(Context context) {
        super(context, null);

    }

    @Override
    protected void onScrollChanged(int x, int y, int oldx, int oldy) {
        super.onScrollChanged(x, y, oldx, oldy);
        if (scrollViewListener != null) {
            scrollViewListener.onScrollChanged(this, x, y, oldx, oldy);
        }
    }

    public void setScrollListener(ScrollListener scrollViewListener) {
        this.scrollViewListener = scrollViewListener;
    }

    public interface ScrollListener {

        void onScrollChanged(ObservableScrollView scrollView, int x, int y, int oldx, int oldy);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return super.onTouchEvent(ev);
    }
}