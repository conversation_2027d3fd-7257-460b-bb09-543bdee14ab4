package com.ybmmarket20.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R

/**
 * 店铺-切换fragment下拉框
 */
class ShopSwitchPopupWindow(val mContext: Context): PopupWindow(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT) {

    var mCallback: ((Int, String) -> Unit)? = null

    init {
        contentView = View.inflate(mContext, R.layout.popwindow_switch_fragment, null)
    }

    // 设置数据
    fun setData(list: MutableList<String>) {
        val rv = contentView.findViewById<RecyclerView>(R.id.rv_switch_fragment)
        rv.layoutManager = WrapLinearLayoutManager(mContext)
        rv.adapter = ShopSwitchFragmentAdapter(list)
        val divider = DividerItemDecoration(mContext, DividerItemDecoration.VERTICAL)
        ContextCompat.getDrawable(mContext, R.drawable.shape_switch_fragment_divider)?.let(divider::setDrawable)
        rv.addItemDecoration(divider)
    }

    fun setClickCallback(block: ((Int, String) -> Unit)?) {
        mCallback = block
    }

    inner class ShopSwitchFragmentAdapter(data: MutableList<String>): YBMBaseAdapter<String>(R.layout.item_switch_fragment, data) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: String?) {
            baseViewHolder?.getView<TextView>(R.id.tv_item)?.text = t
            baseViewHolder?.itemView?.setOnClickListener {
                mCallback?.invoke(baseViewHolder.layoutPosition, t?: "")
            }
        }

    }
}