package com.ybmmarket20.view;

import android.content.Context;
import android.os.Build;
import androidx.viewpager.widget.ViewPager;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.ybmmarket20.R;

/**
 *
 */
public class ClipViewPager extends ViewPager implements ViewPager.PageTransformer {
    public static final float MAX_SCALE = 1.2f;
    public static final float MIN_SCALE = 0.8f;
    //默认距离
    private final static float DISTANCE = 10;
    private float downX;
    private float downY;
    private boolean startTransformer = false;
    private boolean leftClick = false;//左右点击切换位置还是响应点击事件 false 切换位置
    public ClipViewPager(Context context) {
        super(context);
    }

    public ClipViewPager(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if(ev.getAction() == MotionEvent.ACTION_DOWN){
            downX = ev.getX();
            downY = ev.getY();
        }else if (ev.getAction() == MotionEvent.ACTION_UP) {

            float upX = ev.getX();
            float upY = ev.getY();
            //如果 up的位置和down 的位置 距离 > 设置的距离,则事件继续传递,不执行下面的点击切换事件
            if(Math.abs(upX - downX) > DISTANCE || Math.abs(upY - downY) > DISTANCE){
                return super.dispatchTouchEvent(ev);
            }

            View view = viewOfClickOnScreen(ev);
            if (view != null ) {
                if(leftClick){//执行左右点击事件
                    return view.performClick();
                }else {//执行左右位置切换
                    if (view != null) {
                        int index = (Integer) view.getTag(R.id.tag_1);
                        if (getCurrentItem() != index) {
                            setCurrentItem(index,true);
                            return true;
                        }
                    }
                }
            }
        }
        return super.dispatchTouchEvent(ev);
    }

    /**
     * @param ev
     * @return
     */
    private View viewOfClickOnScreen(MotionEvent ev) {
        int childCount = getChildCount();
        int currentIndex = getCurrentItem();
        int[] location = new int[2];
        for (int i = 0; i < childCount; i++) {
            View v = getChildAt(i);
            int position = (Integer) v.getTag(R.id.tag_1);
            v.getLocationOnScreen(location);
            int minX = location[0];
            int minY = location[1];

            int maxX = location[0] + v.getWidth();
            int maxY = location[1] + v.getHeight();

            if(startTransformer){
                if(position < currentIndex){
                    maxX -= v.getWidth() * (1 - MIN_SCALE) * 0.5 + v.getWidth() * (Math.abs(1 - MAX_SCALE)) * 0.5;
                    minX -= v.getWidth() * (1 - MIN_SCALE) * 0.5 + v.getWidth() * (Math.abs(1 - MAX_SCALE)) * 0.5;
                }else if(position == currentIndex){
                    minX += v.getWidth() * (Math.abs(1 - MAX_SCALE));
                }else if(position > currentIndex){
                    maxX -= v.getWidth() * (Math.abs(1 - MAX_SCALE)) * 0.5;
                    minX -= v.getWidth() * (Math.abs(1 - MAX_SCALE)) * 0.5;
                }
            }

            float x = ev.getRawX();
            float y = ev.getRawY();

            if ((x > minX && x < maxX) && (y > minY && y < maxY)) {
                return v;
            }
        }
        return null;
    }

   public void sestScaleTransformPage(boolean start){
       this.startTransformer = start;
       if(start){
           setPageTransformer(true,this);
       }
   }

   public void setLeftClick(boolean click){
       leftClick = click;
   }


    @Override
    public void transformPage(View page, float position) {
        if(!startTransformer){
            return;
        }
        if (position < -1) {
            position = -1;
        } else if (position > 1) {
            position = 1;
        }

        float tempScale = position < 0 ? 1 + position : 1 - position;

        float slope = (MAX_SCALE - MIN_SCALE) / 1;
        //一个公式
        float scaleValue = MIN_SCALE + tempScale * slope;
        page.setScaleX(scaleValue);
        page.setScaleY(scaleValue);

    }
}
