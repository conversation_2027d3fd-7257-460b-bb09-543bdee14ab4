package com.ybmmarket20.view;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.GridView;
import android.widget.LinearLayout;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.adapter.ProductGrid5Adapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ModuleViewProductItemBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class TabProductLayout extends LinearLayout {

    private String text;
    private String action;
    private List<Integer> margin;
    private List<Integer> padding;
    public String apiType;
    private GridView gv;
    HorizontalScrollViewLoadMore sl;
    private ProductGrid5Adapter myGridAdapter;
    private List<RowsBean> rows;
    private String apiHeader = AppNetConfig.HOST;
    private String bgRes;

    private View viewbg;
    private View viewbefore;
    // 是否显示指示条
    private boolean showIndicator = true;
    // 一行显示最大个数
    private int maxCountOneLine = 0;
    // 模块可显示宽度
    private int moudleWidth;

    public TabProductLayout(Context context, String apiType, String action, String bgRes, String text, List<Integer> padding, List<Integer> margin) {
        super(context);
        this.apiType = apiType;
        this.action = action;
        this.bgRes = bgRes;
        this.text = text;
        this.padding = padding;
        this.margin = margin;
        setOrientation(VERTICAL);
        LayoutInflater.from(getContext()).inflate(R.layout.tab_product_layout, this);
        initViews();
    }

    public TabProductLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public TabProductLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }

    public void initViews() {
        sl = ((HorizontalScrollViewLoadMore) findViewById(R.id.sl));
        viewbg = findViewById(R.id.view_indicator_bg);
        viewbefore = findViewById(R.id.view_indicator_before);
        gv = (GridView) findViewById(R.id.cheap_gv);
        gv.setSelector((new ColorDrawable(Color.TRANSPARENT)));
        myGridAdapter = new ProductGrid5Adapter(getContext(), new ArrayList<RowsBean>());
        gv.setAdapter(myGridAdapter);
        myGridAdapter.setOnItemClickListener(new ProductGrid5Adapter.OnGridviewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {

                if (rows != null) {
                    Intent intent = new Intent(getContext(), ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    getContext().startActivity(intent);
                }
            }
        });

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            sl.setOnScrollChangeListener(new OnScrollChangeListener() {
                @Override
                public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                    if (showIndicator) {
                        moveIndicator(scrollX, viewbefore);
                    }
                    TabProductLayout.super.onScrollChanged(scrollX, scrollY, oldScrollX, oldScrollY);
                }
            });
        }
        refresh(true);
    }

    // 设置指示条
    private void moveIndicator(int scrollX, View viewbefore) {
        // 1. 手指滑动距离
        int scrollOffset = scrollX;
        // 2. indicator 滑动距离
        // indicator滑动距离/indicator整体可滑动距离 = 手指滑动距离/recyclerview整体可移动宽度
        // reycyclerview 整体可移动宽度
        int rvWidth = moudleWidth / 3 * maxCountOneLine - moudleWidth + UiUtils.dp2px(27);
        // indicator 整体可移动宽度
        int indicatorWith = computeIndicatorBgWith(maxCountOneLine) - UiUtils.dp2px(20);
        // indicator 滑动距离
        int indicatorTranslation = 0;
        if (rvWidth != 0) {
            indicatorTranslation = (int) (scrollOffset * indicatorWith / rvWidth + 0.5);
        }
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) viewbefore.getLayoutParams();
        layoutParams.leftMargin = indicatorTranslation;
        viewbefore.setLayoutParams(layoutParams);
    }

    public <T extends View> T getView(int viewId) {
        if (this == null) {
            return null;
        }
        try {
            return (T) this.findViewById(viewId);
        } catch (Throwable e) {
            return null;
        }
    }

    private void refresh(boolean show) {

        HttpManager.getInstance().postParser(apiHeader + apiType, new BaseResponse<ModuleViewProductItemBean>() {
            @Override
            public void onSuccess(String content, BaseBean<ModuleViewProductItemBean> obj, ModuleViewProductItemBean rowsBeans) {

                if (obj != null && obj.isSuccess() && rowsBeans != null) {

                    List<RowsBean> rows = rowsBeans.moduleList;
                    if (rows == null) {
                        rows = new ArrayList<>();
                    }
                    if (rows.size() <= 0) {
                        return;
                    }
                    setItemData(rows);
                }

            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (error != null && error.errorCode == NetError.TIME_OUT_ERROR) {
                    refresh(true);
                }
            }
        });
    }

    public void setItemData(List<RowsBean> items) {
        this.rows = items;
        maxCountOneLine = items.size();
        int itemWidth = 0;
        if (getWidth() <= 0) {//默认全屏
            itemWidth = (UiUtils.getScreenWidth() - getPaddingRight() - getPaddingLeft()) / 3;
        } else {
            itemWidth = (UiUtils.getScreenWidth() - getPaddingRight() - getPaddingLeft()) / 3;
        }
        int gvWidth = maxCountOneLine * itemWidth;
        LayoutParams params = new LayoutParams(gvWidth, LayoutParams.MATCH_PARENT, 1);
        gv.setLayoutParams(params);
        gv.setColumnWidth(itemWidth);
        gv.setNumColumns(maxCountOneLine);

        if (maxCountOneLine <= 3) {
            showIndicator = false;
            viewbefore.setVisibility(View.GONE);
            viewbg.setVisibility(View.GONE);
        }

        if (this.rows.size() < 3) {
            findViewById(R.id.iv_more).setVisibility(View.GONE);
        } else {
            findViewById(R.id.iv_more).setVisibility(View.VISIBLE);
            findViewById(R.id.iv_more).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    setZhugeConst();
                    RoutersUtils.open(action);
                }
            });
            ((HorizontalScrollViewLoadMore) findViewById(R.id.sl)).setOnLoadMoreListener(new HorizontalScrollViewLoadMore.OnLoadMoreListener() {
                @Override
                public void onLoadMore() {
                    setZhugeConst();
                    RoutersUtils.open(action);
                }
            });
        }

        myGridAdapter = new ProductGrid5Adapter(getContext(), items);
        gv.setAdapter(myGridAdapter);
        myGridAdapter.setOnItemClickListener(new ProductGrid5Adapter.OnGridviewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {

                if (rows != null) {

                    JSONObject object = new JSONObject();
                    try {
                        object.put("title", text + "");
                        object.put("id", rows.getId() + "");
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    XyyIoUtil.track(XyyIoUtil.ACTION_HOME_CLASSIFICATION_SLIDING, object,rows);

                    Intent intent = new Intent(getContext(), ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    getContext().startActivity(intent);

                }
            }
        });

        initViewAfaterDatas();
    }

    private void setZhugeConst() {
        JSONObject object = new JSONObject();
        try {
            object.put("action", action + "");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        XyyIoUtil.track(XyyIoUtil.ACTION_HOME_CLASSIFICATION_SLIDING_LOADMORE, object);
    }

    private void initViewAfaterDatas() {
        // 设置indicator宽度
        if (showIndicator) {
            ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) viewbg.getLayoutParams();
            layoutParams.width = computeIndicatorBgWith(maxCountOneLine);
            viewbg.setLayoutParams(layoutParams);
        }
        moudleWidth = UiUtils.getScreenWidth();
        if (margin != null && margin.size() >= 4) {
            moudleWidth = moudleWidth - UiUtils.dp2px(margin.get(0)) - UiUtils.dp2px(margin.get(2));
        }
        if (padding != null && padding.size() >= 4) {
            moudleWidth = moudleWidth - UiUtils.dp2px(padding.get(0)) - UiUtils.dp2px(padding.get(2));
        }
    }

    /**
     * @param maxCount 一行最大显示个数
     * @return
     */
    private int computeIndicatorBgWith(int maxCount) {
        return UiUtils.dp2px(40 + (maxCount - 3) * 4);
    }

    public void selPostion(int index) {
        refresh(false);
    }

    private boolean isWhiteBg() {
        if (TextUtils.isEmpty(bgRes) || "#feeaea".equals(bgRes.toLowerCase())) {
            return false;
        }
        return true;
    }

}
