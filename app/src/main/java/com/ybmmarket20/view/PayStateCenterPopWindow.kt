package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import com.lxj.xpopup.core.CenterPopupView
import com.ybmmarket20.R
import kotlinx.android.synthetic.main.pop_layout_pay_state.view.*

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/12/4 15:33
 *    desc   :
 */

class PayStateCenterPopWindow(
    var activity: Context,
    var title: String? = "",
    var contentStr: String? = "",
    var orderNumStr: String? = "",
    var orderCountStr: String? = "",
    var cancelStr: String? = "",
    var confirmStr: String? = "",
    private var cancelCallBack: OnClickListener?,
    private var confirmCallBack: OnClickListener?
) : CenterPopupView(activity) {
    override fun getImplLayoutId() = R.layout.pop_layout_pay_state

    override fun onCreate() {
        if (title.isNullOrEmpty()) {
            tv_title?.visibility = View.GONE
        } else {
            tv_title?.visibility = View.VISIBLE
            tv_title?.text = title
        }
        if (contentStr.isNullOrEmpty()) {
            tv_content?.visibility = View.GONE
        } else {
            tv_content?.visibility = View.VISIBLE
            tv_content?.text = contentStr
        }
        if (cancelStr.isNullOrEmpty()) {
            tv_cancel?.visibility = View.GONE
            view2?.visibility = View.GONE
        } else {
            tv_cancel?.visibility = View.VISIBLE
            view2?.visibility = View.VISIBLE
            tv_cancel?.text = cancelStr
        }
        if (confirmStr.isNullOrEmpty()) {
            tv_confirm?.visibility = View.GONE
            view2?.visibility = View.GONE
        } else {
            tv_confirm?.visibility = View.VISIBLE
            view2?.visibility = View.VISIBLE
            tv_confirm?.text = confirmStr
        }
        if (orderCountStr.isNullOrEmpty() && orderNumStr.isNullOrEmpty()) {
            con1?.visibility = View.GONE
        } else {
            con1?.visibility = View.VISIBLE
        }
        if (orderNumStr.isNullOrEmpty()) {
            tv_order_num?.visibility = View.GONE
        } else {
            tv_order_num?.visibility = View.VISIBLE
            tv_order_num?.text = "待支付订单：$orderNumStr"
        }
        if (orderCountStr.isNullOrEmpty()) {
            tv_order_count?.visibility = View.GONE
        } else {
            tv_order_count?.visibility = View.VISIBLE
            val s1 = SpannableStringBuilder("订单金额： ")
            val s2 = SpannableStringBuilder(orderCountStr)
            orderCountStr?.length?.let {
                s2.setSpan(
                    ForegroundColorSpan(Color.parseColor("#FF4D4D")),
                    0,
                    it,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            val s3 = s1.append(s2)
            tv_order_count?.text = s3
        }

        tv_cancel?.setOnClickListener {
            cancelCallBack?.onClick(tv_cancel)
            dismiss()
        }
        tv_confirm?.setOnClickListener {
            confirmCallBack?.onClick(tv_confirm)
            dismiss()
        }
    }
}