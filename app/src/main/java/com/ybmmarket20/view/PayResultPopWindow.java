package com.ybmmarket20.view;

import androidx.viewpager.widget.ViewPager;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.ybmmarket20.R;
import com.ybmmarket20.adapter.RecyclingPagerAdapter;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;


public class PayResultPopWindow extends BaseCenterPopWindow {

    private ClipViewPager vp_arl;
    private LinearLayout ll_arl;
    private MyPagerAdapter adapter;
    private LinearLayout mRlLayout;
    private LinearLayout llClase;

    private List<Integer> items;

    LinearLayout.LayoutParams layoutParams;

    @Override
    protected int getLayoutId() {
        return R.layout.pay_result_pop;
    }

    @Override
    protected void initView() {

        vp_arl = getView(R.id.vp_arl);
        ll_arl = getView(R.id.ll_arl);
        mRlLayout = getView(R.id.rl_layout);
        llClase = getView(R.id.ll_clase);
        llClase.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        vp_arl.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                    case MotionEvent.ACTION_DOWN:
                        break;
                    case MotionEvent.ACTION_UP:
                        break;
                }
                return false;
            }

        });

        List<Integer> items = new ArrayList<>();
        items.add(R.drawable.icon_operation_explain_01);
        items.add(R.drawable.icon_operation_explain_02);
        initData(items);

    }

    private void initData(List<Integer> items) {
        if (items == null || items.size() == 0) {
            return;
        }
        if (vp_arl == null) {
            return;
        }
        this.items = items;
        adapter = new MyPagerAdapter();
        vp_arl.setAdapter(adapter);
        vp_arl.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                // 改变点的状态
                int size = items.size();
                if (size > 1) {
                    for (int i = 0; i < size; i++) {
                        ll_arl.getChildAt(i).setEnabled(i == position % size);
                    }
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        // 移除上次遗留的所有点
        ll_arl.removeAllViews();
        //重新添加点
        addDots();
        vp_arl.setOffscreenPageLimit(Math.min(2, items.size()));
        layoutParams = (LinearLayout.LayoutParams) vp_arl.getLayoutParams();
        layoutParams.setMargins(ConvertUtils.dp2px(15), 0, ConvertUtils.dp2px(15), 0);
        vp_arl.setLayoutParams(layoutParams);
        vp_arl.setPageMargin(ConvertUtils.dp2px(12));
//        vp_arl.sestScaleTransformPage(true);
//        vp_arl.setLeftClick(true);
        mRlLayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return vp_arl.dispatchTouchEvent(event);
            }
        });
//        if (items.size() > 1) {
//            vp_arl.setCurrentItem(items.size() * 120, false);
//        }

    }

    private void addDots() {
        if (items == null) {
            return;
        }
        if (items.size() == 1) {
            return;
        }
        for (int i = 0; i < items.size(); i++) {
            // 把10dp 转成对应的像素
            View view = new View(contentView.getContext());

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 18, contentView.getContext().getResources()
                            .getDisplayMetrics());

            int dotHight = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 3, contentView.getContext().getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotHight);
            params.setMargins(0, 0, dotHight, 0);
            view.setLayoutParams(params);
            // 指定背景是选择器，在pagechangelistener中只去改变状态，更加面向对象，易于控制
            view.setBackgroundResource(R.drawable.arl_ball_bg_selector03);
            ll_arl.addView(view);
        }

    }

    public class MyPagerAdapter extends RecyclingPagerAdapter {

        @Override
        public int getCount() {
            return items == null ? 0 : items.size();
//            return items == null ? 0 : items.size() > 1 ? Integer.MAX_VALUE : items.size();
        }

        @Override
        public View getView(int position, View convertView, ViewGroup container) {
            final int index = position % items.size();
            ImageView imageView;
            if (convertView == null) {
                imageView = new ImageView(contentView.getContext());
            } else {
                imageView = (ImageView) convertView;
            }
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            imageView.setImageDrawable(contentView.getContext().getResources().getDrawable(items.get(index)));
            imageView.setTag(R.id.tag_1, position);
            return imageView;
        }

    }

    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, UiUtils.getScreenHeight() - ConvertUtils.dp2px(120));
    }
}
