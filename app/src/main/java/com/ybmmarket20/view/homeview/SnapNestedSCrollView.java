package com.ybmmarket20.view.homeview;

import android.content.Context;
import androidx.core.widget.NestedScrollView;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import com.apkfuns.logutils.LogUtils;

/**
 * <AUTHOR> Brin
 * @date : 2019/7/14 - 16:06
 * @Description :
 */
public class SnapNestedSCrollView extends NestedScrollView {

    private boolean needConsume;

    public SnapNestedSCrollView(Context context) {
        super(context);
    }

    public SnapNestedSCrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SnapNestedSCrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void onNestedPreScroll(View target, int dx, int dy, int[] consumed) {
        //super.onNestedPreScroll(target, dx, dy, consumed);
        if (needConsume) {
            consumed[0] = dx;
            consumed[1] = dy;
            scrollBy(0, dy);
            LogUtils.e("SnapNestedSCrollView dy = " + dy);
        }
    }

    @Override
    public boolean onNestedPreFling(View target, float velocityX, float velocityY) {
        //return super.onNestedPreFling(target, velocityX, velocityY);
        if (needConsume) {
            fling((int) velocityY);
        }
        //Log.e("SnapNestedSCrollView", "fling velocityY = " + velocityY + "needConsume = " + needConsume);
        return needConsume;
    }

    @Override
    public boolean onStartNestedScroll(View child, View target, int nestedScrollAxes) {
        return super.onStartNestedScroll(child, target, nestedScrollAxes);
    }

    public void setNeedConsume(boolean needConsume) {
        if (this.needConsume == needConsume){
            return;
        }
        this.needConsume = needConsume;
        String logstr = "SnapNestedScrollView needConsume = " + needConsume;
        Log.e("xyd",logstr);
    }
}
