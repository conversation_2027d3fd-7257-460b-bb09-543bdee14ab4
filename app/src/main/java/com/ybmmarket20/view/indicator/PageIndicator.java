/*
 * Copyright (C) 2011 <PERSON><PERSON>
 * Copyright (C) 2011 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ybmmarket20.view.indicator;


import androidx.recyclerview.widget.RecyclerView;

/**
 * A PageIndicator is responsible to show an visual indicator on the total views
 * number and the current visible view.
 */
public interface PageIndicator extends OnPageChangeListener {
    /**
     * Bind the indicator to a RecyclerView.
     *
     * @param view
     */
    void setRecyclerView(RecyclerView view);

    /**
     * Bind the indicator to a RecyclerView.
     *
     * @param view
     * @param initialPosition
     */
    void setRecyclerView(RecyclerView view, int initialPosition);

    /**
     * <p>Set the current page of both the RecyclerView and indicator.</p>
     * <p>
     * <p>This <strong>must</strong> be used if you need to set the page before
     * the views are drawn on screen (e.g., default start page).</p>
     *
     * @param item
     */
    void setCurrentItem(int item);

    /**
     * Set a page change listener which will receive forwarded events.
     *
     * @param listener
     */
    void setOnPageChangeListener(OnPageChangeListener listener);

    /**
     * Notify the indicator that the fragment list has changed.
     */
    void notifyDataSetChanged();

    /**
     * Set a page column
     *
     * @param column
     */
    void setPageColumn(int column);
}
