package com.ybmmarket20.view

import android.app.Activity
import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ybmmarket20.R
import com.ybmmarket20.adapter.RebateTableAdapter
import com.ybmmarket20.bean.payment.ActResultListItem
import com.ybmmarket20.bean.payment.PaymentConsumeRebateDetailBean
import com.ybmmarket20.constant.AppNetConfig

/**
 * 返利比例底部弹窗
 */
class RebateRatioBottomDialog @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var tvRebateRatio: TextView? = null
    private var tvRebateAmount: TextView? = null
    private var tvRebateTitle: TextView? = null
    private var ivTopRowTitle: TextView? = null
    private var ivClose: ImageView? = null
    private var rvRebateTable: RecyclerView? = null
    private var rebateDialogContent: View? = null

    private lateinit var tableAdapter: RebateTableAdapter
    private var tableData: MutableList<ActResultListItem>? = null
    private var onButtonClickListener: OnButtonClickListener? = null

    init {
        initView()
    }

    private fun initView() {
        // 加载布局文件
        LayoutInflater.from(context).inflate(R.layout.dialog_rebate_ratio_bottom, this, true)

        // 初始化子视图
        initChildViews()

        // 初始化表格数据
        initTableData()

        // 设置点击事件
        setupClickListeners()

        // 默认隐藏
        visibility = GONE
    }

    private fun initChildViews() {
        tvRebateRatio = findViewById(R.id.tv_rebate_ratio)
        tvRebateAmount = findViewById(R.id.tv_rebate_amount)
        tvRebateTitle = findViewById(R.id.tv_rebate_title)
        ivTopRowTitle= findViewById(R.id.iv_toprow_title)
        ivClose = findViewById(R.id.iv_close)
        rvRebateTable = findViewById(R.id.rv_rebate_table)
        rebateDialogContent = findViewById(R.id.rebate_dialog_content)
    }

    private fun setupClickListeners() {
        // 设置关闭按钮点击事件
        ivClose?.setOnClickListener {
            hideDialog()
            onButtonClickListener?.onCloseClick()
        }

        ivTopRowTitle?.setOnClickListener {
            // 显示活动规则详情H5页面
            showActivityRulesWebView()
        }

        // 点击整个view的任何地方都关闭弹窗（蒙层点击）
        rebateDialogContent?.setOnClickListener {
            hideDialog()
            onButtonClickListener?.onCloseClick()
        }

        // 阻止弹窗内容区域的点击事件冒泡
        findViewById<View>(R.id.dialog_content)?.setOnClickListener {
            // 空实现，阻止点击事件冒泡到外层
        }
    }

    /**
     * 初始化表格数据
     */
    private fun initTableData() {
        // 初始化tableData如果还没有初始化
        if (tableData == null) {
            tableData = mutableListOf()
        }
        tableAdapter = RebateTableAdapter(context, tableData ?: arrayListOf())

        // 设置 RecyclerView
        rvRebateTable?.layoutManager = LinearLayoutManager(context)
        rvRebateTable?.adapter = tableAdapter
    }

    /**
     * 显示弹窗
     */
    fun showDialog() {
        visibility = VISIBLE
    }

    /**
     * 隐藏弹窗
     */
    fun hideDialog() {
        visibility = GONE
        // 清理倒计时器
        if (::tableAdapter.isInitialized) {
            tableAdapter.clearAllTimers()
        }
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == VISIBLE
    }

    /**
     * 设置按钮点击监听器
     */
    fun setOnButtonClickListener(listener: OnButtonClickListener) {
        this.onButtonClickListener = listener
    }

    /**
     * 更新数据
     */
    fun updateData(newData: PaymentConsumeRebateDetailBean) {
        if (newData.levelScene == "1" || newData.levelScene.isNullOrEmpty()) {
            var tvRebateContent: LinearLayout = findViewById(R.id.tv_rebate_content)
            tvRebateContent.visibility = View.GONE
        } else {
            tvRebateTitle?.text = getSubRichTextWithTitle(newData); //标题赋值
            tvRebateRatio?.text = getSubRichTextWithPrice(newData); // 价格和比例赋值
            tvRebateAmount?.text = getSubRichTextWithBottom(newData);//底部文字赋值
        }

        // 初始化tableData如果还没有初始化
        if (tableData == null) {
            tableData = mutableListOf()
        }

        tableData?.clear()

        newData.actResultList?.forEachIndexed { index, item ->
            //从PaymentConsumeRebateDetailBean获取倒计时并赋值
            if (item.extraSubsidyAmountRate != null) {
                item.toEtimeSeconds = newData.toEtimeSeconds;
            }
            //斑马线显示
            item.isZebraStripe = index % 2 == 1;
            tableData?.add(item);
        }
        if (::tableAdapter.isInitialized) {
            tableAdapter.notifyDataSetChanged()
        }
    }

    /**
     * 获取对应的标题
     * 场景 1.未参与活动 2.参与活动未达门槛 3.参与活动已达门槛 4.已经达到最高门槛
     * */
    fun getSubRichTextWithTitle(newData: PaymentConsumeRebateDetailBean): CharSequence? {
        when (newData.levelScene) {
            "2" -> {
                return "返利比例";
            }

            "3" -> {
                return "订单完成获得红包";
            }

            "4" -> {
                return "订单完成获得红包";
            }
        }
        return null
    }

    /**
     * 获取对应的价格或者比例（富文本）
     * 场景 1.未参与活动 2.参与活动未达门槛 3.参与活动已达门槛 4.已经达到最高门槛
     * */
    fun getSubRichTextWithPrice(newData: PaymentConsumeRebateDetailBean): CharSequence? {
        when (newData.levelScene) {
            "2" -> {
                return getSpannableString(
                    "${newData.nextLevelRate}%起",
                    "${newData.nextLevelRate}%起",
                    true
                );
            }

            "3" -> {
                return getSpannableString(
                    "${newData.realAmount}元",
                    "${newData.realAmount}元",
                    true
                )
            }

            "4" -> {
                return getSpannableString(
                    "${newData.realAmount}元",
                    "${newData.realAmount}元",
                    true
                )
            }
        }
        return null
    }

    /**
     * 获取对应的底部文字（富文本）
     * 场景 1.未参与活动 2.参与活动未达门槛 3.参与活动已达门槛 4.已经达到最高门槛
     * */
    fun getSubRichTextWithBottom(newData: PaymentConsumeRebateDetailBean): CharSequence? {
        when (newData.levelScene) {
            "2" -> {
                //如果是底部提示
                var contentTxt = "仅差${newData.nextLevelShortAmount}元可参与活动"
                return getSpannableString(contentTxt, "${newData.nextLevelShortAmount}元", false)
            }

            "3" -> {
                if(newData.nextLevelShortAmount == null || newData.nextLevelShortAmount!!.toDouble() <= 0){
                    return "";
                }
                //如果是底部提示
                var contentTxt = "仅差${newData.nextLevelShortAmount}元"
                var contentNext = "可得${newData.nextLevelRedPacketAmount}元红包"
                return SpannableStringBuilder().apply {
                    append(
                        getSpannableString(
                            contentTxt,
                            "${newData.nextLevelShortAmount}元",
                            false
                        )
                    )
                    append(
                        getSpannableString(
                            contentNext,
                            "${newData.nextLevelRedPacketAmount}元",
                            false
                        )
                    )
                }
            }

            "4" -> {
                //如果是底部提示
                var contentTxt = "多买可得，最高可得${newData.maxReturnRedPackageAmount}元红包"
                return getSpannableString(
                    contentTxt,
                    "${newData.maxReturnRedPackageAmount}元",
                    false
                )
            }
        }
        return null
    }

    //设置富文本颜色和字体， spanTextfix：全部文字， spanText：变色文字， needFontChange：是否需要在.或者%后做字体大小差异
    private fun getSpannableString(
        spanTextfix: String,
        spanText: String,
        needFontChange: Boolean
    ): SpannableStringBuilder {
        val spannableString = SpannableStringBuilder(spanTextfix)
        val startIndex = spanTextfix.indexOf(spanText)

        // 如果找不到目标文本，直接返回原始文本
        if (startIndex == -1) {
            return spannableString
        }

        val endIndex = startIndex + spanText.length

        // 设置颜色
        spannableString.setSpan(
            ForegroundColorSpan(
                ContextCompat.getColor(
                    context,
                    R.color.color_FF2121
                )
            ), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )

        //设置字体
        spannableString.setSpan(
            AbsoluteSizeSpan(17, true), // true表示单位是sp
            startIndex,
            endIndex,
            Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        )

        // 如果需要字号变化，处理小数点或者前后的字号
        if (needFontChange) {
            val decimalIndex =
                if (spanText.indexOf("%") != -1) spanText.indexOf("%") else spanText.indexOf(".")
            if (decimalIndex != -1) {
                // 小数点前的部分（整数部分）- 字号36sp
                val integerEndIndex = startIndex + decimalIndex
                spannableString.setSpan(
                    AbsoluteSizeSpan(36, true), // true表示单位是sp
                    startIndex,
                    integerEndIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )

                // 小数点及小数点后的部分 - 字号30sp
                spannableString.setSpan(
                    AbsoluteSizeSpan(30, true), // true表示单位是sp
                    integerEndIndex,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            } else {
                // 如果没有小数点，整个数字使用36sp
                spannableString.setSpan(
                    AbsoluteSizeSpan(36, true),
                    startIndex,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
        }

        return spannableString
    }

    /**
     * 显示活动规则详情WebView弹窗
     */
    private fun showActivityRulesWebView() {
        val activity = context as? Activity ?: return

        // 创建并显示WebView弹窗
        ActivityRulesWebViewDialog.show(activity, AppNetConfig.CONSUMER_REBATE_EXPLAIN)
    }

    companion object {
        /**
         * 创建并显示弹窗
         */
        @JvmStatic
        fun show(activity: Activity): RebateRatioBottomDialog {
            // 创建弹窗实例
            val dialog = RebateRatioBottomDialog(activity)

            // 获取Activity的根视图（DecorView）来确保全屏覆盖
            val rootView = activity.window.decorView as ViewGroup

            // 设置全屏布局参数
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            // 添加到DecorView，确保覆盖整个屏幕包括状态栏
            rootView.addView(dialog, layoutParams)

            // 显示弹窗
            dialog.showDialog()

            return dialog
        }

        /**
         * 隐藏并移除弹窗
         */
        @JvmStatic
        fun hide(dialog: RebateRatioBottomDialog?) {
            dialog?.let {
                it.hideDialog()
                // 从父视图中移除
                (it.parent as? ViewGroup)?.removeView(it)
            }
        }
    }

    interface OnButtonClickListener {
        fun onCloseClick()
    }
}
