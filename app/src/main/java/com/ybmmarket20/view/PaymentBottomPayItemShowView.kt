package com.ybmmarket20.view

import android.content.Context
import android.text.SpannableStringBuilder
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.utils.ImageLoader
import kotlinx.android.synthetic.main.view_payment_bottom_pay_item_show.view.ivPayTypeLogo
import kotlinx.android.synthetic.main.view_payment_bottom_pay_item_show.view.rtvPayTypeMtkTips
import kotlinx.android.synthetic.main.view_payment_bottom_pay_item_show.view.tvPayTypeChangeCard
import kotlinx.android.synthetic.main.view_payment_bottom_pay_item_show.view.tvPayTypeTitle
import kotlinx.android.synthetic.main.view_payment_bottom_pay_item_show.view.tvPayTypeTitleTips

/**
 * 提单页底部展示支付方式
 */
class PaymentBottomPayItemShowView(context: Context, attr: AttributeSet?) :
    ConstraintLayout(context, attr) {

    //换卡监听
    var bottomChangePayTypeCallback: ((View)->Unit)? = null

    init {
        inflate(context, R.layout.view_payment_bottom_pay_item_show, this)
    }

    fun setData(payItemShowBean: PayItemShowBean) {
        ImageLoader.loadImage(context, ivPayTypeLogo, payItemShowBean.payTypeLogo)
        tvPayTypeTitle.text = payItemShowBean.payTypeTitle
        tvPayTypeTitleTips.text = payItemShowBean.payTypeTitleTips
        rtvPayTypeMtkTips.text = payItemShowBean.payTypeMktTips
        rtvPayTypeMtkTips.visibility = if (payItemShowBean.payTypeMktTips.isNullOrEmpty()) View.GONE else View.VISIBLE
        tvPayTypeChangeCard.setOnClickListener(bottomChangePayTypeCallback)
        ivPayTypeLogo.visibility = if (payItemShowBean.payTypeLogo.isNullOrEmpty()) View.GONE else View.VISIBLE
    }

    data class PayItemShowBean(
        val payTypeLogo: String?,
        val payTypeMktTips: String?,
        var payTypeTitle: String?,
        var payTypeTitleTips: SpannableStringBuilder?
    )

}