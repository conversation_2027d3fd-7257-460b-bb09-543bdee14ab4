package com.ybmmarket20.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.AllianceGoodsBean;

/**
 * Author ： LoveNewsweetheart
 * Date:2019/7/19
 */
public class AllianceListHeaderLayout extends LinearLayout {

    private Context mContext;
    
    public AllianceListHeaderLayout(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AllianceListHeaderLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    public AllianceListHeaderLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init();
    }

    private void init(){
        LayoutInflater.from(mContext).inflate(R.layout.item_header_alliance_list, this, true);
    }


    public void setData(AllianceGoodsBean bean){
        setText(R.id.tv_changshi, bean.getCommonSense());
        setText(R.id.tv_changjianzhengzhuang, bean.getPatientSymptoms());
        setText(R.id.tv_jianyiguke, bean.getAdvisePatients());
        setText(R.id.tv_yongyaoyuanze, bean.getMedicationPrinciple());
        setText(R.id.tv_changjianyongyao, bean.getGeneralEffect());
    }

    private void setText(int resId, String content){
        TextView tv = findViewById(resId);
        tv.setText(content);
    }
}
