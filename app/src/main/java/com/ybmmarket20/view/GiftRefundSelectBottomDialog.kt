package com.ybmmarket20.view

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.common.dp
import com.ybmmarket20.fragments.GiftRefundSelectFragment
import com.ybmmarket20.viewmodel.GiftRefundSelectBottomDialogVM

/**
 * @class   GiftRefundSelectBottomDialog
 * <AUTHOR>
 * @date  2024/9/11  选择退还赠品底部弹窗
 * @description
 */
class GiftRefundSelectBottomDialog(
    val mContext: FragmentActivity,
    val mData: MutableList<RefundProductListBean>,
    private val mGiftPromotionId: String,
    private val mSelectAmount: Int,
    private val mCallback: ((dialog: GiftRefundSelectBottomDialog, list: MutableList<RefundProductListBean>, giftPromotionId: String) -> Unit)? = null
) : DialogFragment() {

    companion object {
        const val GIFT_SELECT_DEFAULT_TITLE = "选择退还赠品"
    }

    var mTitle: String? = GIFT_SELECT_DEFAULT_TITLE
        set(value) {
            field = value
            val titleTextView: TextView? = dialog?.findViewById(R.id.title)
            titleTextView?.post {
                titleTextView.text = value
            }
        }

    private val mViewModel: GiftRefundSelectBottomDialogVM by lazy {
        ViewModelProvider(mContext)[GiftRefundSelectBottomDialogVM::class.java]
    }

    fun show() {
        show(mContext.supportFragmentManager, "GiftRefundSelectBottomDialog")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        return View.inflate(mContext, getLayoutResID(), null)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setCancelable(false) // 设置点击屏幕Dialog不消失
        initObserver()
        setTitleContent(mTitle)
        handleUI(mData)

        val window = dialog?.window
        val layoutParams = window?.attributes
        layoutParams?.gravity = Gravity.BOTTOM // 底部显示
        layoutParams?.height = 452.dp
        layoutParams?.width = LayoutParams.MATCH_PARENT
        window?.attributes = layoutParams
    }

    private fun initObserver() {
        dialog?.findViewById<ImageView>(R.id.iv_close)?.setOnClickListener {
            this.dismiss()
        }
    }

    private fun handleUI(data: MutableList<RefundProductListBean>) {
        childFragmentManager.beginTransaction()
            .add(R.id.framelayout_gift_select, GiftRefundSelectFragment(data, mGiftPromotionId, mSelectAmount) {list, id ->
                mCallback?.invoke(this@GiftRefundSelectBottomDialog, list, id)
            })
            .commit()
    }

    private fun setTitleContent(mTitle: String?) {
        dialog?.findViewById<TextView>(R.id.tv_title)?.let {
            it.text = mTitle ?: ""
        }
    }


    private fun getLayoutResID(): Int = R.layout.dialog_gift_refund_select_bottom


}