package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.VelocityTracker;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Scroller;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.CartActivityBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;

/**
 * 购物车顶部提示视图
 */
public class CartViewLayout extends LinearLayout {

    private ItemClick itemClick = new ItemClick();
    private boolean isFlag = true;

    private static final int MODULEVIEWTYPEIMAGEVIEW = 0;
    private static final int MODULEVIEWTYPETEXTVIEW = 1;
    private static final int TITLEGRAVITYLEFT = 0;
    private static final int TITLEGRAVITYCENTER = 1;

    public CartViewLayout(Context context) {
        this(context, null);
    }

    public CartViewLayout(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CartViewLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    public void initViews() {
        setOrientation(HORIZONTAL);
    }

    /**
     * @param moduleView 数据
     */
    public void setData(CartActivityBean moduleView) {
        if (moduleView == null) {
            this.setVisibility(View.GONE);
            return;
        }
        setVisibility(View.VISIBLE);
        ImageView imageView = null;
        TextView textView = null;
        TextView couponTv = null;
        LayoutParams params =null;
        removeAllViews();

        //图片url和购物车头部显示title都为空就不显示,再加个条件，显示优惠券的字段为false
        if (TextUtils.isEmpty(moduleView.imgUrl) && TextUtils.isEmpty(moduleView.text) && !moduleView.isShowVoucher()) {
            this.setVisibility(View.GONE);
        }

        switch (moduleView.dataType) {
            case MODULEVIEWTYPEIMAGEVIEW:
            default:
                if (!TextUtils.isEmpty(moduleView.imgUrl)) {
                    imageView = new ImageView(getContext());
                }
                break;
            case MODULEVIEWTYPETEXTVIEW:
                if (!TextUtils.isEmpty(moduleView.text)) {
                    textView = new TextView(getContext());
                }
                break;
        }
        if (moduleView.isShowVoucher()) {
            couponTv = new TextView(getContext());
        }

        if(moduleView.isShowVoucher()){
            params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT,1);
        }else{
            params = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        }
        if (moduleView.height > 0) {
            params.height = ConvertUtils.dp2px(moduleView.height);
        }
        if (moduleView.width > 0) {
            params.width = moduleView.width;
        }
        if (moduleView.padding != null && moduleView.padding.size() > 0) {
            if (imageView != null) {
                imageView.setPadding(ConvertUtils.dp2px(moduleView.padding.get(0)),
                        ConvertUtils.dp2px(moduleView.padding.get(1)),
                        ConvertUtils.dp2px(moduleView.padding.get(2)),
                        ConvertUtils.dp2px(moduleView.padding.get(3)));
            }
            if (textView != null) {
                textView.setPadding(ConvertUtils.dp2px(moduleView.padding.get(0)),
                        ConvertUtils.dp2px(moduleView.padding.get(1)),
                        ConvertUtils.dp2px(moduleView.padding.get(2)),
                        ConvertUtils.dp2px(moduleView.padding.get(3)));
            }
        }
        if (moduleView.margin != null && moduleView.margin.size() > 0) {
            params.setMargins(ConvertUtils.dp2px(moduleView.margin.get(0)),
                    ConvertUtils.dp2px(moduleView.margin.get(1)),
                    ConvertUtils.dp2px(moduleView.margin.get(2)),
                    ConvertUtils.dp2px(moduleView.margin.get(3)));
        }
        if (imageView != null) {
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            imageView.setLayoutParams(params);
            setImage(imageView, moduleView);
            imageView.setTag(R.id.tag_action, moduleView.action);
            imageView.setOnClickListener(itemClick);
            if (isFlag) {
                imageView.setVisibility(View.VISIBLE);
            } else {
                imageView.setVisibility(View.GONE);
            }
            addView(imageView);
        }
        if (textView != null) {
            textView.setLayoutParams(params);
            setTextView(textView, moduleView);
            textView.setTag(R.id.tag_action, moduleView.action);
            textView.setOnClickListener(itemClick);
            if (isFlag) {
                textView.setVisibility(View.VISIBLE);
            } else {
                textView.setVisibility(View.GONE);
            }
            addView(textView);
        }
        if (couponTv != null) {
            LayoutParams layoutParams =null;
            if(TextUtils.isEmpty(moduleView.text)){
                layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            }else{
                layoutParams = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            }
            layoutParams.height = ConvertUtils.dp2px(33);
            couponTv.setLayoutParams(layoutParams);
            setDiscountCouponTextView(couponTv);
            couponTv.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    new CartCouponPopWindow().show(v);
                }
            });
            addView(couponTv);
        }
    }

    /*
    * 设置文字
    * */
    private void setTextView(TextView textView, CartActivityBean bean) {
        if (textView == null) {
            return;
        }
        textView.setText(bean.text);

//        textView.setSingleLine();
//        textView.setMaxLines(1);
//        textView.setEllipsize(TextUtils.TruncateAt.END);

        if (!TextUtils.isEmpty(bean.backgroundColor)) {//设置方案的颜色
            if (getColor(bean.backgroundColor) != 0) {
                textView.setBackgroundColor(getColor(bean.backgroundColor));
                //textView是什么颜色，背景就是什么颜色
                setBackgroundColor(getColor(bean.backgroundColor));
            }
        }
        if (!TextUtils.isEmpty(bean.color)) {
            if (getColor(bean.color) != 0) {
                textView.setTextColor(getColor(bean.color));
            }
        }
        if (bean.fontSize > 0) {
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, bean.fontSize);
        }

        if (!TextUtils.isEmpty(bean.action)&&bean.isShowVoucher()) {
            textView.setCompoundDrawablesWithIntrinsicBounds(null
                    , null, getDrawable(R.drawable.icon_cart_right), null);
        } else {
            textView.setCompoundDrawablesWithIntrinsicBounds(null
                    , null, null, null);
        }

        switch (bean.align) {
            case TITLEGRAVITYLEFT:
                textView.setGravity(Gravity.CENTER_VERTICAL | Gravity.LEFT);
                break;
            case TITLEGRAVITYCENTER:
            default:
                textView.setGravity(Gravity.CENTER);
                break;
        }

    }

    /**
     * 设置优惠券按钮样式
     *
     * @param textView
     */
    private void setDiscountCouponTextView(TextView textView) {
        if (textView == null) {
            return;
        }
        final String discountCouponName = "优惠券";
        final String discountCouponBackgroundColor = "#fff7ef";
        final String discountCouponColor = "#99664D";
        final int discountCouponSize = 14;

        textView.setText(discountCouponName);

        textView.setBackgroundColor(getColor(discountCouponBackgroundColor));

        textView.setTextColor(getColor(discountCouponColor));

        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, discountCouponSize);

        textView.setGravity(Gravity.CENTER_VERTICAL | Gravity.RIGHT);

        textView.setPadding(0, 0, ConvertUtils.dp2px(10), 0);
    }

    /*
    * 设置图片
    * */
    private void setImage(ImageView view, CartActivityBean bean) {
        if (view == null) {
            return;
        }
        String url = bean.imgUrl;
        if (!url.startsWith("http")) {
            url = AppNetConfig.CDN_HOST + bean.imgUrl;
        }
        ImageHelper.with(getContext()).load(url).dontTransform().dontAnimate()
                .diskCacheStrategy(DiskCacheStrategy.SOURCE).into(view);
    }

    /*
    * 通用点击跳转
    * */
    private class ItemClick implements OnClickListener {
        @Override
        public void onClick(View v) {
            String action = (String) v.getTag(R.id.tag_action);
            if (!TextUtils.isEmpty(action)) {
                RoutersUtils.open(action);
            } else {
                isFlag = false;
                v.setVisibility(View.GONE);
            }
        }
    }

    /*
    * 色值
    * */
    protected int getColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }


    /*
    * Drawable资源
    * */
    private Drawable getDrawable(int id) {
        return getResources().getDrawable(id);
    }


}
