package com.ybmmarket20.view;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.InputType;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.RotateAnimation;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.PlanProductInfoBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.DialogUtil;

/**
 * 商品增加、减少、输入布局
 */
public class ProductEditLayout2 extends RelativeLayout {
    private long lastTime;
    protected static RequestParams editShopNumberParams;
    public final static int DIFF_TIME = 400;
    private int ADD_STEP = 1;//加号处理
    private boolean needHide = false;//true 可以收起 false不能收起
    protected ImageView iv_numSub;
    protected ImageView iv_numAdd;
    protected TextView tv_number;
    protected int number;
    private String proId;
    private String planId;
    protected ViewGroup rootView;
    private PlanProductInfoBean bean;

    public ProductEditLayout2(Context context) {
        this(context, null);
    }

    public ProductEditLayout2(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ProductEditLayout2(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews(getBackground() == null);
    }

    public void bindData(PlanProductInfoBean bean) {
        bindData(bean,false,1);
    }

    public void bindData(PlanProductInfoBean bean,boolean needHide, int step) {
        this.proId = bean.code;
        this.planId = bean.planningScheduleId+"";
        this.bean = bean;
        this.number = bean.purchaseNumber;
        this.needHide = needHide;
        if (step <= 0) {
            step = 1;
        }
        ADD_STEP = step;
        tv_number.setInputType(InputType.TYPE_NULL);
        //初始化商品数量
        tv_number.setTag(R.id.tag_3, false);
        tv_number.setText(String.valueOf(0));
        tv_number.setTextColor(getResources().getColor(R.color.coupon_limit_tv01));
        tv_number.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        tv_number.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(final View v) {
                if (v.getContext() instanceof BaseActivity) {
                    ((BaseActivity) v.getContext()).hideSoftInput();
                }
                final TextView textNum = (TextView) v;
                //编辑弹出对话框加减数量
                DialogUtil.addOrSubDialog(((BaseActivity) v.getContext()), InputType.TYPE_CLASS_NUMBER, textNum.getText().toString(), ADD_STEP
                        , true,true, new DialogUtil.DialogClickListener() {

                            private InputMethodManager mImm;

                            @Override
                            public void confirm(String content) {
                                sendShopNum(content);
                            }

                            @Override
                            public void cancel() {

                            }

                            @Override
                            public void showSoftInput(View view) {
                                mImm = (InputMethodManager) (view.getContext()).getSystemService(Context.INPUT_METHOD_SERVICE);
                                mImm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                            }

                        });
            }
        });
        if (number < 0) {
            number = 0;
        }
        //如果是小于0并且需要隐藏，那么才隐藏,否则不隐藏
        boolean initSate = number <= 0 && needHide;
        iv_numSub.setVisibility(initSate ? GONE : VISIBLE);
        tv_number.setVisibility(initSate ? GONE : VISIBLE);
        if (number >= 0) {
            tv_number.setText(String.valueOf(number));
        }
        iv_numSub.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                numOnClick(false);
            }
        });
        iv_numAdd.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                numOnClick(true);
            }
        });
    }

    public void initViews(boolean def) {
        View view = View.inflate(getContext(), getLayoutId(), this);
        iv_numSub = (ImageView) view.findViewById(R.id.iv_numSub);
        iv_numAdd = (ImageView) view.findViewById(R.id.iv_numAdd);
        tv_number = (TextView) view.findViewById(R.id.tv_number);
        if (!def) {
            LayoutParams params = (LayoutParams) tv_number.getLayoutParams();
            params.setMargins(0, 0, 0, 0);
        }
    }


    public int getLayoutId() {
        return R.layout.product_edit_layout;
    }

    private boolean checkStatus(int status) {
        if (status == 2) {
            ToastUtils.showShort("产品已经售罄");
            return false;
        } else if (status == 4) {
            ToastUtils.showShort("产品已经下架不能购买");
            return false;
        }
        return true;
    }

    public void numOnClick(boolean isAdd) {
        //获取商品的数量
        String str = tv_number.getText().toString();
        int shopNum = Integer.valueOf(str);
        number = shopNum;
        if (!isAdd) {
            number -= ADD_STEP;
            if (number < 1) {
                number = 1;
            }
        } else {
            if (number <= 0) {//如果本来是小于0的，添加的时候显示出来
                iv_numSub.setVisibility(VISIBLE);
                tv_number.setVisibility(VISIBLE);
                iv_numSub.setAnimation(showReduceViewAnim());
                tv_number.setAnimation(showTranslateAnim());
                tv_number.setTag(R.id.tag_3, true);
            }
            number += ADD_STEP;
        }
        tv_number.setText(number + "");
        if (!isAdd) {
            isFastDoubleClick(false, number);
        } else {
            isFastDoubleClick(true, number);
        }
    }

    private boolean isFastDoubleClick(boolean isAdd, int number) {
        if (tv_number == null || TextUtils.isEmpty(proId)) {
            return false;
        }
        executeLast(number, isAdd);
        if (lastTime <= 0 || System.currentTimeMillis() - lastTime < DIFF_TIME) {
            lastTime = System.currentTimeMillis();
            return true;
        } else {
            tv_number.getHandler().removeCallbacks(lastRunnable);
            lastTime = System.currentTimeMillis();
            editShopNumber(proId, isAdd, number, tv_number);//执行切换的任务
        }
        return false;
    }

    /**
     * 页面关闭时调用
     */
    private void clean() {
        editShopNumberParams = null;
    }

    private void executeLast(int number, boolean lastIsAdd) {
        if (this == null || tv_number == null || TextUtils.isEmpty(proId)) {
            return;
        }
        if (lastRunnable == null) {
            return;
        }
        tv_number.getHandler().removeCallbacks(lastRunnable);
        lastRunnable.setData(proId, number, lastIsAdd);
        tv_number.getHandler().postDelayed(lastRunnable, (long) (DIFF_TIME * 1.2));
    }

    private class LastRunnable implements Runnable {
        private String proId;
        private int number;
        private boolean lastIsAdd;

        @Override
        public void run() {
            if (this == null || tv_number == null) {
                return;
            }
            if (System.currentTimeMillis() - lastTime < DIFF_TIME) {
                return;
            }
            editShopNumber(proId, lastIsAdd, number, tv_number);//执行切换的任务
        }

        public void setData(String proId, int number, boolean lastIsAdd) {
            this.proId = proId;
            this.lastIsAdd = lastIsAdd;
            this.number = number;
        }
    }

    //最后的任务
    private LastRunnable lastRunnable = new LastRunnable();


    public void sendShopNum(String str) {
        //获取商品的数量
        int num = 0;
        try {
            num = Integer.parseInt(str);
        } catch (Exception e) {
            num = 0;
        }
        if (num <= 0) {
            num = 0;
        }
        boolean isAdd = false;
        if (number <= 0 && num > 0) {
            tv_number.setTag(R.id.tag_3, true);
        }
        isAdd = num > number;
        number = num;
        tv_number.setText(number + "");
        editShopNumber(proId,isAdd, number, tv_number);
    }

    //修改商品发送到服务器
    private void editShopNumber(final String id, final boolean isAdd,final int number, final TextView tv_number) {
        if (number <= 0) {
            return;
        }
        //服务器同步
        if (editShopNumberParams == null) {
            editShopNumberParams = new RequestParams();
            editShopNumberParams.put("merchantId", HttpManager.getInstance().getMerchant_id());
            editShopNumberParams.setUrl(AppNetConfig.PLAN_PRODUCT_MODIFY);
        }
        editShopNumberParams.put("purchaseNumber", String.valueOf(number));
        editShopNumberParams.put("code", id);
        editShopNumberParams.put("planningScheduleId", planId);
        HttpManager.getInstance().post(editShopNumberParams, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean cartDataBean) {
                lastTime = 0;
                if (null != obj) {
                    if (obj.isSuccess()) {
                        if (id == proId) {
                            if (tv_number != null && ProductEditLayout2.this.number == number) {
                                tv_number.setText(number + "");
                                bean.purchaseNumber = number;
                                Intent intent = new Intent(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
                                intent.putExtra("code",proId);
                                intent.putExtra("num",number);
                                intent.putExtra("from",2);
                                LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                            }
                        }
                    } else {
                        if (proId == id && tv_number != null) {
                            //原来值
                            tv_number.setText(bean.purchaseNumber + "");
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (proId == id && tv_number != null) {
                    //原来值
                    tv_number.setText(bean.purchaseNumber + "");
                }
            }
        });
    }

    /**
     * 显示减号的动画
     */
    private AnimationSet showReduceViewAnim() {
        AnimationSet set = new AnimationSet(true);
        RotateAnimation rotate = new RotateAnimation(0, 720, RotateAnimation.RELATIVE_TO_SELF, 0.5f, RotateAnimation.RELATIVE_TO_SELF, 0.5f);
        set.addAnimation(rotate);
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 2f
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if ( iv_numAdd != null) {
                    iv_numAdd.setImageResource(R.drawable.icon_add_gray);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        set.addAnimation(translate);
        set.setDuration(500);
        return set;
    }


    /**
     * 隐藏减号的动画
     */
    private AnimationSet hideReduceViewAnim() {
        AnimationSet set = new AnimationSet(true);
        RotateAnimation rotate = new RotateAnimation(0, 720, RotateAnimation.RELATIVE_TO_SELF, 0.5f, RotateAnimation.RELATIVE_TO_SELF, 0.5f);
        set.addAnimation(rotate);
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 2f
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                if (iv_numAdd != null) {
                    iv_numAdd.setImageResource(R.drawable.icon_add_def);
                }
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        set.addAnimation(translate);
        set.setDuration(500);
        return set;
    }

    /**
     * 控件的水平动画
     */
    private Animation showTranslateAnim() {
        TranslateAnimation translate = new TranslateAnimation(
                TranslateAnimation.RELATIVE_TO_SELF, 1f
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0
                , TranslateAnimation.RELATIVE_TO_SELF, 0);
        translate.setDuration(500);
        return translate;
    }

    private ViewGroup getWindowRootView() {
        if (rootView == null) {
            rootView = (ViewGroup) (BaseYBMApp.getApp().getCurrActivity()).getWindow().getDecorView();
        }
        return rootView;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        new Handler(Looper.myLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                clean();
            }
        }, DIFF_TIME);
    }
}
