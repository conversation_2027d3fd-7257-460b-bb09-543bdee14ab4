package com.ybmmarket20.view.sameSpecifications

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport

abstract class SameSpecificationsAnalysisListAdapter(layoutId: Int, list: List<RowsBean>): YBMBaseAdapter<RowsBean>(layoutId, list) {

    private val mCacheRecord = mutableSetOf<Int>()

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RowsBean?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            if (!mCacheRecord.contains(holder.bindingAdapterPosition)) {
                mCacheRecord.add(holder.bindingAdapterPosition)
                CommodityDetailReport.trackSameGoodsSpecGoodsExposure(mContext, holder.bindingAdapterPosition, bean.id, bean.productName)
            }
        }
    }
}