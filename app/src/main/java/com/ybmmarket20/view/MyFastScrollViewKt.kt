package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.apkfuns.logutils.LogUtils
import com.google.android.material.appbar.AppBarLayout
import com.ybm.app.utils.BugUtil
import com.ybm.app.view.CommonRecyclerView
import com.ybm.app.view.WrapGridLayoutManager
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.utils.UiUtils

/**
 * 一键回到顶端
 */
class MyFastScrollViewKt : RelativeLayout {


    private var mIv: ImageView? = null
    private var mAppbar: AppBarLayout? = null
    private var mContentView: RelativeLayout? = null
    private var mRecyclerView: RecyclerView? = null
    private val SHOW_FLOATINT_SCREEN_HEIGHT = 1.5f
    private var mState = 0
    private var y = 0
    private var screenHeight = 0
    var isShowFloating = false
    var isShowFastScroll = false

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    init {
        screenHeight = UiUtils.getScreenHeight()
        val inflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        mContentView = inflater.inflate(R.layout.view_my_fast_scroll, this, true) as RelativeLayout
        mIv = mContentView?.findViewById<ImageView>(R.id.iv)
        mIv?.setOnClickListener(ItemClick())
    }

    private inner class ItemClick : OnClickListener {
        override fun onClick(v: View) {
            mAppbar?.setExpanded(true, true)
            mRecyclerView?.stopScroll()
            mRecyclerView?.smoothScrollToPosition(0)
        }
    }

    /**
     * @param recyclerView 当前列表view
     * @param y            滚动距离
     * @param dy           判断上下滚动状态
     * @param appbar       全部药品列表顶部toolBar
     */
    fun showFastScroll(recyclerView: RecyclerView, appbar: AppBarLayout?) {
        mRecyclerView = recyclerView
        mAppbar = appbar
        y = recyclerView.computeVerticalScrollOffset()
        if (y > screenHeight * SHOW_FLOATINT_SCREEN_HEIGHT) {
            if (!isShowFastScroll) {
                mIv?.visibility = View.VISIBLE
                isShowFastScroll = true
            }
        } else {
            if (isShowFastScroll) {
                mIv?.visibility = View.INVISIBLE
                isShowFastScroll = false
            }
        }
    }

}