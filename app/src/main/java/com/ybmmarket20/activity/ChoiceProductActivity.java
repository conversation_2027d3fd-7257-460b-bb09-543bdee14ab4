package com.ybmmarket20.activity;

import static com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_REFUND_SELECT_GIFT;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;

import com.github.mzule.activityrouter.annotation.Router;
import com.google.gson.Gson;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.OrderDetail2Adapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderDetailBean;
import com.ybmmarket20.bean.CheckOrderDetailRowsBean;
import com.ybmmarket20.bean.PackageListBean;
import com.ybmmarket20.bean.QueryGiftRefundNum;
import com.ybmmarket20.bean.QueryGiftRefundNumResult;
import com.ybmmarket20.bean.RefundDetaiItemBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.CopyUtilKt;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.view.GiftRefundSelectBottomDialog;
import com.ybmmarket20.viewmodel.ChoiceRefundGoodsViewModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;

/**
 * 选择退货商品
 */
@Router({"choiceproduct/:status/:orderNo/:orderId/:finish", "choiceproduct/:status/:orderNo/:orderId/:finish/:refundMode", "choiceproduct/:status/:orderNo/:orderId/:refundMode", "choiceproduct/:status/:orderNo/:orderId", "choiceproduct/:status/:orderNo"})
public class ChoiceProductActivity extends BaseActivity {

    @Bind(R.id.rv_product)
    RecyclerView rvProduct;
    @Bind(R.id.cb_all)
    CheckBox cbAll;
    @Bind(R.id.tv_num)
    TextView tvNum;
    @Bind(R.id.btn_refund)
    Button btnRefund;
    @Bind(R.id.tv_channel)
    TextView tvChannel;

    @Bind(R.id.cl_refund_freight)
    ConstraintLayout clRefundFreight;

    @Bind(R.id.tv_refund_freight_price)
    TextView tvRefundFreightPrice;

    @Bind(R.id.cb_refund_freight)
    CheckBox cbRefundFreight;

    protected String orderId;
    protected String status;
    protected String orderNo;//订单编号
    protected String finish;
    private String refundMode;
    private List<RefundProductListBean> rowsBeans;
    protected OrderDetail2Adapter adapter;
    private boolean userChoice = true;
    private int normalItemTotal = 0; //普通商品和套餐数量
    private int nearEffectiveItemTotal = 0; //近效期和临期商品和套餐数量
    public CheckOrderDetailBean order;
    private boolean isShowNearEffectiveFlag;//是否显示近效期和临期分组头部 true:显示
    private ChoiceRefundGoodsViewModel mViewModel;

    @Override
    protected void initData() {
        orderId = getIntent().getStringExtra("orderId");
        status = getIntent().getStringExtra("status");
        orderNo = getIntent().getStringExtra("orderNo");
        finish = getIntent().getStringExtra("finish");
        refundMode = getIntent().getStringExtra("refundMode");
        mViewModel = new ViewModelProvider(this).get(ChoiceRefundGoodsViewModel.class);
        if (!TextUtils.isEmpty(finish)) {
            setTitle("选择退货商品");
            btnRefund.setText("确认退货");
        } else {
            setTitle("选择退款商品");
            btnRefund.setText("确认退款");
        }
        if (TextUtils.isEmpty(orderId)) {
            finish();
            ToastUtils.showShort("参数错误");
            return;
        }
        cbAll.setOnCheckedChangeListener((buttonView, isChecked) -> {

            if (canChoice()){
                if (rowsBeans == null || rowsBeans.isEmpty()) {
                    return;
                }
                if (!userChoice) {
                    userChoice = true;
                    return;
                }
                if (isChecked) {
                    for (RefundProductListBean bean : rowsBeans) {
                        if (bean.extraGift == 0) {
                            bean.isCheck = true;
                        }
                    }
                    adapter.setNewData(rowsBeans);

                } else {
                    for (RefundProductListBean bean : rowsBeans) {
                        if (bean.extraGift == 0) {
                            bean.isCheck = false;
                        }
                    }
                    adapter.setNewData(rowsBeans);
                }
                editAllActivitiesMainGoods();
                resetCount();
            } else {
                if (cbAll.isChecked() != cbRefundFreight.isChecked()){  //退运费和全选保持一致就行
                    cbRefundFreight.performClick();
                }
            }
        });
        adapter = new OrderDetail2Adapter(rowsBeans, true, canChoice()) { //状态91时表示已退款 此时只能退运费 其他置灰


            @Override
            protected void bindGoodsGift(YBMBaseHolder holder, RefundProductListBean bean) {
                super.bindGoodsGift(holder, bean);
                TextView count = holder.getView(R.id.tv_goods_gift_count);
                count.setText("X" + bean.curSelectedCount);
                //赠品是否选中
                if (!bean.isGiftSelected && bean.isGiveSkuTypeFullSelected()) {
                    //满选
                    setRecyclerViewItemHeight(holder.itemView, false);
                } else {
                    setRecyclerViewItemHeight(holder.itemView, true);
                }
            }

            @Override
            protected void bindGoodsSelectGift(YBMBaseHolder holder, RefundProductListBean bean) {
                super.bindGoodsSelectGift(holder, bean);
                TextView tvSelectGiftAmount = holder.getView(R.id.tvSelectGiftAmount);
                tvSelectGiftAmount.setText("需退还 " + bean.selectGiftAmount + "盒 赠品，");
                holder.itemView.setOnClickListener(v -> {
                    FullGiftInfo fgInfo = mFullGiftCache.get(bean.extraGiftId);
                    if (fgInfo != null) {
                        try {
                            List<RefundProductListBean> giftGoodsList = CopyUtilKt.deepCopy(fgInfo.giftGoodsList);
                            if (giftGoodsList == null) return;
                            new GiftRefundSelectBottomDialog(ChoiceProductActivity.this, giftGoodsList, fgInfo.giftPromotionId, fgInfo.selectGiftAmount, (dialog, refundProductListBeans, giftPromotionId) -> {
                                handleResultGift(refundProductListBeans);
                                dialog.dismiss();
                                return null;
                            }).show();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                });
            }

            @Override
            protected void bindPackageTitle(YBMBaseHolder baseViewHolder, RefundProductListBean bean) {
                super.bindPackageTitle(baseViewHolder, bean);
                bindCheck(baseViewHolder, bean);
            }

            @Override
            protected void bindItem(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
                super.bindItem(baseViewHolder, bean);
                if (bean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT) {
                    RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) baseViewHolder.itemView.getLayoutParams();
                    lp.topMargin = ConvertUtils.dp2px(10);
                    if (bean.extraGiftVariety == 0) {
                        //该主品无赠品
                        baseViewHolder.itemView.setBackgroundResource(R.drawable.bg_cart_section_content_01);
                    } else {
                        //该主品有赠品
                        FullGiftInfo fgInfo = mFullGiftCache.get(bean.extraGiftId);
                        if (fgInfo != null) {
                            if (fgInfo.getMainGoodsPosition(bean) > 0) {
                                lp.topMargin = 0;
                            }
                        }
                        baseViewHolder.itemView.setBackgroundResource(R.drawable.bg_cart_section_content_bottom_no_corner);
                    }
                    baseViewHolder.itemView.setLayoutParams(lp);
                }
                bindCheck(baseViewHolder, bean);
            }

            @Override
            protected void onBindNumAddOrSub(RefundProductListBean bean, String count) {
                super.onBindNumAddOrSub(bean, count);
//                if (!TextUtils.equals(count, bean.productAmount) && !TextUtils.equals(count, "1")){
//
//                }
                editMainSpecificMainGoods(bean);
            }

            protected void bindCheck(final YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
                final CheckBox checkBox = baseViewHolder.getView(R.id.cb_choice);
                checkBox.setChecked(bean.isCheck);
                baseViewHolder.setOnClickListener(R.id.ll_root, v -> {

                    if (!canChoice()) return; //不能选直接return

                    if (checkBox.isChecked()) {
                        checkBox.setChecked(false);
                        bean.isCheck = false;
                    } else {
                        checkBox.setChecked(true);
                        bean.isCheck = true;
                    }
                    if (bean.subSize > 0) {//同步套餐
                        for (int a = baseViewHolder.getAdapterPosition() + 1; a < baseViewHolder.getAdapterPosition() + bean.subSize + 1; a++) {
                            rowsBeans.get(a).isCheck = bean.isCheck;
                        }
                    }
                    GoodsCount goodsCount = resetCount();
                    if (goodsCount.checkedCount < goodsCount.totalCount && cbAll.isChecked()) {
                        userChoice = false;
                        cbAll.setChecked(false);
                    } else if (goodsCount.checkedCount == goodsCount.totalCount && !cbAll.isChecked()) {
                        userChoice = false;
                        cbAll.setChecked(true);
                    }
                    editMainSpecificMainGoods(bean);
                });
            }
        };
        adapter.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "没有商品");
        rvProduct.setLayoutManager(new WrapLinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        rvProduct.setAdapter(adapter);
        ((SimpleItemAnimator) rvProduct.getItemAnimator()).setSupportsChangeAnimations(false);
        getOrderDetail(orderId);
        handleQueryGiftNum();
    }

    private void setRecyclerViewItemHeight(View itemView, boolean isWrapper) {
        RecyclerView.LayoutParams lp = (RecyclerView.LayoutParams) itemView.getLayoutParams();
        if (isWrapper) {
            lp.height = RecyclerView.LayoutParams.WRAP_CONTENT;
        } else {
            lp.height = 0;
        }
        itemView.setLayoutParams(lp);
    }


    /**
     * 已退款时，商品不能选择  置灰  这种是有运费的情况
     * @return
     */
    public boolean canChoice(){
        return !status.equals("91");
    }

    /**
     * 处理获取赠品数量
     */
    @SuppressLint("NotifyDataSetChanged")
    public void handleQueryGiftNum() {
        mViewModel.getQueryGiftRefundNumLiveData().observe(this, giftResult -> {
            dismissProgress();
            if (giftResult != null && giftResult.data != null && giftResult.isSuccess()) {
                QueryGiftRefundNumResult result = giftResult.data;
                handleResultGift(result.getGiftList());
            }
            adapter.notifyDataSetChanged();
            resetCount();
        });
    }

    /**
     * 修改主品数量或者选中满选主品处理单个活动返回数据
     * @param resultList
     * @param extraGiftId
     */
    @SuppressLint("NotifyDataSetChanged")
    public void handleSingleActivityChange(List<RefundProductListBean> resultList, String extraGiftId) {
        FullGiftInfo fgInfo = mFullGiftCache.get(extraGiftId);
        if (fgInfo == null) return;
        //先移除活动赠品
        Iterator<RefundProductListBean> it = rowsBeans.iterator();
        while (it.hasNext()) {
            RefundProductListBean b = it.next();
            if (b.isGift() && TextUtils.equals(b.extraGiftId, extraGiftId)) {
                it.remove();
            }
        }
        //设置赠品itemType
        for (RefundProductListBean refundProductListBean : resultList) {
            refundProductListBean.setItemType(RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT);
            refundProductListBean.productId = refundProductListBean.id + "";
            if (refundProductListBean.isGiveSkuTypeFullSelected() && !refundProductListBean.isGiftSelected) {
                //满选未选中
                refundProductListBean.isCheck = false;
            } else {
                //满选已选中和满赠
                refundProductListBean.isCheck = true;
            }
        }
        //添加活动赠品
        int insertPosition = 0;
        List<RefundProductListBean> pList = fgInfo.primeGoodsList;
        if (pList != null && !pList.isEmpty()) insertPosition = rowsBeans.lastIndexOf(pList.get(pList.size()-1)) + 1;
        rowsBeans.addAll(insertPosition, resultList);
        mFullGiftCache.clear();
        handleFullGiftGoods(rowsBeans);
        adapter.notifyDataSetChanged();
    }


    /**
     * 修改主品数量或者选中满选主品对返回数据分组并处理
     * @param resultList
     */
    public void handleResultGift(List<RefundProductListBean> resultList) {
        if (resultList == null) return;
        Map<String, List<RefundProductListBean>> groupMap = new HashMap<>();
        for (RefundProductListBean bean : resultList) {
            List<RefundProductListBean> actList;
            if (!groupMap.containsKey(bean.extraGiftId)) {
                actList = new ArrayList<>();
                groupMap.put(bean.extraGiftId, actList);
            }
            actList = groupMap.get(bean.extraGiftId);
            if (actList == null) continue;
            actList.add(bean);
        }
        for(Map.Entry<String, List<RefundProductListBean>> entry : groupMap.entrySet()) {
            handleSingleActivityChange(entry.getValue(), entry.getKey());
        }
        resetCount();
    }

    /**
     * 编辑指定主品
     * @param bean
     */
    public void editMainSpecificMainGoods(RefundProductListBean bean) {
        if (bean.extraGiftVariety > 0) {
            List<Map<String, String>> list = new ArrayList<>();
            FullGiftInfo fgInfo = mFullGiftCache.get(bean.extraGiftId);
            if (fgInfo == null) return;
            try {
                for (RefundProductListBean refundProductListBean : fgInfo.primeGoodsList) {
                    //主品
                    Map<String, String> map = new HashMap<>();
                    map.put("orderDetailId", refundProductListBean.productId);
                    map.put("productAmount", refundProductListBean.numberAmount + "");
                    map.put("isMainSelected", refundProductListBean.isCheck + "");
                    list.add(map);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (bean.isGiveSkuTypeFullSelected()) {
                if (fgInfo.giftGoodsList != null){
                    //满选
                    for (RefundProductListBean refundProductListBean : fgInfo.giftGoodsList) {
                        Map<String, String> map = new HashMap<>();
                        map.put("orderDetailId", refundProductListBean.productId);
                        map.put("productAmount", refundProductListBean.curSelectedCount + "");
                        map.put("isGiftSelected", refundProductListBean.isGiftSelected + "");
                        list.add(map);
                    }
                }
            }
            String refundGoodsParams = new Gson().toJson(list);
            showProgress();
            mViewModel.queryGiftRefundNum(refundGoodsParams, orderNo);
        }
    }

    /**
     * 编辑所有活动主品
     */
    public void editAllActivitiesMainGoods() {
        List<Map<String, String>> list = new ArrayList<>();
        for(String extraGiftId : mFullGiftCache.keySet()) {
            FullGiftInfo fgInfo = mFullGiftCache.get(extraGiftId);
            if (fgInfo == null) continue;
            try {
                for (RefundProductListBean refundProductListBean : fgInfo.primeGoodsList) {
                    Map<String, String> map = new HashMap<>();
                    map.put("orderDetailId", refundProductListBean.productId);
                    map.put("productAmount", refundProductListBean.numberAmount + "");
                    map.put("isMainSelected", refundProductListBean.isCheck + "");
                    list.add(map);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (fgInfo.isCanSelectGift) {
                //满选
                for (RefundProductListBean refundProductListBean : fgInfo.getGiftGoodsList()) {
                    Map<String, String> map = new HashMap<>();
                    map.put("orderDetailId", refundProductListBean.productId);
                    map.put("productAmount", refundProductListBean.numberAmount + "");
                    list.add(map);
                }
            }
        }
        if (list.isEmpty()) return;
        String refundGoodsParams = new Gson().toJson(list);
        showProgress();
        mViewModel.queryGiftRefundNum(refundGoodsParams, orderNo);
    }

//    public void editGiftList(String giftPromotionId, int giveSkuType) {
//        FullGiftInfo fgInfo = mFullGiftCache.get(giftPromotionId);
//        if (fgInfo == null) return;
//        rowsBeans.
//    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_choice_product;
    }

    @OnClick({R.id.btn_refund})
    public void onClick(View view) {
        if (view.getId() == R.id.btn_refund) {
            GoodsCount goodsCount = getGoodsCount();
            if ((canChoice() && goodsCount.checkedCount <= 0) || (!canChoice() && !cbRefundFreight.isChecked())) {
                ToastUtils.showShort("请选择退款商品");
                return;
            }
            toRefund();
        }
    }

    /**
     *  检测是否显示“不返还优惠券”提示语
     * @return
     */
    private boolean checkIfShowRefundDialog() {
        boolean canShow = false;
        for (RefundProductListBean bean : rowsBeans) {
            if ((bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_TITLE
                    || bean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT)) {
                if (!bean.isCheck) {
                    canShow = true;
                } else {
                    int canRefundNum = Integer.parseInt(bean.productAmount);
                    if (bean.numberAmount < canRefundNum) {
                        canShow = true;
                    }
                }
            }
        }
        return canShow;
    }

    //查看订单详情
    private void toRefund() {
        GoodsCount goodsCount = getGoodsCount();
        ArrayList<RefundDetaiItemBean> list = new ArrayList<>(goodsCount.checkedCount);
        RefundDetaiItemBean bean4;
        for (RefundProductListBean bean : rowsBeans) {
            if (bean.isCheck && (bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_TITLE
                    || bean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT
                    || (bean.isGiveSkuTypeFullSelected() && bean.isGiftSelected))) {
                bean4 = new RefundDetaiItemBean();
                if (bean.isGiveSkuTypeFullSelected() && bean.isGiftSelected) {
                    //满选
                    bean4.productAmount = bean.curSelectedCount + "";
                } else if (bean.isGift()) {
                    //满赠
                    bean4.productAmount = bean.curSelectedCount + "";
                } else {
                    bean4.productAmount = bean.numberAmount + "";
                }
                if (bean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_TITLE) {
                    bean4.packageId = bean.productId;
                } else {
                    bean4.productId = bean.productId;
                }
                bean4.productPrice = bean.productPrice + "";
                list.add(bean4);
            }
        }

        boolean hasRefundFreight = order.canFreightRefund() && cbRefundFreight.isChecked();

        String param = new Gson().toJson(list);
        Intent intent = new Intent(getMySelf(), ApplyRefundActivity.class);
        intent.putExtra("type", "1");
        intent.putExtra("orderId", orderId);
        intent.putExtra("status", status);
        intent.putExtra("orderNo", orderNo);
        intent.putExtra("param", param);
        intent.putExtra("payType", order.payType + "");
        intent.putExtra("contactor", order.contactor + "");
        intent.putExtra("mobile", order.mobile + "");
        intent.putExtra("refundMode", refundMode);
        intent.putExtra("billType", order.billType);
        intent.putExtra("hasRefundFreight", hasRefundFreight); //是否选中退运费
        startActivity(intent);
        finish();
    }

    //查看订单详情
    private void getOrderDetail(String id) {
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("id", id);
        params.put("sceneType", "2");
        HttpManager.getInstance().post(AppNetConfig.ORDER_DETAIL, params, new BaseResponse<CheckOrderDetailBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderDetailBean> bean, CheckOrderDetailBean order) {
                if (bean != null && bean.isSuccess()) {
                    if (order != null) {
                        ChoiceProductActivity.this.order = order;
                        setData(order);
                    } else {
                        showDialog();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {

            }
        });
    }

    /**
     * 设置列表数据
     *
     * @param bean
     */
    public void setData(CheckOrderDetailBean bean) {
        if (bean == null) return;
        updateShowNearEffectiveFlag(false);
        rowsBeans = new ArrayList<>();
        int insertNearEffectiveHeaderPosition = 0;
        //处理非近效期或非临期商品数据
        handleGoodsData(rowsBeans, bean, false);
        //处理非近效期或非临期套餐数据
        handlePackageData(rowsBeans, bean, false);
        //插入临期头的位置
        insertNearEffectiveHeaderPosition = rowsBeans.size();
        //处理近效期或临期商品数据
        handleGoodsData(rowsBeans, bean, true);
        //处理近效期或临期套餐数据
        handlePackageData(rowsBeans, bean, true);
        //添加近效期或临期分组头
        addNearEffectiveHeader(rowsBeans, insertNearEffectiveHeaderPosition);
        //处理满赠商品
        handleFullGiftGoods(rowsBeans);
        adapter.setNewData(rowsBeans);
        if (rowsBeans.isEmpty() && canChoice()) showDialog();
        resetCount();
        tvChannel.setVisibility((TextUtils.isEmpty(bean.getChannelCode()) || "1".equals(bean.getChannelCode()) || bean.isIsThirdCompany()) && !bean.isAuditStatus() ? View.GONE : View.VISIBLE);
        String tvChannelStr = "";
        if (bean.isAuditStatus()) {
            tvChannelStr = "温馨提示：当前订单审核中，如您提交部分退货可能会影响您剩余商品的发货时效。如有疑问请您咨询客服电话400-0505-111";
        }
        if (!(TextUtils.isEmpty(bean.getChannelCode()) || "1".equals(bean.getChannelCode()) || bean.isIsThirdCompany())) {
            tvChannelStr = "温馨提示：您申请退货的品种为临床渠道控销品种，如对当前商品可退数量存在疑问，可咨询400-0507-788";
        }
        tvChannel.setText(tvChannelStr);

        //是否显示退运费
        if (bean.canFreightRefund()){
            clRefundFreight.setVisibility(View.VISIBLE);
            tvRefundFreightPrice.setText(bean.refundFreightAmount+"元");
        }else {
            clRefundFreight.setVisibility(View.GONE);
        }

    }

    /**
     * 处理套餐数据
     *
     * @param data
     * @param bean
     * @param needNearEffective 处理的套餐类型是否是近效期或临期
     */
    private void handlePackageData(List<RefundProductListBean> data, CheckOrderDetailBean bean, boolean needNearEffective) {
        if (bean.getPackageList() == null) return;
        List<PackageListBean> packageList = bean.getPackageList();
        //遍历处理套餐头
        for (PackageListBean packageBean : packageList) {
            //是否是当前要筛选的数据
            if (needNearEffective != isNearEffectiveGoodsOrPackage(packageBean.nearEffectiveFlag))
                continue;
            RefundProductListBean productListBean = new RefundProductListBean();
            productListBean.setItemType(needNearEffective ? RefundProductListBean.ITEMTYPE_PACKAGE_TITLE_NEAR_EFFECTIVE : RefundProductListBean.ITEMTYPE_PACKAGE_TITLE);
            productListBean.productId = packageBean.id;
            productListBean.productName = packageBean.title;
            productListBean.productAmount = packageBean.packageCount + "";
            productListBean.subtotal = packageBean.subtotalPrice;
            productListBean.productPrice = packageBean.price;
            productListBean.mediumPackageNum = 1;
            productListBean.numberAmount = packageBean.packageCount;
            productListBean.isSplit = 1;
            productListBean.subSize = packageBean.orderDetailList == null ? 0 : packageBean.orderDetailList.size();
            productListBean.subtotalPrice = packageBean.subtotalPrice;
            productListBean.totalPrice = packageBean.totalPrice + "";
            data.add(productListBean);
            if (!needNearEffective) normalItemTotal++;
            else nearEffectiveItemTotal++;
            //标记存在近效期和临期套餐
            if (needNearEffective) updateShowNearEffectiveFlag(true);
            //遍历处理套餐商品
            List<CheckOrderDetailRowsBean> orderDetailList = packageBean.orderDetailList;
            for (CheckOrderDetailRowsBean checkOrderDetailRowsBean : orderDetailList) {
                addProduct(data, checkOrderDetailRowsBean, needNearEffective ? RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT_NEAR_EFFECTIVE : RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT);
            }
        }
    }

    /**
     * 处理商品数据
     *
     * @param data
     * @param bean
     * @param needNearEffective 处理的商品类型是否是近效期或临期
     */
    private void handleGoodsData(List<RefundProductListBean> data, CheckOrderDetailBean bean, boolean needNearEffective) {
        if (bean.getDetailList() == null) return;
        List<CheckOrderDetailRowsBean> detailList = bean.getDetailList();
        for (CheckOrderDetailRowsBean checkOrderDetailRowsBean : detailList) {
            if (needNearEffective != isNearEffectiveGoodsOrPackage(checkOrderDetailRowsBean.nearEffectiveFlag))
                continue;
            if (checkOrderDetailRowsBean.extraGift == 1 && !needNearEffective) {
                //赠品
                addProduct(data, checkOrderDetailRowsBean, RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT);
            } else {
                addProduct(data, checkOrderDetailRowsBean, needNearEffective ? RefundProductListBean.ITEMTYPE_REFUND_CONTENT_NEAR_EFFECTIVE : RefundProductListBean.ITEMTYPE_REFUND_CONTENT);
            }
            //标记存在近效期临期商品
            if (needNearEffective) updateShowNearEffectiveFlag(true);
            if (!needNearEffective) {
                if (checkOrderDetailRowsBean.extraGift == 0) {
                    //主品
                    normalItemTotal++;
                    normalItemTotal += checkOrderDetailRowsBean.extraGiftVariety;
                }
            }
            else nearEffectiveItemTotal++;
        }

    }

    /**
     * 添加临期分组头
     */
    private void addNearEffectiveHeader(List<RefundProductListBean> data, int insertPosition) {
        if (!isShowNearEffectiveFlag) return;
        RefundProductListBean productListBean = new RefundProductListBean();
        productListBean.setItemType(RefundProductListBean.ITEMTYPE_GROUP_TITLE_NEAR_EFFECTIVE);
        productListBean.nearEffectiveCount = nearEffectiveItemTotal;
        data.add(insertPosition, productListBean);
    }

    /**
     * 更新是否显示近效期和临期分组头状态
     *
     * @param flag
     */
    private void updateShowNearEffectiveFlag(boolean flag) {
        if (flag != isShowNearEffectiveFlag) isShowNearEffectiveFlag = flag;
    }

    /**
     * 判断是否是临期的商品或者套餐
     *
     * @return true 临期或近效期
     */
    private boolean isNearEffectiveGoodsOrPackage(int nearEffectiveFlag) {
        return nearEffectiveFlag != 0;
    }

    private RefundProductListBean generateGoodsBean(CheckOrderDetailRowsBean listBean, int itemType) {
        if (listBean == null) {
            return null;
        }
        RefundProductListBean productListBean = new RefundProductListBean();
        productListBean.setItemType(itemType);
        productListBean.imageUrl = listBean.imageUrl;
        //这是id 不是productId 不能用跳转到商品详情
        productListBean.productId = listBean.id + "";
        productListBean.productName = listBean.productName;
        productListBean.productPrice = listBean.productPrice;
        productListBean.productAmount = listBean.productAmount + "";
        productListBean.spec = listBean.spec;
        productListBean.manufacturer = listBean.manufacturer;
        productListBean.subtotal = StringUtil.DecimalFormat2Double(listBean.productPrice * listBean.productAmount);
        productListBean.blackProductText = listBean.blackProductText;
        productListBean.mediumPackageNum = listBean.mediumPackageNum;
        productListBean.numberAmount = listBean.productAmount;
        productListBean.isSplit = listBean.isSplit;
        productListBean.nearEffectiveFlag = listBean.nearEffectiveFlag;
        productListBean.extraGift = listBean.extraGift;
        productListBean.extraGiftVariety = listBean.extraGiftVariety;
        productListBean.extraGiftId = listBean.extraGiftId;
        productListBean.afterSaleTags = listBean.afterSaleTags;
        productListBean.giveSkuType = listBean.giveSkuType;
        productListBean.curSelectedCount = listBean.curSelectedCount;
        productListBean.isGiftSelected = listBean.isGiftSelected;
        productListBean.id = listBean.id;
        productListBean.nearEffect = listBean.nearEffect;
        productListBean.orderDetailId = listBean.id + "";
        //是赠品
        if (productListBean.isGift()) {
            try {
                productListBean.curSelectedCount = Integer.parseInt(productListBean.productAmount);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return productListBean;
    }

    /**
     * 添加套餐商品和普通商品
     *
     * @param data
     * @param listBean
     * @param itemType
     */
    private void addProduct(List<RefundProductListBean> data, CheckOrderDetailRowsBean listBean, int itemType) {
        RefundProductListBean productListBean = generateGoodsBean(listBean, itemType);
        data.add(productListBean);
    }

    private void showDialog() {
        AlertDialogEx dialogEx = new AlertDialogEx(getMySelf());
        dialogEx.setMessage("无可退商品").setCancelButton("我知道了", new ViewOnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                finish();
            }
        }).show();
    }

    private GoodsCount resetCount() {
        GoodsCount goodsCount = getGoodsCount();
        if (canChoice()){
            tvNum.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_choice_product), goodsCount.checkedCount + "", goodsCount.totalCount + "")));
        }else {
            tvNum.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_choice_product), "0", "0")));
        }
        return goodsCount;
    }

    /**
     * 获取选中数量和总数量
     * @return
     */
    private GoodsCount getGoodsCount() {
        GoodsCount count = new GoodsCount();
        Map<String, Boolean> map = new HashMap<>();
        if (rowsBeans == null) return count;
        for (RefundProductListBean rowsBean : rowsBeans) {
            if (isNearEffectiveGoodsOrPackage(rowsBean.nearEffectiveFlag)) continue;
            if (rowsBean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT
                && rowsBean.extraGiftId != null) {
                map.put(rowsBean.extraGiftId, rowsBean.isCheck);
            }
        }
        for (RefundProductListBean rowsBean : rowsBeans) {
            if (isNearEffectiveGoodsOrPackage(rowsBean.nearEffectiveFlag)) continue;
            if (rowsBean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT
                || rowsBean.getItemType() == RefundProductListBean.ITEMTYPE_PACKAGE_TITLE) {
                if(rowsBean.isCheck) count.checkedCount ++;
                count.totalCount ++;
            } else if (rowsBean.getItemType() == RefundProductListBean.ITEMTYPE_REFUND_CONTENT_GIFT) {
                if (map.get(rowsBean.extraGiftId) != null && map.get(rowsBean.extraGiftId)) {
                    if (rowsBean.isGiveSkuTypeFullSelected() && rowsBean.isGiftSelected) {
                        count.checkedCount ++;
                    } else if(!rowsBean.isGiveSkuTypeFullSelected() && rowsBean.isCheck) count.checkedCount ++;
                }
                if (rowsBean.isGiveSkuTypeFullSelected()) {
                    if (rowsBean.isGiftSelected) {
                        count.totalCount ++;
                    }
                } else {
                    count.totalCount ++;
                }
            }
        }
        return count;
    }

    private class GoodsCount {
        public int checkedCount;
        public int totalCount;
    }

    private final Map<String, FullGiftInfo> mFullGiftCache = new HashMap<>();

    /**
     * 处理满赠品
     * @param rowsBeans
     */
    private void handleFullGiftGoods(List<RefundProductListBean> rowsBeans) {
        Iterator<RefundProductListBean> iterator = rowsBeans.iterator();
        //删除选择赠品按钮
        while (iterator.hasNext()) {
            RefundProductListBean bean = iterator.next();
            if (bean.getItemType() == ITEMTYPE_REFUND_SELECT_GIFT) {
                iterator.remove();
            }
        }
        for (int i = 0; i < rowsBeans.size(); i++) {
            RefundProductListBean rowsBean = rowsBeans.get(i);
            if (isNearEffectiveGoodsOrPackage(rowsBean.nearEffectiveFlag)) continue;
            if (rowsBean.extraGiftId != null) {
                boolean isContainsKey = mFullGiftCache.containsKey(rowsBean.extraGiftId);
                if (isContainsKey) {
                    FullGiftInfo giftInfo = mFullGiftCache.get(rowsBean.extraGiftId);
                    if (giftInfo != null) giftInfo.addFullGiftGoodsInfo(rowsBeans.get(i), i);
                } else {
                    FullGiftInfo fgInfo = new FullGiftInfo();
                    fgInfo.giftPromotionId = rowsBean.extraGiftId;
                    fgInfo.addFullGiftGoodsInfo(rowsBeans.get(i), i);
                    mFullGiftCache.put(rowsBean.extraGiftId, fgInfo);
                }
            }
        }
        //添加可选赠品按钮
        for(String key : mFullGiftCache.keySet()) {
            FullGiftInfo fgInfo = mFullGiftCache.get(key);
            if (fgInfo != null && fgInfo.isCanSelectGift) {
                if (fgInfo.selectGiftAmount == 0) continue;
                RefundProductListBean selectGiftBean = new RefundProductListBean();
                selectGiftBean.setItemType(ITEMTYPE_REFUND_SELECT_GIFT);
                selectGiftBean.extraGiftId = fgInfo.giftPromotionId;
                selectGiftBean.selectGiftAmount = fgInfo.selectGiftAmount;
                int insertPosition = 0;
                List<RefundProductListBean> pList = fgInfo.primeGoodsList;
                if (pList != null && !pList.isEmpty()) insertPosition = rowsBeans.lastIndexOf(pList.get(pList.size()-1)) + 1;
                rowsBeans.add(insertPosition, selectGiftBean);
            }
        }
    }

    /**
     * 满赠品信息
     */
    private class FullGiftInfo {
        public String giftPromotionId;
        public List<RefundProductListBean> primeGoodsList; //主品列表
        public List<RefundProductListBean> giftGoodsList; // 赠品列表
        public Map<String, RefundProductListBean> giftMap;//赠品列表
        public RefundProductListBean selectGift; //选品按钮
        public int giftStartPosition;
        public int giftEndPosition;
        public boolean isCanSelectGift; //是否可选赠品
        public int selectGiftAmount;//需要选择的赠品数量
        public List<RefundProductListBean> getGiftGoodsList() {
            if (giftGoodsList == null) {
                giftGoodsList = new ArrayList<>();
            }
            return giftGoodsList;
        }
        /**
         * 添加慢赠品，主品或赠品
         * @param bean
         */
        public void addFullGiftGoodsInfo(RefundProductListBean bean, int position) {
            if (bean.extraGift == 1) {
                //赠品
                if (giftGoodsList == null) {
                    giftGoodsList = new ArrayList<>();
                    giftStartPosition = position;
                }
                if (giftMap == null) giftMap = new HashMap<>();
                giftGoodsList.add(bean);
                giftMap.put(bean.orderDetailId, bean);
                if (bean.isGiftSelected) {
                    selectGiftAmount += bean.curSelectedCount;
                }
                if (position > giftEndPosition) {
                    if (isCanSelectGift) {
                        giftEndPosition = position + 1;
                    } else {
                        giftEndPosition = position;
                    }
                }
            } else {
                //主品
                if (bean.isGiveSkuTypeFullSelected()) {
                    isCanSelectGift = bean.isGiveSkuTypeFullSelected();
                }
                if (primeGoodsList == null) primeGoodsList = new ArrayList<>();
                primeGoodsList.add(bean);
                //防止无赠品
                giftStartPosition = position + 1;
            }
        }

        public int getMainGoodsPosition(RefundProductListBean bean) {
            if (bean == null) return -1;
            return primeGoodsList.indexOf(bean);
        }
    }
}
