package com.ybmmarket20.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.utils.JsonUtils;
import com.ybmmarket20.business.order.ui.OrderListFragment;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.RecyclerViewSwipe.RecyclerTouchListener;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.SearchOrderTipAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CheckOrderRowsBean;
import com.ybmmarket20.bean.CheckOrderSearchListBean;
import com.ybmmarket20.bean.CheckOrderSearchRowsBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.OrderActionBean;
import com.ybmmarket20.bean.OrderSearchTipEntry;
import com.ybmmarket20.bean.OrderSearchTipItem;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.OnResult;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.OrderActionLayout;
import com.ybmmarket20.view.OrderItemAptitudeView;
import com.ybmmarket20.viewmodel.OrderSearchTipViewModel;
import com.ybmmarketkotlin.utils.TextViewKt;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * 搜索订单
 */
@Router("searchorder")
public class SearchOrderActivity extends SearchOrderAnalysisActivity implements OnResult {

    @Bind(R.id.search_order_left)
    ImageView searchOrderLeft;
    @Bind(R.id.search_order_iv1)
    ImageView searchOrderIv1;
    @Bind(R.id.search_title_et)
    EditText searchTitleEt;
    @Bind(R.id.search_title_right)
    Button searchTitleRight;
    @Bind(R.id.search_list_view)
    CommonRecyclerView checkOrderLv;
    @Bind(R.id.rv_tip)
    RecyclerView rvTip;
    private String name;
    private List<CheckOrderSearchRowsBean> rows = new ArrayList<>();
//    private YBMBaseAdapter orderAdapter;
    private YBMBaseMultiItemAdapter<CheckOrderSearchRowsBean> orderAdapter;
    private int bottom = ConvertUtils.dp2px(4);
    protected SimpleDateFormat dateFormat;
    protected int page = 1;
    protected RecyclerTouchListener onTouchListener;
    private boolean isFrist = true;
    private int currPosition;
    private OrderSearchTipViewModel orderSearchTipViewModel;
    private OrderSearchTipItem mOrderSearchTipItem;
    private HashMap<String,String> intentParams;
    private String mParamSearchAfter;   // 后台分页回传字段，提升搜索性能

    @Override
    protected void initData() {
        super.initData();
        checkOrderLv.setShowAutoRefresh(false);
        searchTitleEt.setHint("搜索商品/发票/订单编号");
        orderSearchTipViewModel = new ViewModelProvider(this).get(OrderSearchTipViewModel.class);
        searchTitleEt.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH || actionId == EditorInfo.IME_ACTION_SEND) {
                    mOrderSearchTipItem = null;
                    getSearchData(0);
                    return true;
                }
                return false;
            }
        });

        searchTitleEt.setOnClickListener(v -> {
            if (rvTip.getVisibility() == View.GONE) {
                rvTip.setVisibility(View.VISIBLE);
                searchTitleRight.setVisibility(View.VISIBLE);
            }
        });

        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        orderAdapter = new YBMBaseMultiItemAdapter<CheckOrderSearchRowsBean>(rows) {
            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, CheckOrderSearchRowsBean bean) {

                String status1 = "审核中";
                String status7 = "出库中";
                String status2 = "配送中";
                String status3 = "已完成";
                String status4 = "已取消";
                String status6 = "已拆单";
                String status10 = "待支付";
                String status21 = "已拒签";
                String status20 = "已送达";
                String status90 = "退款审核中";
                String status91 = "已退款";

                String format = dateFormat.format(new Date(bean.createTime));
                TextView status = baseViewHolder.getView(R.id.tv_order_status);
                OrderItemAptitudeView aptitudeView = baseViewHolder.getView(R.id.aptitudeView);
                status.setVisibility(View.VISIBLE);
                switch (bean.status) {
                    case 1:
                        status.setText(status1);
                        break;
                    case 7://action 再次购买，申请退款（只有详情有）
                        status.setText(status7);
                        break;
                    case 2://要有是否收到货的选择对话框
                        status.setText(status2);
                        break;
                    case 3://申请退货(详情有),确认收货，再次购买
                        status.setText(status3);
                        break;
                    case 4://再次购买
                        status.setText(status4);
                        break;
                    case 6:
                        status.setText(status6);
                        break;
                    case 10:// 取消订单，去支付
                        /*iv.setImageDrawable(getResources().getDrawable(R.drawable.order_status_10));
                        status.setText(status10);*/
//                        Log.e("xyd", "posion = " + baseViewHolder.getBindingAdapterPosition() + "item hashcode = " + baseViewHolder.hashCode() + "; textview hashcode = " + tvUnpayTimeCountdown.hashCode());
//                    if (bean.showExpireReminder && bean.countDownNewTime > 0) {
//                        status.setVisibility(View.GONE);
//                        llUnpayLayout.setVisibility(View.VISIBLE);
//                        long currentRealTime = SystemClock.elapsedRealtime();
//                        long realCountdownTime = bean.countDownNewTime * 1000 - currentRealTime + bean.localtime;
//                        TextViewKt.addCountDown(tvUnpayTimeCountdown, realCountdownTime, null, null, () -> {
//                            getMyOrder(0);
//                            return null;
//                        });
//                    } else {
//                        llUnpayLayout.setVisibility(View.GONE);
//                        status.setVisibility(View.VISIBLE);
//                        status.setText(status10);
//                    }
                        status.setText(status10);
                        break;
                    case 21://再次购买，申请退款（只有详情有）
                        status.setText(status21);
                        break;
                    case 20://再次购买，确认收货，申请退货（部分，只有详情有）
                        status.setText(status20);
                        break;
                    case 90:
                        status.setText(status90);
                        break;
                    case 91:
                        status.setText(status91);
                        break;
                }

                baseViewHolder.setText(R.id.tv_order_number, "共"+bean.varietyNum + "种");
                SpannableStringBuilder showPriceStr = new SpannableStringBuilder();
                showPriceStr.append("¥" + UiUtils.transform(bean.money));
                showPriceStr.setSpan(new AbsoluteSizeSpan(16, true), 1, showPriceStr.length() - 2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                showPriceStr.setSpan(new AbsoluteSizeSpan(14, true), showPriceStr.length() - 2, showPriceStr.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                baseViewHolder.setText(R.id.tv_price, showPriceStr);
                Group groupOne = baseViewHolder.getView(R.id.group_one);
                RecyclerView rv = baseViewHolder.getView(R.id.rv_goods);
                rv.setAdapter(null);
                if (bean.orderImages != null && bean.orderImages.size()>0){
                    if (bean.orderImages.size() > 1){
                        groupOne.setVisibility(View.INVISIBLE);
                        rv.setVisibility(View.VISIBLE);
                        ArrayList<CheckOrderRowsBean.OrderImages> mDataList = new ArrayList<>();
                        if (bean.orderImages != null){
                            if (bean.orderImages.size() > 3){
                                mDataList = (ArrayList<CheckOrderRowsBean.OrderImages>) bean.orderImages.subList(0,3);
                            } else{
                                mDataList = bean.orderImages;
                            }
                        }
                        rv.setAdapter(new OrderListFragment.OrderProductAdapter(mDataList));
                        rv.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));

                    }else {
                        CheckOrderRowsBean.OrderImages data = bean.orderImages.get(0);
                        groupOne.setVisibility(View.VISIBLE);
                        rv.setVisibility(View.GONE);
                        baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + data.getImageUrl(), R.drawable.jiazaitu_min);
                        baseViewHolder.setText(R.id.tv_title,data.getProductName());
                        baseViewHolder.setText(R.id.tv_specifications, data.getSpec());
                        baseViewHolder.setText(R.id.tv_goods_num, "x"+data.getProductAmount());
                    }
                }else {
                    groupOne.setVisibility(View.INVISIBLE);
                    rv.setVisibility(View.GONE);
                }



                //第三方药店
                TextView orderName = baseViewHolder.getView(R.id.tv_shop_title);
                if (!TextUtils.isEmpty(bean.origName)) {
                    orderName.setText(bean.origName);
                }

                orderName.setCompoundDrawablePadding(UiUtils.dp2px(4));
                OrderActionLayout layout = baseViewHolder.getView(R.id.ral_btn);
                // 全部、配送中、他人代付、待支付项显示
                layout.bindData(bean, baseViewHolder.getLayoutPosition(), true);
                //设置卖家备注
                ConstraintLayout clRemark = baseViewHolder.getView(R.id.cl_remark);
                if (!TextUtils.isEmpty(bean.sellerRemark)) {
                    TextView tvRemarkDes = baseViewHolder.getView(R.id.tv_remark_des);
                    tvRemarkDes.setText(bean.sellerRemark);
                    clRemark.setVisibility(View.VISIBLE);
                } else {
                    clRemark.setVisibility(View.GONE);
                }

                aptitudeView.setData(bean);
                aptitudeView.setMerchantExceptionCheckCallback(s -> {
                    handleSystemAndMerchantException(mContext, s, bean.orderNo);
                    return null;
                });
                aptitudeView.setSystemExceptionCheckCallback(s -> {
                    handleSystemAndMerchantException(mContext, s, bean.orderNo);
                    return null;
                });
                aptitudeView.setVisibility(bean.hasOrderExceptionFlag? View.VISIBLE: View.GONE);
                aptitudeView.setFromPage("order_list");
            }
        };
        orderAdapter.addItemType(0, R.layout.order_list_item_new);
        orderAdapter.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "您还没有相关订单");
        checkOrderLv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getSearchData(0);
            }

            @Override
            public void onLoadMore() {
                getSearchData(page);
            }
        });
        checkOrderLv.setEnabled(false);
        checkOrderLv.setAdapter(orderAdapter);
        orderAdapter.openLoadMore(10, true);
        checkOrderLv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });

        //增加滑动删除功能
        onTouchListener = new RecyclerTouchListener(this, checkOrderLv.getRecyclerView());
        onTouchListener
                .setSwipeOptionViews(R.id.bg)//设置滑出的菜单
                .setIndependentViews(R.id.bt_1, R.id.bt_2, R.id.bt_3, R.id.bt_4, R.id.bt_5, R.id.bt_6, R.id.tv_refund,R.id.tv_more,R.id.tv_title, R.id.btn_check_receive_money_account, R.id.tvSystemCheck, R.id.tvMerchantCheck, R.id.rllUpdate, R.id.aptitudeView)
                .setSwipeable(R.id.fg, R.id.bg, new RecyclerTouchListener.OnSwipeOptionsClickListener() {
                    @Override
                    public void onSwipeOptionClicked(int viewID, int position) {
                        if (viewID == R.id.bg) {
                            showDialogDeleteOrder(position);
                        }
                    }
                }).setItemClickable(new RecyclerTouchListener.OnRowClickListener() {//条目点击事件

            @Override
            public void onRowClicked(int position) {
                if (position < rows.size()) {
                    currPosition = position;
                    Intent intent = new Intent(SearchOrderActivity.this, OrderDetailActivity.class);
                    intent.putExtra(IntentCanst.ORDER_ID, rows.get(position).id + "");
                    startActivity(intent);
                }
            }

            @Override
            public void onIndependentViewClicked(int independentViewID, final int position) {//设置条目内点击事件
//
            }
        });
        checkOrderLv.getRecyclerView().addOnItemTouchListener(onTouchListener);
        String timeFieldName = getIntent().getStringExtra(IntentCanst.ORDER_SEARCH_TIME_FIELD);
        // 时间field不为空，说明筛选弹框进来，获取其他信息
        if(!TextUtils.isEmpty(timeFieldName)){
            String quickFieldName = getIntent().getStringExtra(IntentCanst.ORDER_SEARCH_QUICK_FIELD);
            int timeId = getIntent().getIntExtra(IntentCanst.ORDER_SEARCH_TIME_ID,0);
            int quickId = getIntent().getIntExtra(IntentCanst.ORDER_SEARCH_QUICK_ID,0);
            String startTime = getIntent().getStringExtra(IntentCanst.ORDER_SEARCH_START_TIME);
            String endTime = getIntent().getStringExtra(IntentCanst.ORDER_SEARCH_END_TIME);
            intentParams = new HashMap<>();
            intentParams.put(timeFieldName,String.valueOf(timeId));
            intentParams.put(quickFieldName,String.valueOf(quickId));
            intentParams.put("startTimeStr",startTime);
            intentParams.put("endTimeStr",endTime);
            getSearchData(0);
        }
        initOrderSearchTip();
    }

    private void handleSystemAndMerchantException(Context context, String content, String orderNo) {
        new AlertDialogEx(context)
                .setMessage(content)
                .setMessageGravity(Gravity.START)
                .setTitle("资质异常提醒")
                .setCorner()
                .setCancelButton("稍后更新", "#9494A5", (dialog, button) -> {})
                .setConfirmButton("去更新", "#00B377", (dialog, button) -> {
                    RoutersUtils.open("ybmpage://aptitude");
                    HashMap<String, String> trackParams = new HashMap<>();
                    trackParams.put("order_no", orderNo);
                    trackParams.put("page_source", "reminder_popup");
                    XyyIoUtil.track("Update_Qualification", trackParams);
                })
                .show();
    }

    /**
     * 初始化订单搜索启动页
     */
    private void initOrderSearchTip() {
        rvTip.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        orderSearchTipViewModel.getOrderSearchTipLiveData().observe(this, new Observer<BaseBean<List<OrderSearchTipEntry>>>() {
            @Override
            public void onChanged(BaseBean<List<OrderSearchTipEntry>> orderSearchTipBeanBaseBean) {
                dismissProgress();
                if (orderSearchTipBeanBaseBean.isSuccess()) {
                    SearchOrderTipAdapter adapter = new SearchOrderTipAdapter(orderSearchTipBeanBaseBean.data);
                    rvTip.setAdapter(adapter);
                    adapter.setOnItemClickListener(new Function1<OrderSearchTipItem, Unit>() {
                        @Override
                        public Unit invoke(OrderSearchTipItem orderSearchTipItem) {
                            mOrderSearchTipItem = orderSearchTipItem;
                            getSearchData(0);
                            return null;
                        }
                    });
                }
            }
        });
        orderSearchTipViewModel.getOrderSearchTip();
    }

    @OnClick({R.id.search_order_left, R.id.search_title_right})
    public void clickTab(View view) {

        switch (view.getId()) {
            case R.id.search_order_left:
                finish();
                break;
            case R.id.search_title_right:
                mOrderSearchTipItem = null;
                getSearchData(0);
                XyyIoUtil.track("page_OrderList_Search");
                break;
        }
    }

    public void getSearchData(final int page) {
        getSearchData(page, 10);
    }

    public void getSearchData(final int page, final int pageSize) {
        rvTip.setVisibility(View.GONE);
        searchTitleRight.setVisibility(View.GONE);
        name = searchTitleEt.getText().toString().trim();
        // 搜索中间页请求，需要输入框判空
        if(intentParams == null){
            if (TextUtils.isEmpty(name) && mOrderSearchTipItem == null) {
                ToastUtils.showShort("搜索内容不能为空");
                checkOrderLv.setRefreshing(false);
                return;
            }
        }
        //隐藏软键盘
        hideSoftInput();
        if (page == 0) {
            checkOrderLv.setRefreshing(true);
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("name", name);
        params.put("limit", String.valueOf(10));
        params.put("offset", String.valueOf(page));
        if (mOrderSearchTipItem != null) {
            params.put(mOrderSearchTipItem.getField(), mOrderSearchTipItem.getParam());
        }
        if(page != 0){
            params.put("searchAfter",mParamSearchAfter);
        }
        if(intentParams != null && !intentParams.isEmpty()){
            params.put("quickSearch", JsonUtils.toJson(intentParams));
        }
        HttpManager.getInstance().post(AppNetConfig.ORDER, params, new BaseResponse<CheckOrderSearchListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<CheckOrderSearchListBean> obj, CheckOrderSearchListBean data) {
                checkOrderLv.setRefreshing(false);
                if (null != obj) {
                    if (obj.isSuccess()) {
                        List<CheckOrderSearchRowsBean> orderList = null;
                        if (data == null || data.getRows() == null) {
                            orderList = new ArrayList<>();
                        } else {
                            orderList = data.getRows();
                        }
                        if(data != null){
                            mParamSearchAfter = data.getSearchAfter();
                        }
                        if (page < 1) {//下拉刷新
                            SearchOrderActivity.this.page = 1;
                            if (rows == null) {
                                rows = new ArrayList<>();
                            }
                            rows.clear();
                            rows.addAll(orderList);
                            if (orderAdapter != null && orderAdapter.getData() == rows) {
                                orderAdapter.notifyDataSetChanged();
                            } else {
                                orderAdapter.setNewData(rows);
                            }
                        } else {
                            for (CheckOrderRowsBean bean : orderList) {
                                rows.remove(bean);
                            }
                            rows.addAll(orderList);
                            if (orderList.size() >= pageSize) {
                                SearchOrderActivity.this.page++;
                            }
                            int orderSize = orderList.size();
                            orderAdapter.notifyDataChangedAfterLoadMore(orderSize >= pageSize);
                        }
                    } else {
                        if (checkOrderLv != null) {
                            checkOrderLv.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    if (rows == null) {
                                        rows = new ArrayList<>();
                                    }
                                    try {
                                        orderAdapter.setNewData(rows);
                                    } catch (Throwable e) {

                                    }
                                }
                            }, 400);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                checkOrderLv.setRefreshing(false);
                if (checkOrderLv != null) {
                    checkOrderLv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (rows == null) {
                                rows = new ArrayList<>();
                            }
                            try {
                                orderAdapter.setNewData(rows);
                            } catch (Throwable e) {

                            }
                        }
                    }, 400);
                }
            }
        });
    }

    //  网络请求删除服务器数据
    private void showDialogDeleteOrder(final int position) {

        final AlertDialogEx alert = new AlertDialogEx(SearchOrderActivity.this);
        alert.setTitle("删除");
        alert.setMessage("您确认删除吗？");
        alert.setCancelButton("取消", null);
        alert.setConfirmButton("确定", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                showProgress();
                CheckOrderRowsBean rowsBean = rows.get(position);
                String merchantid = SpUtil.getMerchantid();
                RequestParams params = new RequestParams();
                params.put("merchantId", merchantid);
                params.put("id", String.valueOf(rowsBean.id));
                HttpManager.getInstance().post(AppNetConfig.ORDERS_DELETE, params, new BaseResponse<EmptyBean>() {

                    @Override
                    public void onSuccess(String content, BaseBean<EmptyBean> data, EmptyBean obj) {
                        dismissProgress();
                        if (data != null) {
                            if (data.isSuccess()) {
                                rows.remove(position);
                                orderAdapter.notifyDataSetChanged();
                            }
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                    }
                });
            }
        });
        alert.show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isFrist) {
            isFrist = false;
        } else {//刷新订单列表
//            if (orderAdapter != null && orderAdapter.getData() != null) {
//                getSearchData(0, Math.max(10, currPosition + 4));
//            } else {
//                getSearchData(0);
//            }
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_searchorder;
    }

    @Override
    public void onResult(boolean succ, Object o) {

    }

    @Override
    public void onRefresh(OrderActionBean bean) {
        getSearchData(0, Math.max(10, currPosition + 4));
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        isScroll(ev);
        return super.dispatchTouchEvent(ev);
    }
}