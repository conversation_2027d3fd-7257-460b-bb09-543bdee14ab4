package com.ybmmarket20.activity.afterSales.adapter.detail

import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMMultiViewAdapter
import com.ybmmarket20.bean.aftersales.AfterSalesCompanyReceiveProductInfo
import com.ybmmarket20.bean.aftersales.AfterSalesConsultHistory
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoiceInfo
import com.ybmmarket20.bean.aftersales.ReissueLicenseInfo
import com.ybmmarket20.utils.ImageLoader

/**
 * 售后详情协商历史
 */
class AfterSalesDetailConsultHistoryAdapter(multiData: List<AfterSalesConsultHistory>) :
    YBMMultiViewAdapter<AfterSalesConsultHistory>(
        R.layout.item_after_sales_consult_history,
        multiData
    ) {
    override fun bindMultiView(holder: YBMBaseHolder, bean: AfterSalesConsultHistory) {
        //通用数据
        setCommonData(holder, bean)
        //售后类型
        setAfterSalesType(holder, bean)
        //物流
        setLogistic(holder, bean)
        //需要补发的资质
        setLicense(holder, bean.reissueCredential)
        //专票信息
        setSpecialInvoice(holder, bean.specialInvoiceInfo)
        //有误信息
        setErrorInfo(holder, bean.incorrectInvoiceInfo)
        //补充说明
        setTips(holder, bean.remarks)
        //上传凭证
        setUploadImage(holder, bean.evidences)
        //商家收货信息
        setCustomerReceiveInfo(holder, bean.companyReceiveProductInfo)
    }

    /**
     * 设置文本和空间的显隐
     */
    private fun setTextWithVisible(textView: TextView, text: String?, preText: String? = null, parent: View? = null) {
        if (text.isNullOrEmpty()) {
            if (parent != null) {
                parent.visibility = View.GONE
            } else {
                textView.visibility = View.GONE
            }
            return
        } else {
            parent?.visibility = View.VISIBLE
            textView.visibility = View.VISIBLE
        }
        textView.text = "${preText?: ""}$text"
    }

    private fun View.isVisible(isVisible: Boolean) {
        this.visibility = if (isVisible) View.VISIBLE else View.GONE
    }

    /**
     * 通用数据
     */
    private fun setCommonData(holder: YBMBaseHolder, bean: AfterSalesConsultHistory) {
        val ivAvatar = holder.getView<ImageView>(R.id.ivAvatar)
        val tvCompanyName = holder.getView<TextView>(R.id.tvCompanyName)
        val tvStatusName = holder.getView<TextView>(R.id.tvStatusName)
        val tvCreateTime = holder.getView<TextView>(R.id.tvCreateTime)
        ImageLoader.loadImage(mContext, ivAvatar, bean.avatar)
        setTextWithVisible(tvCompanyName, bean.chatter)
        setTextWithVisible(tvStatusName, bean.auditProcessStateName)
        setTextWithVisible(tvCreateTime, bean.createTime)
    }

    /**
     * 设置售后类型
     */
    private fun setAfterSalesType(holder: YBMBaseHolder, bean: AfterSalesConsultHistory) {
        val llAfterSalesType = holder.getView<LinearLayout>(R.id.llAfterSalesType)
        val tvAfterSalesType = holder.getView<TextView>(R.id.tvAfterSalesType)
        tvAfterSalesType.text = "售后类型：${bean.afterSalesTypeName}"
        llAfterSalesType.isVisible(!bean.afterSalesTypeName.isNullOrEmpty())
    }

    /**
     * 物流
     */
    private fun setLogistic(holder: YBMBaseHolder, bean: AfterSalesConsultHistory) {
        val llLogistic = holder.getView<LinearLayout>(R.id.llLogistic)
        val tvLogisticName = holder.getView<TextView>(R.id.tvLogisticName)
        val tvExpressNo = holder.getView<TextView>(R.id.tvExpressNo)
        setTextWithVisible(tvLogisticName, bean.expressName, "物流公司：")
        setTextWithVisible(tvExpressNo, bean.expressNo, "快递单号：")
        llLogistic.isVisible(bean.isShowLogistic())
    }

    /**
     * 需要补发的资质
     */
    private fun setLicense(holder: YBMBaseHolder, bean: ReissueLicenseInfo?) {
        val llLicense = holder.getView<LinearLayout>(R.id.llLicense)
        val tvCompanyLicense = holder.getView<TextView>(R.id.tvCompanyLicense)
        val tvCompanyReport = holder.getView<TextView>(R.id.tvCompanyReport)
        val tvCompanyGoodsLicense = holder.getView<TextView>(R.id.tvCompanyGoodsLicense)
        setTextWithVisible(tvCompanyLicense, bean?.corpCredential, "企业资质：")
        setTextWithVisible(tvCompanyGoodsLicense, bean?.productCredential, "商品首营资料：")
        setTextWithVisible(tvCompanyReport, bean?.drugSupervisionReport, "商品药检报告：")
        llLicense.isVisible(bean?.isShowReissueLicenseInfo()?: false)
    }

    /**
     * 专票信息
     */
    private fun setSpecialInvoice(holder: YBMBaseHolder, bean: AfterSalesSpecialInvoiceInfo?) {
        val llSpecialInvoiceInfo = holder.getView<LinearLayout>(R.id.llSpecialInvoiceInfo)
        val tvAcceptElectronicSpecialInvoice = holder.getView<TextView>(R.id.tvAcceptElectronicSpecialInvoice)
        val tvSpecialInvoiceCompanyName = holder.getView<TextView>(R.id.tvSpecialInvoiceCompanyName)
        val tvSpecialInvoiceCheckNo = holder.getView<TextView>(R.id.tvSpecialInvoiceCheckNo)
        val tvSpecialInvoiceAddress = holder.getView<TextView>(R.id.tvSpecialInvoiceAddress)
        val tvSpecialInvoicePhone = holder.getView<TextView>(R.id.tvSpecialInvoicePhone)
        val tvSpecialInvoiceBankName = holder.getView<TextView>(R.id.tvSpecialInvoiceBankName)
        val tvSpecialInvoiceAcc = holder.getView<TextView>(R.id.tvSpecialInvoiceAcc)
        setTextWithVisible(tvAcceptElectronicSpecialInvoice, if (bean?.isElectronicInvoice == 1) "是" else "否", "是否接受电子专票：")
        setTextWithVisible(tvSpecialInvoiceCompanyName, bean?.companyName, "公司名称：")
        setTextWithVisible(tvSpecialInvoiceCheckNo, bean?.enterpriseRegistrationNo, "纳税人识别号：")
        setTextWithVisible(tvSpecialInvoiceAddress, bean?.registerAddress, "地址：")
        setTextWithVisible(tvSpecialInvoicePhone, bean?.phone, "电话：")
        setTextWithVisible(tvSpecialInvoiceBankName, bean?.bankName, "开户银行：")
        setTextWithVisible(tvSpecialInvoiceAcc, bean?.acct, "银行账号：")
        llSpecialInvoiceInfo.isVisible(bean?.isShowAfterSalesSpecialInvoiceInfo()?: false)
    }

    /**
     * 补充说明
     */
    private fun setTips(holder: YBMBaseHolder, bean: String?) {
        val tvTips = holder.getView<TextView>(R.id.tvTips)
        setTextWithVisible(tvTips, bean, "补充说明：")
    }

    /**
     * 有误信息
     */
    private fun setErrorInfo(holder: YBMBaseHolder, bean: String?) {
        val tvErrorInf = holder.getView<TextView>(R.id.tvErrorInfo)
        setTextWithVisible(tvErrorInf, bean, "有误信息：")
    }

    /**
     * 上传凭证
     */
    private fun setUploadImage(holder: YBMBaseHolder, bean: List<String>?) {
        val rvUploadImage = holder.getView<RecyclerView>(R.id.rvUploadImage)
        val llUploadImage = holder.getView<LinearLayout>(R.id.llUploadImage)
        if (bean.isNullOrEmpty()) {
            llUploadImage.visibility = View.GONE
        } else {
            llUploadImage.visibility = View.VISIBLE
            rvUploadImage.layoutManager = GridLayoutManager(mContext, 4)
            rvUploadImage.adapter = UploadImageAdapter(bean)
        }
    }

    /**
     * 商家收货信息
     */
    private fun setCustomerReceiveInfo(holder: YBMBaseHolder, bean: AfterSalesCompanyReceiveProductInfo?) {
        val llReceiveProductInfo = holder.getView<LinearLayout>(R.id.llReceiveProductInfo)
        val tvRecipient = holder.getView<TextView>(R.id.tvRecipient)
        val tvReceiveProductAddress = holder.getView<TextView>(R.id.tvReceiveProductAddress)
        val tvCustomerPhone = holder.getView<TextView>(R.id.tvCustomerPhone)
        val tvExpressRemark = holder.getView<TextView>(R.id.tvExpressRemark)
        setTextWithVisible(tvRecipient, bean?.recipient, "收件人：")
        setTextWithVisible(tvReceiveProductAddress, bean?.deliveryAddress, "收货地址：")
        setTextWithVisible(tvCustomerPhone, bean?.receivingPhone, "联系电话：")
        setTextWithVisible(tvExpressRemark, bean?.expressRemarks, "快递说明：")
        llReceiveProductInfo.isVisible(bean?.isShowAfterSalesCompanyReceiveProductInfo()?: false)
    }
}