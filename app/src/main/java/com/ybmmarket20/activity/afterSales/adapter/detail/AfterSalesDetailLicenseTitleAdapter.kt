package com.ybmmarket20.activity.afterSales.adapter.detail

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMSingleViewAdapter


/**
 * 需要补发的资质标题
 */
class AfterSalesDetailLicenseTitleAdapter(title: String) : YBMSingleViewAdapter<String>(
    R.layout.item_after_sales_detail_license_title,
    title
) {
    override fun bindSingleView(holder: YBMBaseHolder, bean: String) {
        val title = holder.getView<TextView>(R.id.tvTitle)
        title.text = bean
    }
}