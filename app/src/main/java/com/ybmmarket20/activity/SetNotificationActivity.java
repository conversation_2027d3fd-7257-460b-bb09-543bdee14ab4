package com.ybmmarket20.activity;

import android.app.PendingIntent;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.widget.CheckBox;
import android.widget.RemoteViews;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.business.order.ui.OrderListActivity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.utils.NotificationUtils;
import com.ybmmarket20.utils.SpUtil;
import com.zxing.activity.CaptureActivity;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

@Router("setnotification")
public class SetNotificationActivity extends BaseActivity {

    @Bind(R.id.cb_setting)
    CheckBox cbSetting;

    public final static String NOTIFICATION_KEY = "sp_notification_key";

    PendingIntent pendingIntent;
    NotificationUtils utils;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_set_notification;
    }

    @Override
    protected void initData() {
        if (cbSetting == null) {
            return;
        }
        setTitle("快捷通知栏设置");
        utils = new NotificationUtils(this, 1003);

        cbSetting.setChecked(isOpen());
        Intent intent = new Intent(SetNotificationActivity.this, MainActivity.class);
        pendingIntent = PendingIntent.getActivity(SetNotificationActivity.this,  0, intent, PendingIntent.FLAG_UPDATE_CURRENT);

    }

    private void setPermissionOpen() {
        if (!utils.isPermissionOpen()) {
            setAlertDialogEx(new AlertDialogEx.OnClickListener() {
                @Override
                public void onClick(AlertDialogEx dialog, int button) {
                    utils.requestPermission();
                }
            }, "快捷状态栏");
        }
    }

    public void setAlertDialogEx(AlertDialogEx.OnClickListener onClickListener, String permission) {
        AlertDialogEx dialogEx = new AlertDialogEx(BaseYBMApp.getApp().getCurrActivity());
        dialogEx.setTitle("提示").setMessage("应用需要 “" + permission + "” 权限，请到 “设置 -> 状态栏与通知” 中授予！").setCancelButton("取消", null).setConfirmButton("确认", onClickListener).show();
    }

    public static boolean isOpen() {
        return SpUtil.readBoolean(NOTIFICATION_KEY, true);//默认为true
    }

    @OnClick({R.id.ll_set_notification})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.ll_set_notification:
                if (cbSetting == null) {
                    return;
                }
                if (cbSetting.isChecked()) {
                    closeNotification();
                } else {
                    openNotification();
                }

                break;
        }
    }

    private void closeNotification() {
        if (cbSetting == null) {
            return;
        }
        cbSetting.setChecked(false);
        cancelNotification();
        SpUtil.writeBoolean(NOTIFICATION_KEY, false);
    }

    private void openNotification() {
        if (cbSetting == null) {
            return;
        }
        cbSetting.setChecked(true);
        OpenNotification();
        SpUtil.writeBoolean(NOTIFICATION_KEY, true);
        cbSetting.postDelayed(new Runnable() {
            @Override
            public void run() {
                setPermissionOpen();
            }
        }, 300);
    }

    // 取消通知
    private void cancelNotification() {
        if (utils != null) {
            utils.cancelNotification(this);
        }
    }

    private void OpenNotification() {
        if (cbSetting == null) {
            return;
        }
        Intent intent = new Intent(this, MainActivity.class);
        intent.putExtra(IntentCanst.ACTIVITY_MAIN, "0");

        Intent intent2 = new Intent(this, CaptureActivity.class);

        Intent intent3 = new Intent(this, CommonH5Activity.class);
        intent3.putExtra(IntentCanst.ACTIVITY_MESSAGE, getMsgUrl());
        intent3.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        Intent intent4 = new Intent(this, OrderListActivity.class);
        intent4.putExtra(IntentCanst.ORDER_STATE, "0");

        RemoteViews bigContentView = getRemoteViews(intent, intent2, intent3, intent4);

        RemoteViews bigContentView2 = getRemoteViews(intent, intent2, intent3, intent4);

        if (utils != null) {
            utils.sendCustomerNotification(null, "药帮忙快捷入口", "快捷入口", "快捷入口", R.drawable.icon_notification_logo, pendingIntent, bigContentView, bigContentView2, false, false, false);
        }

//        intent = new Intent(this, MainActivity.class);
//        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
//        pendingIntent = PendingIntent.getActivity(this, 1003, intent, 0);
//
//        RemoteViews contentView = new RemoteViews(getPackageName(), R.layout.notification_item);
//
//        contentView.setTextViewText(R.id.text11, "首页");
//        contentView.setTextViewText(R.id.text22, "扫码进货");
//        contentView.setTextViewText(R.id.text33, "消息");
//        contentView.setTextViewText(R.id.text44, "订单");
//
//        //自定义跳转
//        contentView.setOnClickPendingIntent(R.id.ll_11, PendingIntent.getBroadcast(SetNotificationActivity.this, 11, new Intent().setAction("a"), PendingIntent.FLAG_UPDATE_CURRENT));
//        contentView.setOnClickPendingIntent(R.id.ll_22, PendingIntent.getBroadcast(SetNotificationActivity.this, 11, new Intent().setAction("b"), PendingIntent.FLAG_UPDATE_CURRENT));
//        contentView.setOnClickPendingIntent(R.id.ll_33, PendingIntent.getBroadcast(SetNotificationActivity.this, 11, new Intent().setAction("c"), PendingIntent.FLAG_UPDATE_CURRENT));
//        contentView.setOnClickPendingIntent(R.id.ll_44, PendingIntent.getBroadcast(SetNotificationActivity.this, 11, new Intent().setAction("d"), PendingIntent.FLAG_UPDATE_CURRENT));
//
//        //用来调用子View中需要一个Int型参数的方法
//        contentView.setInt(R.id.share_content, "setTextColor", NotificationUtils.isDarkNotificationTheme(SetNotificationActivity.this) == true ? Color.WHITE : Color.BLACK);
//
//        NotificationUtils utils = new NotificationUtils(SetNotificationActivity.this, 1003);
//        utils.sendCustomerNotification("ticker", "title", "content", R.drawable.logo, pendingIntent, contentView, contentView2, true, true, true);

//        PendingIntent remotePending = PendingIntent.getActivity(this, 0, new Intent(this, MainActivity.class), PendingIntent.FLAG_UPDATE_CURRENT);
    }

    private RemoteViews getRemoteViews(Intent intent, Intent intent2, Intent intent3, Intent intent4) {
        RemoteViews bigContentView = new RemoteViews(getPackageName(), R.layout.notification_item);
        setRemoteViews(bigContentView);
        setPendingIntent(intent, intent2, intent3, intent4, bigContentView);
        return bigContentView;
    }

    private void setPendingIntent(Intent intent, Intent intent2, Intent intent3, Intent intent4, RemoteViews bigContentView) {
        bigContentView.setOnClickPendingIntent(R.id.ll_11, PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT));
        bigContentView.setOnClickPendingIntent(R.id.ll_22, PendingIntent.getActivity(this, 0, intent2, PendingIntent.FLAG_UPDATE_CURRENT));
        bigContentView.setOnClickPendingIntent(R.id.ll_33, PendingIntent.getActivity(this, (int) SystemClock.uptimeMillis(), intent3, PendingIntent.FLAG_UPDATE_CURRENT));
        bigContentView.setOnClickPendingIntent(R.id.ll_44, PendingIntent.getActivity(this, 0, intent4, PendingIntent.FLAG_UPDATE_CURRENT));
    }

    private void setRemoteViews(RemoteViews bigContentView) {
        bigContentView.setTextViewText(R.id.text11, "首页");
        bigContentView.setTextViewText(R.id.text22, "扫码进货");
        bigContentView.setTextViewText(R.id.text33, "消息");
        bigContentView.setTextViewText(R.id.text44, "订单");
    }

    private static String getMsgUrl() {

        String merchantId = SpUtil.getMerchantid();
        String apiHost = AppNetConfig.getStaticHost2Https();
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(apiHost + "xyyvue/dist/#/messagecenter?");
        stringBuffer.append("ybm_title=消息中心&");
        stringBuffer.append("umkey=1111hd1&");
        stringBuffer.append("head_menu=0&");
        stringBuffer.append("merchantId=" + merchantId);
        return stringBuffer.toString();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isOpen()) {
            openNotification();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ButterKnife.unbind(this);
    }

}
