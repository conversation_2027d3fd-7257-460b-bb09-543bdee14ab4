package com.ybmmarket20.activity;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.AnimationDrawable;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;


import com.apkfuns.logutils.LogUtils;
import com.github.mzule.activityrouter.annotation.Router;
import com.iflytek.cloud.ErrorCode;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.VoiceSearchManager;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.RouterConstantKt;
import com.ybmmarket20.utils.RoutersUtils;

import butterknife.Bind;
import butterknife.OnClick;

import static com.ybmmarket20.constant.RouterConstantKt.CURRENT_PAGE;
import static com.ybmmarket20.constant.RouterConstantKt.OPENNEXTPAGEURLKEY;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 语音搜索
 * <p>
 * 需要搜索页面 - 》 语音识别页面 - 》
 * <p>
 * 1. 返回
 * 2. 指向第三个页面  如：ybmpage://shop_search_result?shopCode=${shopCode}&&searchKey= + 识别结果
 * 3. 指向大搜页面
 * <p>
 * 注意： ybmpage://shop_search_result?shopCode=${shopCode}&&searchKey 这部分内容要urlencode一下，否则路由带不过来
 */
@Router({"searchvoiceactivity"})
public class SearchVoiceActivity extends BaseActivity  {

    public static String INTENT_FROM_SEARCH = "from_search";

    @Bind(R.id.tv)
    TextView tv;
    @Bind(R.id.iv)
    ImageView iv;
    @Bind(R.id.iv_microphone)
    ImageView ivMicrophone;

    private AnimationDrawable loadingDrawable;
    private VoiceSearchManager instance;
    private String ERRORCODE = "10118";
    private String openNextPageUrl;
    private String fromPage;
    private String currentPage;

    @Override
    protected void initData() {
        setTitle("语音搜索");

        fromPage = getIntent().getStringExtra("fromPage");
        openNextPageUrl = getIntent().getStringExtra(OPENNEXTPAGEURLKEY);
        currentPage = getIntent().getStringExtra(CURRENT_PAGE);

        RxPermissions rxPermissions = new RxPermissions(this);
        if (rxPermissions.isGranted(android.Manifest.permission.RECORD_AUDIO)) {
            requestRecordAudioPermission();
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(this,
                    "药帮忙App需要申请录音权限，用于语音识别",
                    () -> requestRecordAudioPermission());
        }

    }

    private void requestRecordAudioPermission() {
        requestEachPermissions(new PermissionCallBack("语音识别需要申请麦克风权限") {
            @Override
            public void granted(Permission permission) {
                startSpeechDialog();
            }
        }, Manifest.permission.RECORD_AUDIO);
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_searchvoice;
    }

    private void startSpeechDialog() {
        showVoice();
        // 使用SpeechRecognizer对象，可根据回调消息自定义界面；
        instance = VoiceSearchManager.getInstance();
        instance.startListening();
        if (instance.getRet() != ErrorCode.SUCCESS) {
            showTip("听写失败");
            BugUtil.sendBug(new IllegalArgumentException("错误码：" + instance.getRet()));
        } else {
            tv.setText("请说出药品名称");// 显示内容
            tv.setTextColor(this.getResources().getColor(R.color.home_back_selected));
        }
        instance.setOnDismissVoiceListener(new VoiceSearchManager.onDismissVoiceListener() {

            @Override
            public void DismissVoice() {
                dismissVoice();
            }

            @Override
            public void onResult(String result, boolean isLast) {
                if (tv == null) {
                    return;
                }

                // LogUtils.e("searchVioce voiceKey = " + result);
                // LogUtils.e("searchVioce openNextPageUrl = " + openNextPageUrl);
                if (!TextUtils.isEmpty(result) && isLast) {
                    tv.setTextColor(SearchVoiceActivity.this.getResources().getColor(R.color.text_search));
                    tv.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_voice_result), result)));

                    if (!TextUtils.isEmpty(openNextPageUrl)) {
                        String decodeNextUrl = RoutersUtils.urlDecode(openNextPageUrl);
                        RoutersUtils.open(decodeNextUrl + result);
                        finish();
                        return;
                    }


                    if (!TextUtils.isEmpty(currentPage)) {
                        //返回到当前页面
                        Intent intent = new Intent();
                        intent.putExtra("key", result);
                        setResult(RouterConstantKt.CURRENT_PAGE_RESULT_VOICE, intent);
                    } else if (TextUtils.isEmpty(fromPage)) {
                        String action = "ybmpage://searchproductop/?voice=" + result;
                        RoutersUtils.open(action);
                    } else {
                        Intent intent = new Intent();
                        intent.putExtra("key", result);
                        setResult(Activity.RESULT_OK, intent);
                    }

                    finish();
                }
            }

            @Override
            public void onErrorCode(String error) {
                if (tv == null) {
                    return;
                }
                if (ERRORCODE.equals(error)) {
                    //未检测到语音;
                    tv.setText(SearchVoiceActivity.this.getResources().getString(R.string.text_voice));
                    tv.setTextColor(SearchVoiceActivity.this.getResources().getColor(R.color.text_voice_search));
                }
            }
        });
    }

    private void showTip(String data) {
        ToastUtils.showShort(data);
    }

    //显示语音
    public void showVoice() {
        if (iv == null || ivMicrophone == null) {
            return;
        }

        iv.setImageResource(R.drawable.voice_loading);
        loadingDrawable = (AnimationDrawable) iv.getDrawable();
        if (loadingDrawable != null) {
            iv.setVisibility(View.VISIBLE);
            ivMicrophone.setVisibility(View.GONE);
            loadingDrawable.start();
        }
    }

    //取消语音
    public void dismissVoice() {
        if (iv == null || ivMicrophone == null) {
            return;
        }
        instance.stopListening();
        if (loadingDrawable != null) {
            iv.setVisibility(View.GONE);
            ivMicrophone.setVisibility(View.VISIBLE);
            loadingDrawable.stop();
        }
    }

    @OnClick({R.id.iv_microphone})
    public void clickTab(View view) {

        switch (view.getId()) {
            case R.id.iv_microphone://语音搜索
                startSpeechDialog();
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (instance != null) {
            instance.onDestroy();
        }
    }
}
