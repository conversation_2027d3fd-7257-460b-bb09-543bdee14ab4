package com.ybmmarket20.activity

import android.graphics.Typeface
import android.view.View
import androidx.activity.viewModels
import androidx.core.view.isVisible
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.vm.BaseVMActivity
import com.ybmmarket20.databinding.ActivityEditInvoiceBinding
import com.ybmmarket20.viewmodel.EditInvoiceViewModel

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/9/18 15:40
 *    desc   :
 */
@Router("editInvoiceActivity")
class EditInVoiceActivity :
    BaseVMActivity<ActivityEditInvoiceBinding, EditInvoiceViewModel>(ActivityEditInvoiceBinding::inflate) {
    override val viewModel: EditInvoiceViewModel by viewModels()

    //当前是编辑状态还是查看状态
    private var mIsEdit = false

    //当前是普通发票还是专用发票
    private var mIsNormal = true
    override fun initData() {
        setTitle("修改发票信息")
        vBinding.tvInvoiceTypeNormal.setOnClickListener {
            changeInvoiceType(true)
            mIsEdit = true
            setEditState(isEdit = mIsEdit)
        }
        vBinding.tvInvoiceTypeSpecial.setOnClickListener {
            changeInvoiceType(false)
            mIsEdit = false
            setEditState(isEdit = mIsEdit)
        }
        setEditState(isEdit = mIsEdit)
        changeInvoiceType(true)
        vBinding.tvSubmit.setOnClickListener {
            if (mIsEdit) {
                showDialog({ _, _ ->
                    submit()
                },{ _, _ ->
                },"请确认修改的发票信息和资质信息保持一致，否则会导致信息修改审核不通过")
            }
        }
    }

    private fun setEditState(isEdit: Boolean) {
        vBinding.tvSubmit.isSelected = isEdit
        vBinding.companyNameInput.setEditable(isEdit)
        vBinding.companyPhoneInput.setEditable(isEdit)
        vBinding.companyNumberInput.setEditable(isEdit)
        vBinding.companyAddressInput.setEditable(isEdit)
        vBinding.companyBankCardInput.setEditable(isEdit)
        vBinding.companyBankNameInput.setEditable(isEdit)
    }

    private fun changeInvoiceType(isNormal: Boolean) {
        mIsNormal = isNormal
        vBinding.tvInvoiceTypeNormal.typeface =if (isNormal) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
        vBinding.tvInvoiceTypeSpecial.typeface =if (!isNormal) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
        vBinding.tvInvoiceTypeNormal.isSelected = isNormal
        vBinding.tvInvoiceTypeSpecial.isSelected = !isNormal
        vBinding.groupSpecial.visibility = if (isNormal) View.GONE else View.VISIBLE
    }
    private fun showDialog(confirmListener: AlertDialogEx.OnClickListener,cancelListener: AlertDialogEx.OnClickListener, str: String) {
        val dialogEx = AlertDialogEx(this)
        dialogEx.setMessage(str)
            .setCancelable(false).setConfirmButton("去修改资质","#00B955", confirmListener)
            .setCancelButton("取消", cancelListener)
            .setCanceledOnTouchOutside(false).setTitle(null).show()
    }
    private fun submit() {
        val map = HashMap<String, String>()
        map.clear()
        if (vBinding.companyNameInput.isVisible) {
            map["companyName"] = vBinding.companyNameInput.getCompanyName()
        }
        if (vBinding.companyPhoneInput.isVisible) {
            map["companyPhone"] = vBinding.companyPhoneInput.getCompanyName()
        }
        if (vBinding.companyNumberInput.isVisible) {
            map["companyNumber"] = vBinding.companyNumberInput.getCompanyName()
        }
        if (vBinding.companyAddressInput.isVisible) {
            map["companyAddress"] = vBinding.companyAddressInput.getCompanyName()
        }
        if (vBinding.companyBankCardInput.isVisible) {
            map["companyBankCard"] = vBinding.companyBankCardInput.getCompanyName()
        }
        if (vBinding.companyBankNameInput.isVisible) {
            map["companyBankName"] = vBinding.companyBankNameInput.getCompanyName()
        }

    }
}