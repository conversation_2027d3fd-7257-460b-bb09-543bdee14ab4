package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.MsgBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.utils.RoutersUtils;

import butterknife.Bind;

/**
 * 消息中心详情
 */
@Router({"msgdetailactivity", "msgdetailactivity/id"})
public class MsgDetailActivity extends BaseActivity {

    @Bind(R.id.tv_time)
    TextView tvTime;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_content)
    TextView tvContent;

    @Override
    protected void initData() {
        setTitle("消息详情");
        MsgBean bean = (MsgBean) getIntent().getSerializableExtra("msgbean");
        String msgId = getIntent().getStringExtra("id");
        if (bean == null && TextUtils.isEmpty(msgId)) {
            ToastUtils.showShort("没有消息内容");
            finish();
        }
        if (!TextUtils.isEmpty(msgId)) {
            getData(msgId);
        }
        if (bean != null) {
            setData(bean);
        }
    }

    private void getData(String msgid) {
        //获取消息内容
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("id", msgid);

    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_msg_detail;
    }

    public static void Intent2Me(MsgBean bean) {
        Intent intent = new Intent(YBMAppLike.getAppContext(), MsgDetailActivity.class);
        intent.putExtra("msgbean", bean);
        YBMAppLike.getApp().getCurrActivity().startActivity(intent);
    }


    public void setData(final MsgBean data) {
        if (!TextUtils.isEmpty(data.title) && data.title.length() < 12) {
            setTitle(data.title);
        }
        tvTitle.setText(data.title);
        tvContent.setText(data.content);
        tvTime.setText(data.sendTime);
        if (!TextUtils.isEmpty(data.action)) {
            tvContent.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RoutersUtils.open(data.action);
                    finish();
                }
            });
        }
    }
}
