package com.ybmmarket20.activity

import android.view.Gravity
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_LICENSE
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.bean.RelatedAptitudeTag
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.OrderServiceView
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.DownloadRelatedAptitudeViewModel
import com.ybmmarket20.viewmodel.OrderDetailViewModel
import kotlinx.android.synthetic.main.activity_download_related_aptitude.clAptitudeDownloadRecord
import kotlinx.android.synthetic.main.activity_download_related_aptitude.etMail
import kotlinx.android.synthetic.main.activity_download_related_aptitude.flReason
import kotlinx.android.synthetic.main.activity_download_related_aptitude.ivEditClear
import kotlinx.android.synthetic.main.activity_download_related_aptitude.rtvAptitudeAfterSale
import kotlinx.android.synthetic.main.activity_download_related_aptitude.rtvConfirm
import kotlinx.android.synthetic.main.activity_download_related_aptitude.tvErrorTips
import kotlinx.android.synthetic.main.activity_download_related_aptitude.tvTips

/**
 * 下载相关资质
 */
@Router("downloadrelatedaptitude/:orgId/:orgName/:orderId/:orderNo/:shopCode/:aptitudeStatus/:afterSalesNo")
class DownloadRelatedAptitudeActivity : BaseActivity() {

    private val mViewModel: DownloadRelatedAptitudeViewModel by viewModels()
    private val afterSaleViewModel: OrderDetailViewModel by viewModels()
    private var mOrgId: String = ""
    private var mOrgName: String = ""
    private var mOrderId: String = ""
    private var mOrderNo: String = ""
    private var mAptitudeStatus: Int = OrderServiceView.APTITUDE_STATUS_POP_APPLY
    private var mAfterSalesNo: String = ""
    private var afterSaleDetailHtmlUrl: String = ""
    private var applyInvoiceHtmlUrl: String = ""

    override fun getContentViewId(): Int = R.layout.activity_download_related_aptitude

    override fun initData() {
        setTitle("资质相关")
        mOrgId = intent.getStringExtra("orgId") ?: ""
        mOrgName = intent.getStringExtra("orgName") ?: ""
        mOrderId = intent.getStringExtra("orderId") ?: ""
        mOrderNo = intent.getStringExtra("orderNo") ?: ""
        mAfterSalesNo = intent.getStringExtra("afterSalesNo")?: ""
        afterSaleDetailHtmlUrl = intent.getStringExtra("afterSaleDetailHtmlUrl")?: ""
        applyInvoiceHtmlUrl = intent.getStringExtra("applyInvoiceHtmlUrl")?: ""
        mAptitudeStatus = try {
            (intent.getStringExtra("aptitudeStatus"))!!.toInt()
        } catch (e: Exception) {
            e.printStackTrace()
            OrderServiceView.APTITUDE_STATUS_POP_APPLY
        }
        initObserver()
        mViewModel.getRelatedAptitudeList(mOrgId, mOrderNo)
        setAptitudeStatusInfo()
        clAptitudeDownloadRecord.setOnClickListener {
            RoutersUtils.open("ybmpage://downloadrecord/$mOrderNo")
        }
        flReason.setOnItemClick {
            if (mAptitudeStatus == OrderServiceView.APTITUDE_STATUS_SELF) {
                handleNoAptitudeTagForClick(it)
            } else {
                handleNoAptitudeTagForPopClick(it)
            }
        }
        tvTips.setOnClickListener {
            handleTipClick()
        }
        ivEditClear.setOnClickListener {
            etMail.setText("")
        }
        etMail.doAfterTextChanged {
            handleEditInput(it.toString())
        }
        rtvConfirm.setOnClickListener {
            confirm()
        }
    }

    private fun setAptitudeStatusInfo() {
        if (mAptitudeStatus == OrderServiceView.APTITUDE_STATUS_SELF) {
            rtvAptitudeAfterSale.isVisible = false
            return
        }
        rtvAptitudeAfterSale.text = if (mAptitudeStatus == OrderServiceView.APTITUDE_STATUS_POP_APPLY) "申请资质售后" else "查看资质售后"
        rtvAptitudeAfterSale.setOnClickListener {
            val orderDetail = CheckOrderDetailBean().apply {
                orderNo = mOrderNo
                orgId = mOrgId
                origName = mOrgName
            }
            if (mAptitudeStatus == OrderServiceView.APTITUDE_STATUS_POP_APPLY) {
                if (applyInvoiceHtmlUrl.isNullOrEmpty()) {
                    showProgress()
                    afterSaleViewModel.getAfterSalesInfo(orderDetail, TIPS_TYPE_LICENSE)
                } else {
                    RoutersUtils.open("ybmpage://commonh5activity?url=" + applyInvoiceHtmlUrl + "&isShowCart=0")
                }
            } else {
                if (afterSaleDetailHtmlUrl.isNullOrEmpty()) {
                    RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=$mAfterSalesNo")
                } else {
                    RoutersUtils.open("ybmpage://commonh5activity?url=" + afterSaleDetailHtmlUrl + "&isShowCart=0")
                }
            }
        }
    }

    private fun confirm() {
        val reason = flReason.getData()
            ?.filter { it.isEnable() && it.isSelected }
            ?.map { it.code }
            ?.joinToString(",") ?: ""
        if (reason.isEmpty()) {
            ToastUtils.showShort("请先选择下载类型")
            return
        }
        if (!UiUtils.isEmail(etMail.text.toString())) {
            ToastUtils.showShort("请先输入正确的邮箱")
            return
        }
        mViewModel.submitDownloadAptitude(
            mapOf(
                "orderNo" to mOrderNo,
                "merchantId" to SpUtil.getMerchantid(),
                "email" to etMail.text.toString(),
                "qualificationTypeArr" to reason,
                "orgId" to mOrgId
            )
        )
    }

    private fun handleEditInput(content: String) {
        ivEditClear.isVisible = content.isNotEmpty()
        tvErrorTips.isVisible = !UiUtils.isEmail(content) && content.isNotEmpty()
        if (!UiUtils.isEmail(content) && content.isNotEmpty()) {
            tvErrorTips.isVisible = true
            etMail.setBackgroundResource(R.drawable.shape_aptitude_download_recode_edit_error)
        } else {
            tvErrorTips.isVisible = false
            etMail.setBackgroundResource(R.drawable.shape_aptitude_download_recode_edit)
        }
    }

    private fun handleTipClick() {
        AlertDialogEx(this).apply {
            setTitle("收不到邮件？\n建议您从这几个方面检查")
            setMessage(
                "1.检查邮箱地址是否输入正确\n" +
                        "2.检查邮件是否在垃圾箱内\n" +
                        "3.将如下邮箱设置为白名单\n" +
                        "4.更换邮箱再次尝试发送".trimIndent()
            )
            setMessageGravity(Gravity.CENTER_VERTICAL)
            setConfirmButton("我知道了") { d, _ -> d.dismiss() }
            show()
        }
    }

    /**
     * 点击POP无资质
     */
    private fun handleNoAptitudeTagForPopClick(tag: RelatedAptitudeTag?) {
        if (tag?.isEnable() == true) return
        AlertDialogEx(this).apply {
            setTitle("资质说明")
            setMessage("商家尚未上传${tag?.msg ?: ""}，请申请资质售后。")
            setConfirmButton("去申请") { _, _ ->
                if (applyInvoiceHtmlUrl.isNullOrEmpty()) {
                    val orderDetail = CheckOrderDetailBean().apply {
                        orderNo = mOrderNo
                        orgId = mOrgId
                        origName = mOrgName
                    }
                    showProgress()
                    afterSaleViewModel.getAfterSalesInfo(orderDetail, TIPS_TYPE_LICENSE)
                } else {
                    RoutersUtils.open("ybmpage://commonh5activity?url=" + applyInvoiceHtmlUrl + "&isShowCart=0")
                }
            }
            setCancelButton("取消") { d, _ -> d.dismiss() }
            show()
        }
    }

    /**
     * 点击自营无资质
     */
    private fun handleNoAptitudeTagForClick(tag: RelatedAptitudeTag?) {
        if (tag?.isEnable() == true) return
        AlertDialogEx(this).apply {
            setTitle("资质说明")
            setMessage("暂无相关资质，请联系平台客服")
            setConfirmButton("好的") {_, _ ->
                dismiss()
            }
            show()
        }
    }

    private fun initObserver() {
        mViewModel.tagListLiveData.observe(this) {
            if (it.isSuccess) {
                flReason.setData(it.data)
            }
        }
        mViewModel.submitResultLiveData.observe(this) {
            ToastUtils.showLong("发送成功")
        }
        afterSaleViewModel.afterSaleTipsLiveData.observe(this) {
            RoutersUtils.open(it)
            dismissProgress()
        }
    }

    override fun getBaseViewModel(): BaseViewModel = mViewModel
}