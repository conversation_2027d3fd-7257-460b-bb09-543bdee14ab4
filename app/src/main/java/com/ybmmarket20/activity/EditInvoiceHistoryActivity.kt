package com.ybmmarket20.activity

import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.adapter.InvoiceEditHistoryAdapter
import com.ybmmarket20.bean.InvoiceEditHistoryBean
import com.ybmmarket20.common.vm.BaseVMActivity
import com.ybmmarket20.databinding.ActivityEditInvoiceHistoryBinding
import com.ybmmarket20.viewmodel.EditInvoiceViewModel

/**
 *    author : hcq
 *    date   : 2025/9/18 15:40
 *    desc   : 发票修改记录页面
 */
@Router("editInvoiceHistoryActivity")
class EditInvoiceHistoryActivity :
    BaseVMActivity<ActivityEditInvoiceHistoryBinding, EditInvoiceViewModel>(ActivityEditInvoiceHistoryBinding::inflate) {

    override val viewModel: EditInvoiceViewModel by viewModels()
    private var adapter: InvoiceEditHistoryAdapter? = null

    override fun initData() {
        setTitle("修改记录")

        // 获取Intent参数
        getIntentParams()

        // 初始化UI
        initViews()

        // 设置观察者
        setupObservers()
    }

    /**
     * 获取Intent参数
     */
    private fun getIntentParams() {

    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        // 初始化RecyclerView
        adapter = InvoiceEditHistoryAdapter(mutableListOf())
        vBinding.rvEditHistory.layoutManager = LinearLayoutManager(this)
        vBinding.rvEditHistory.adapter = adapter

    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        val data = getMockDataList();
        if (data != null && data.isNotEmpty()) {
            // 显示数据
            adapter?.setNewData(data)
            vBinding.rvEditHistory.visibility = View.VISIBLE
            vBinding.llEmptyView.visibility = View.GONE
        } else {
            // 显示空状态
            showEmptyView()
        }
    }

    /**
     * 显示空状态视图
     */
    private fun showEmptyView() {
        vBinding.rvEditHistory.visibility = View.GONE
        vBinding.llEmptyView.visibility = View.VISIBLE
    }


    fun getMockDataList(): MutableList<InvoiceEditHistoryBean> {
        val mockList = mutableListOf<InvoiceEditHistoryBean>()

        // 添加一些模拟的修改记录，匹配UI截图
        mockList.add(
            InvoiceEditHistoryBean(
                id = "1",
                editTime = "2025年9月18日 13:30",
                editStatus = 2,
                editStatusName = "审核通过",
                operator = "系统",
                remark = "审核通过",
                invoiceType = "增值税专用发票",
                companyName = "辽宁市西安区永和大药房",
                taxNumber = "91420112MA4KLGHW01",
                address = "武汉市东湖新技术开发区关东大道77号金融港后台服务中心一期A1栋第3层301室（注册地址：武汉市武昌区）",
                phone = "027-********",
                bankName = "中国建设银行武汉白沙洲支行",
                bankAccount = "62043098712004076353"
            )
        )

        mockList.add(
            InvoiceEditHistoryBean(
                id = "2",
                editTime = "2025年9月18日 13:30",
                editStatus = 1,
                editStatusName = "审核通过",
                operator = "系统",
                remark = "审核通过",
                invoiceType = "电子普通发票",
                companyName = "辽宁市西安区永和大药房",
                taxNumber = "91420112MA4KLGHW01",
                address = "武汉市工商金融港后台服务中心一期A2栋第3层301室（注册地址：武汉市武昌区）",
                phone = "027-********",
                bankName = "中国建设银行武汉白沙洲支行",
                bankAccount = "62043098712004076353"
            )
        )

        return mockList
    }
}