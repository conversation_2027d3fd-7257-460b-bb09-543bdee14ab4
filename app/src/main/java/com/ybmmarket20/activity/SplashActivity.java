package com.ybmmarket20.activity;

import static com.ybm.activity.ad.AdFragment.ACTION_ADBEAN;
import static com.ybm.activity.ad.AdFragment.ACTION_URL;
import static com.ybm.activity.ad.AdFragment.AD_SCM_STR;
import static com.ybm.activity.ad.AdFragment.AD_SPM_STR;
import static com.ybm.activity.ad.AdFragment.IMAGE_URL;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import androidx.lifecycle.ViewModelProvider;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.mzule.activityrouter.annotation.Router;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.tencent.smtt.sdk.QbSdk;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.NetUtil;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybm.app.utils.SpUtil;
import com.ybm.app.utils.file.ExternalFileManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AdDataBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.SkinPeelerBean;
import com.ybmmarket20.bean.cart.CartLayoutType;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.PrivacyDialog;
import com.ybmmarket20.common.PrivacyInitManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.ConstantData;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.viewmodel.ElsePageViewModel;
import com.ybmmarket20.xyyreport.XyyReportManager;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.concurrent.TimeUnit;


/**
 * edit by wh 2018.4.27
 * 启动页
 */
@Router({"http://ybm100.com/splash", "splashactivity"})
public class SplashActivity extends BaseActivity {

    private static final int AD_SUCCESS = 100;
    private static final int AD_FAILURE = 101;
    private static final int AD_DISPLAY = 102;
    private static final int IS_FIRST = 103;
    private AlertDialogEx alert;
    private boolean isAdSuccess = false;
    private boolean isStart = false;
    private boolean isFirst;
    private String adUrl;
    private String mAdSpmStr = "";
    private String mAdScmStr = "";
    private String actionUrl;
    public static final int HOME_TYPE_STEADY = 0; //静态首页 对应newLayout
    public static final int HOME_TYPE_CMS = 1;    //cms首页 对应new
    public static final int HOME_TYPE_OLD = 2;    //旧首页  对应old
    private AdDataBean adDataBean;
    private int startActivityCount = 0;
    private ElsePageViewModel mViewModel;

    @Override
    protected void onStart() {
        super.onStart();
        startActivityCount = YBMAppLike.createdActivityCount;
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (!isFinishing() && YBMAppLike.createdActivityCount == startActivityCount) {
            ToastUtils.showShort("药帮忙App已进入后台");
        }
    }

    @Override
    protected void onCreate(Bundle savedBundle) {
        super.onCreate(savedBundle);
        //TODO： 开关接口，如果接口失败或打开状态则检查应用签名
        mViewModel = new ViewModelProvider(this).get(ElsePageViewModel.class);
//        String md5 = SignUtil.md5(SignUtil.getSignature(this));
//        if (!md5.equals("e4af14a4133a4fa286c80d6edf5f519f")) {
//            Toast.makeText(YBMAppLike.getAppContext()
//                    , "应用签名不符，请从官方渠道下载后重新安装"
//                    , Toast.LENGTH_LONG).show();
//            finish();
//            new Handler().postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    System.exit(0);
//                }
//            }, 3000);
//        }
//        ImmersionBar.with(this).init();
//        ImmersionBar.with(this).fullScreen(true);
//        setContentView(R.layout.activity_splash);
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                ExternalFileManager.get().onCreate(getApplicationContext());
            }
        });
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        try {
            Uri data = getIntent().getData();
            if (data != null) {
                LogUtils.i(data.toString());
            }
        } catch (Exception ignore) {

        }
        handleFromUrl();
    }

    private void handleFromUrl() {
        try {
            String router = getIntent().getExtras().getString("router", "");
            Log.i("splash_router", router);
            if (!TextUtils.isEmpty(router)) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        boolean result = RoutersUtils.open(router);
                        if (!result) {
                            RoutersUtils.open("ybmpage://main");
                        }
                        finish();
                    }
                }, 2000);
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 展示温馨提示
     */
    private void showMention() {
        PrivacyInitManager.INSTANCE.showPrivacyAgreementDialog(new PrivacyDialog.OnClickListener() {
            @Override
            public void onPositiveClicked(Dialog alertDialog) {
                initRequest();
                SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd", Locale.CHINA);
                String savedDateStr = SpUtil.readString("splash_permission_date", "");
                String newSaveDateStr = df.format(new Date());
                if (newSaveDateStr.equals(savedDateStr)) {
                    // 如果是同一天，直接跳转，不提示权限申请
                    jumpNext();
                } else {
                    SpUtil.writeString("splash_permission_date", newSaveDateStr);
                    getPermission();
                }
            }

            @Override
            public void onNegativeClicked(Dialog alertDialog) {
                finish();
                System.exit(0);
            }
        }, new PrivacyInitManager.ClickableSpanCallback() {
            @Override
            public void onClickServiceProtocol() {
                mViewModel.getLoginAgreement(2);
            }

            @Override
            public void onClickPrivacyPolicy() {
                mViewModel.getLoginAgreement(1);
            }
        });
    }

    @Override
    protected void initData() {
        handleFromUrl();
        try {
            String router = getIntent().getExtras().getString("router", "");
            if (!TextUtils.isEmpty(router)) return;
        } catch (Exception e) {
            e.printStackTrace();
        }
        SpUtil.writeInt(ConstantData.SHOW_CMS, HOME_TYPE_CMS);
        //加载qq浏览器内核
//        initQQX5Core();
        isFirst = IntentCanst.GUIDE_VERSION > SpUtil.readInt(IntentCanst.GUIDE_RUN, 0);
        try {

            Uri data = getIntent().getData();
            if (data != null) {
                LogUtils.i(data.toString());
            }
            if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
                finish();
                return;
            }
        } catch (Exception ignore) {

        }
        showMention();
        initRequest();
        //注册pushtoken
        registerPushtoken();
    }

    public void getPermission() {
        if (isLogin()) {
            // 未登录在登录页面申请，否则应用市场会拒
            RxPermissions rxPermissions = new RxPermissions(SplashActivity.this);
            if (rxPermissions.isGranted(android.Manifest.permission.READ_PHONE_STATE)) {
                getDeviceIdRootPermissions(rxPermissions);
            } else {
                PermissionDialogUtil.showPermissionInfoDialog(SplashActivity.this,
                        "药帮忙App需要申请手机/电话权限，用于读取设备识别码（IMEI、IMSI和MAC），防止账号被盗",
                        () -> getDeviceIdRootPermissions(rxPermissions));
            }
        } else {
            jumpNext();
        }
    }

    private void initRequest() {
        if (PrivacyInitManager.INSTANCE.isAgreedPrivacy()) {
            //获取黑白换肤开关状态
            getSkinPeeler();
            getCancelAttention();
            getCartLayoutType();
        }
    }

    private void getSkinPeeler() {//换肤
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GET_SKIN_PEELER_SWITCH).build();
        HttpManager.getInstance().post(params, new BaseResponse<SkinPeelerBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SkinPeelerBean> obj, SkinPeelerBean skinPeelerBean) {
                if (obj != null && obj.isSuccess() && skinPeelerBean != null) {
                    SpUtil.writeBoolean(ConstantData.SKIN_PEELER, skinPeelerBean.getDetail());
                }
            }
        });
    }

    private void registerPushtoken() {
        PrivacyInitManager.INSTANCE.initPushToken(this);
    }

    // 获取是否开启cms
    private void getCancelAttention() {
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.GET_LAYOUT_TYPE).build();
        // old使用原admin布局，new使用cms配置布局
        HttpManager.getInstance().post(params, new BaseResponse<String>() {
            @Override
            public void onSuccess(String content, BaseBean<String> obj, String s) {
                if ("newLayout".equalsIgnoreCase(s)) {
                    SpUtil.writeInt(ConstantData.HOME_TYPE, HOME_TYPE_STEADY);
                } else if ("new".equalsIgnoreCase(s)) {
                    SpUtil.writeInt(ConstantData.HOME_TYPE, HOME_TYPE_CMS);
                } else {
                    SpUtil.writeInt(ConstantData.HOME_TYPE, HOME_TYPE_OLD);
                }
            }
        });

    }


    // 获取购物车样式
    private void getCartLayoutType() {
        RequestParams requestParams = new RequestParams();
        requestParams.put("scene", "getCart");
        HttpManager.getInstance().post(AppNetConfig.GET_CART_LAYOUT_TYPE, requestParams, new BaseResponse<CartLayoutType>() {
            @Override
            public void onSuccess(String content, BaseBean<CartLayoutType> obj, CartLayoutType cartLayoutType) {
                if (cartLayoutType != null) {
                    SpUtil.writeInt(ConstantData.CART_TYPE, cartLayoutType.getStyleTemplate());
                }
            }
        });
    }

    private void jumpNext() {
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                getAdConfig();
            }
        });
    }


    /****
     * 获取广告
     * **/
    private void getAdConfig() {
        //如果是第一次直接到引导页
        if (isFirst) {
            checkSendMessage(IS_FIRST);
            return;
        }
        mHandler.postDelayed(new Runnable() {//接口1.2秒如果没有执行成功就自己跳转主页
            @Override
            public void run() {
                if (mHandler == null || isFinishing() || isDestroy) {
                    return;
                }
                goNextUi();
            }
        }, 1200);
        float hpw = UiUtils.getScreenHeight() * 1.0f / UiUtils.getScreenWidth();
        DecimalFormat df = new DecimalFormat("#.00");
        String str = df.format(hpw);
//        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.AD_CONFIG)
        RequestParams params = RequestParams.newBuilder()
                .url(AppNetConfig.AD_CONFIG_NEW)
                .addParam("h", UiUtils.getScreenHeight() + "")
                .addParam("w", UiUtils.getScreenWidth() + "").addParam("hpw", str)
                .addParam("merchantId", merchant_id == null ? "" : merchant_id)
                .build();
        HttpManager.getInstance().post(params, new BaseResponse<AdDataBean>() {
            @Override
            public void onSuccess(String content, BaseBean<AdDataBean> adBean, AdDataBean data) {
                if (mHandler != null) {
                    mHandler.removeCallbacks(null);
                }
                if (adBean != null && adBean.isSuccess() && data != null) {
                    //是否使用新的主题
                    YBMAppLike.isNewTheme = adBean.getData().isNewTheme;
                    boolean isDisplay = adBean.getData().isdisplay;
                    //容错处理，广告图片地址为Null 则广告不显示
                    String adUrl1 = checkAdUrlByResolution(adBean.data);
                    if (StringUtil.isEmpty(adUrl1)) {
                        isDisplay = false;
                    }
                    adDataBean = data;
                    adUrl = ImageUtil.getImageUrl(adUrl1);
                    try {
                        mAdSpmStr = adBean.getData().trackData.getSpmEntity().concat();
                        mAdScmStr = adBean.getData().trackData.getScmEntity().concat();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    actionUrl = adBean.getData().jumpUrl;
                    if (isFirst) {
                        return;
                    }
                    loadAdFile(adUrl, isDisplay);
                } else {
                    checkSendMessage(AD_FAILURE);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (mHandler != null) {
                    mHandler.removeCallbacks(null);
                }
                checkSendMessage(AD_FAILURE);
            }
        });
    }

    /**
     * 根据分辨率获取广告图片url
     *
     * @return
     */
    private String checkAdUrlByResolution(AdDataBean adData) {
        float rat = UiUtils.getScreenHeight(this) * 1f / UiUtils.getScreenWidth();
        if (rat < 1.8f) {
            return adData.startImage;
        } else return adData.startImageOther;
    }

    private void loadAdFile(String url, boolean display) {
        // 后台下载广告，第一次进入app直接到引导页
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                try {
                    ImageHelper.with(getApplicationContext())
                            .load(url).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .into(UiUtils.getScreenWidth(), UiUtils.getScreenHeight())
                            .get(3, TimeUnit.SECONDS);
                    //接口返回不显示
                    if (!display) {
                        checkSendMessage(AD_DISPLAY);
                        return;
                    }
                    checkSendMessage(AD_SUCCESS);
                } catch (Exception e) {
                    // BugUtil.sendBug(new Throwable("启屏页广告图片加载失败： url = " + url + "\n " + e.toString()));
                    checkSendMessage(AD_FAILURE);
                }
            }
        });

    }

    @Override
    public int getContentViewId() {
        return 0;
    }

    //发送消息
    private void checkSendMessage(int what) {
        if (mHandler == null) {
            return;
        }
        mHandler.sendEmptyMessage(what);
    }

    private Handler mHandler = new Handler(YBMAppLike.getAppContext().getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AD_SUCCESS://下载成功
                    isAdSuccess = true;
                    break;
                case AD_FAILURE://下载失败
                case AD_DISPLAY://不显示
                case IS_FIRST://第一次只显示广告
                    isAdSuccess = false;
                    break;
            }
            goNextUi();
        }
    };

    private void goNextUi() {
        if (isStart) {
            return;
        }
        isStart = true;
        //从首选项拿出判断是否为第一次，是否是更新
        Class<? extends Activity> target;
        Bundle bundle = null;
        if (isFirst) {
            target = GuidePageActivity.class;
        } else {
            String merchantId = com.ybmmarket20.utils.SpUtil.getMerchantid();
            if (!TextUtils.isEmpty(merchantId)) {
                XyyIoUtil.identify(merchantId);
                XyyReportManager.setSignInfo(this, merchantId, com.ybmmarket20.utils.SpUtil.getAccountId());
            }
            //没网或者下载不成功（包括不让显示）
            if (NetUtil.getNetworkState(getMySelf()) == NetUtil.NETWORN_NONE || !isAdSuccess) {
                if (isLogin()) {
                    target = MainActivity.class;
                } else {
                    target = LoginActivity.class;
                }
            } else {
                //去首页再跳转广告页 不直接跳广告页 因为要提前加载首页数据
                if (isLogin()) {
                    target = MainActivity.class;
                } else {
                    target = LoginActivity.class;
                }
                bundle = new Bundle();
                bundle.putString(IMAGE_URL, adUrl);
                bundle.putString(ACTION_URL, actionUrl);
                bundle.putString(AD_SPM_STR, mAdSpmStr);
                bundle.putString(AD_SCM_STR, mAdScmStr);
                bundle.putSerializable(ACTION_ADBEAN, adDataBean);
            }
        }
        gotoAtivity(target, bundle);
        finish();
        overridePendingTransition(R.anim.splash_in, R.anim.splash_out);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (alert != null) {
            try {
                alert.dismiss();
            } catch (Throwable throwable) {
                alert = null;
            }
        }
    }

    private static class QBSdkPreInitCallback implements QbSdk.PreInitCallback {
        @Override
        public void onCoreInitFinished() {
            LogUtils.d("X5-core初始化完成");
        }

        @Override
        public void onViewInitFinished(boolean b) {
            LogUtils.d("X5-view初始化完成:" + b);
        }
    }

    //初始化qq浏览器内核
    private void initQQX5Core() {
        QbSdk.initX5Environment(getApplication().getApplicationContext(), new QBSdkPreInitCallback());
    }

    @SuppressLint("CheckResult")
    private void getDeviceIdRootPermissions(RxPermissions rxPermissions) {
        rxPermissions.request(
                android.Manifest.permission.READ_PHONE_STATE
        ).subscribe(granted -> {
            if (granted) { // 在android 6.0之前会默认返回true
                jumpNext();
            } else {
                //未获取权限
                jumpNext();
            }
        }, throwable -> {
            jumpNext();
        });

    }
}
