package com.ybmmarket20.activity;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.UpdateManager;
import com.ybmmarket20.R;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.navigationbar.DefaultNavigationBar;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarketkotlin.viewmodel.UnregisterViewModel;

import butterknife.Bind;

/**
 * 关于我们
 */
@Router("about")
public class AboutActivity extends BaseActivity {
    @Bind(R.id.about_version)
    TextView aboutVersion;
    @Bind(R.id.about_phone)
    TextView aboutPhone;
    @Bind(R.id.tv_unregister)
    TextView tvUnregister;
    private String versionName;
    private double clickTime;
    private int clickCount;
    private UnregisterViewModel mViewModel;

    @Override
    protected void initData() {
        setTitle("关于我们");
        mViewModel = new ViewModelProvider(this).get(UnregisterViewModel.class);
        mViewModel.findUnregisterStatus(SpUtil.getMerchantid());
        aboutPhone.setText(getResources().getString(R.string.about_tv02));
        initVersion();
        initObserver();
    }

    private void initObserver() {
        mViewModel.getUnregisterStatus().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean status) {
                // ture : 已申请冻结，false :未申请冻结
                if (status) {
                    tvUnregister.setText("账号注销审核中，点击可撤销申请");
                    tvUnregister.setOnClickListener(v -> {
                        showConfirmDialog();
                    });
                } else {
                    tvUnregister.setText("账号注销");
                    tvUnregister.setOnClickListener(v -> {
                        RoutersUtils.open("ybmpage://unregister");
                    });
                }
            }
        });
        mViewModel.getCancelDropAccountLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    mViewModel.findUnregisterStatus(SpUtil.getMerchantid());
                }
            }
        });
    }

    private void showConfirmDialog() {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage("是否撤销注销申请？")
                .setCancelButton("否", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                })
                .setConfirmButton("是", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                    mViewModel.cancelDropAccount(SpUtil.getMerchantId());
                })
                .setCancelable(false)
                .setCanceledOnTouchOutside(false)
                .setTitle(null)
                .show();
    }

    @Override
    protected void initHead() {
        super.initHead();
        new DefaultNavigationBar.Builder(this).setTitle("关于我们").build();
    }

    private void initVersion() {
        String packageName = this.getPackageName();
        try {
            versionName = this.getPackageManager().getPackageInfo(
                    packageName, 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        aboutVersion.setText("版本号:V" + UpdateManager.getVersion());
        if (BaseYBMApp.getApp().isDebug()) {
            aboutVersion.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    startActivity(new Intent(getApplicationContext(), DebugActivity.class));
                }
            });
        }
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_about;
    }

}
