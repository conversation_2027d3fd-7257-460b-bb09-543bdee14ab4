package com.ybmmarket20.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BalanceExtractBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.OrderStatusNumber;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.fragments.BalanceFragment;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 明细页面
 */
@Router("balanceactivity")
public class BalanceActivity extends BaseActivity {

    @Bind(R.id.tv_all_balance)
    TextView tvAllBalance;
    @Bind(R.id.tv_total_balance)
    TextView tvTotalBalance;
    @Bind(R.id.tv_receive_balance)
    TextView tvReceiveBalance;
    @Bind(R.id.vp_balance)
    ViewPager vpBalance;
    @Bind(R.id.tv_extract)
    TextView mTvExtract;
    @Bind(R.id.ps_tab)
    SlidingTabLayout psTab;
    @Bind(R.id.tv_balance)
    TextView tvBalance;
    @Bind(R.id.tv_tip)
    TextView tvTip;

    private String[] mTitle = {"全部记录", "收入记录", "支出记录"};
    private List<BalanceFragment> list_fragment = new ArrayList<>();
    private BalanceAdapter adapter;

    @Override
    protected void initData() {
        setTitle("我的余额");
        setRigthText(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RoutersUtils.open(getUrl());
            }
        }, "使用帮助");

        adapter = new BalanceAdapter(getSupportFragmentManager());
        vpBalance.setAdapter(adapter);

        psTab.setViewPager(vpBalance);
        psTab.setIndicatorWidthEqualTitleHalf(true);

        vpBalance.setOffscreenPageLimit(mTitle.length + 1);
        getOderStatus();

    }

    private void getOderStatus() {
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.ORDER_STATUS_NUMER, params, new BaseResponse<OrderStatusNumber>() {
            @Override
            public void onSuccess(String content, BaseBean<OrderStatusNumber> obj, OrderStatusNumber data) {
                if (obj != null && obj.isSuccess() && data != null) {
                    tvBalance.setVisibility(data.waitDrawBlanceNum > 0 ? View.VISIBLE : View.GONE);
                    if (!TextUtils.isEmpty(data.balanceTips)) {
                        tvTip.setVisibility(View.VISIBLE);
                        tvTip.setText(data.balanceTips);
                    }
                }
            }
        });
    }

    /*
     * ybmpage://commonh5activity?cache=0&url=
     * https://app-v4.ybm100.com/static/xyyvue/dist/#/conrule?ybm_title=余额使用说明&head_menu=0
     * */
    public String getUrl() {
        return "ybmpage://commonh5activity?url=" + AppNetConfig.getStaticHost2Https() + "xyyvue/dist/#/conrule?ybm_title=余额使用说明&head_menu=0";
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_my_balance;
    }

    @OnClick({R.id.ll_total, R.id.ll_wait_receive, R.id.tv_extract})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_total:
                RoutersUtils.open("ybmpage://balanceotherdetail/4");
                break;
            case R.id.ll_wait_receive:
                RoutersUtils.open("ybmpage://balanceotherdetail/3");
                break;
            case R.id.tv_extract:
                getResponse();
                break;
        }
    }

    /**
     * 用户余额提现
     */
    private void getResponse() {
        if (tvAllBalance == null) {
            return;
        }
        showProgress();
        final String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.BANK_CHECK, params, new BaseResponse<BalanceExtractBean>() {
            @Override
            public void onSuccess(String content, BaseBean<BalanceExtractBean> obj, BalanceExtractBean bean) {
                dismissProgress();
                if (obj.isSuccess() && bean != null) {
                    if (!TextUtils.isEmpty(bean.action)) {
                        RoutersUtils.open(bean.action);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    /**
     * fragment适配器，返回得到的数据
     */
    private class BalanceAdapter extends FragmentStatePagerAdapter implements BalanceFragment.BalanceNumberListener {


        public BalanceAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {

            if (list_fragment == null || list_fragment.isEmpty() || list_fragment.size() <= position || list_fragment.get(position) == null) {
                if (list_fragment == null) {
                    list_fragment = new ArrayList<>();
                }
                if (list_fragment.size() > position && list_fragment.get(position) == null) {
                    list_fragment.remove(position);
                }
                int type = (position == 0 ? BalanceFragment.TYPE_ALL : (position == 1 ? BalanceFragment.TYPE_INCOME : BalanceFragment.TYPE_EXPEND));
                Bundle bundle = new Bundle();
                bundle.putInt(BalanceFragment.BUNDLE_TYPE, type);
                BalanceFragment fragment = BalanceFragment.getInstance(bundle);
                fragment.setBalanceListener(this);
                list_fragment.add(position, fragment);
            }
            return list_fragment.get(position);
        }

        @Override
        public int getCount() {
            return mTitle.length;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return position >= mTitle.length ? mTitle[0] : mTitle[position];
        }

        @Override
        public void getBalanceNumber(String total, String wait, String available, int isShowCash) {
            String totalBalance = TextUtils.isDigitsOnly(total) ? "0.0" : total;
            String waitBalance = TextUtils.isDigitsOnly(wait) ? "0.0" : wait;
            String availableBalance = TextUtils.isDigitsOnly(available) ? "0.0" : available;
            boolean isShow = isShowCash == 1;
            if (tvTotalBalance != null) {
                tvTotalBalance.setText(totalBalance);
            }
            if (tvReceiveBalance != null) {
                tvReceiveBalance.setText(waitBalance);
            }
            if (tvAllBalance != null) {
                tvAllBalance.setText(availableBalance);
            }
            if (mTvExtract != null) {
                mTvExtract.setVisibility(isShow ? View.VISIBLE : View.GONE);
            }
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        for (BalanceFragment fragment : list_fragment) {
            if (fragment != null) {
                fragment.getData();
            }
        }
    }
}
