package com.ybmmarket20.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.CommonDialogLayout;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 降价通知
 */
@Router({"depreciateinform", "depreciateinform/:price/:id"})
public class DepreciateInformActivity extends BaseActivity {

    @Bind(R.id.tv_depreciate_hint)
    TextView mTvDepreciateHint;
    @Bind(R.id.tv_present_price)
    TextView mTvPresentPrice;
    @Bind(R.id.tv_ditch_price)
    EditText mTvDitchPrice;

    private String mPresentPrice;
    private String mPrice;
    private String mId;

    @Override
    protected void initData() {

        setTitle("降价通知");
        mPrice = getIntent().getStringExtra("price");
        mId = getIntent().getStringExtra("id");

        init();
    }

    private void init() {
        mPresentPrice = getResources().getString(R.string.text_depreciate_inform);
        mTvPresentPrice.setText(Html.fromHtml(String.format(mPresentPrice, "¥" + mPrice)));

        mTvDitchPrice.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable edt) {
                String temp = edt.toString();
                int posDot = temp.indexOf(".");
                if (posDot <= 0) return;
                if (temp.length() - posDot - 1 > 2) {
                    edt.delete(posDot + 3, posDot + 4);
                }
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_depreciate_inform;
    }

    @OnClick({R.id.btn_confirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_confirm:

                String currentPrice = mPrice;
                String expectPrice = mTvDitchPrice.getText().toString().trim();
                if (TextUtils.isEmpty(expectPrice)) {
                    ToastUtils.showShort("请输入渠道价格");
                    return;
                }
//                if (expectPrice.length() <= 1 && expectPrice.contains("0")) {
//                    ToastUtils.showShort("渠道价格不能为零");
//                    return;
//                }

                if (UiUtils.BigDecimalPriceTo0(expectPrice)) {
                    ToastUtils.showShort("请输入正确的渠道价格");
                    return;
                }

                if (UiUtils.BigDecimalPrice(currentPrice, expectPrice)) {
                    ToastUtils.showShort("渠道价格必须低于当前价格");
                    return;
                }
                int skuId;
                try {
                    skuId = Integer.parseInt(mId);
                } catch (Exception e) {
                    skuId = 0;
                }
                String businessType = "2";
                requestData(businessType, currentPrice, expectPrice, skuId);
                break;
        }
    }

    /**
     * businessType 收藏类型 默认收藏不传，1: 表示有货提醒业务类型；2：降价提醒q
     * currentPrice 关注价
     * expectPrice  期望价
     * merchantId   商品业务相关id
     * skuId        商户id
     * 服务端请求
     */
    private void requestData(String businessType, String currentPrice, String expectPrice, final int skuId) {

        final String collect_str = "订阅成功";

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(skuId));
        params.put("businessType", businessType);
        params.put("currentPrice", currentPrice);
        params.put("expectPrice", expectPrice);

        HttpManager.getInstance().post(AppNetConfig.COLLECT, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {

                if (null != obj) {
                    if (obj.isSuccess()) {
                        DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        setResult(RESULT_OK, getAction(businessType,1+""));
                        finish();
                    }
                }
            }
        });
    }

    public Intent getAction(String businessType,String favoriteStatus) {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putString("businessType", businessType);
        bundle.putString("favoriteStatus", favoriteStatus);
        intent.putExtras(bundle);
        return intent;
    }
}
