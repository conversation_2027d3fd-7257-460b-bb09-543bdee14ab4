package com.ybmmarket20.activity

import com.ybmmarket20.common.LicenseStatusActivity
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport
import com.ybmmarket20.xyyreport.paramsInfo.IRowsBeanInfo

abstract class ProductDetailAnalysisActivity: LicenseStatusActivity() {

    /**
     * 商详-导航-分享按钮点击
     */
    fun trackNavigateShareBtnClick() {
        CommodityDetailReport.trackNavigateShareBtnClick(this)
    }

    @JvmName("trackBottomShareWechatForImageClick")
    fun trackBottomShareWechatForImageClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareWechatForImageClick(this, productId)
        return null
    }
    fun trackBottomShareWechatForLinkClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareWechatForLinkClick(this, productId)
        return null
    }
    fun trackBottomShareWechatCommonForLinkClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareWechatCommonForLinkClick(this, productId)
        return null
    }
    fun trackBottomShareWeComForImageClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareWeComForImageClick(this, productId)
        return null
    }
    fun trackBottomShareWeComForLinkClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareWeComForLinkClick(this, productId)
        return null
    }
    fun trackBottomShareCopyLinkClick(productId: String?): Void? {
        CommodityDetailReport.trackBottomShareCopyLinkClick(this, productId)
        return null
    }

}