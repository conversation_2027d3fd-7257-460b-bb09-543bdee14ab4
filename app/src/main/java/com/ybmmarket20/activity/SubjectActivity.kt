package com.ybmmarket20.activity

import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeSubjectTab
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.fragments.SubjectFragment
import kotlinx.android.synthetic.main.activity_subject.*

/**
 * <AUTHOR>
 * @date 2020-05-12
 * @description 专题页面
 */

@Router("homeSubject/:strategyTypeId/:subjectType", "homeSubject")
class SubjectActivity: BaseActivity() {

    private var strategyTypeId: String? = null
    private var majorTitle: String? = null

    override fun getContentViewId(): Int = R.layout.activity_subject

    override fun initData() {
        strategyTypeId = intent.getStringExtra("strategyTypeId")
        majorTitle = intent.getStringExtra("majorTitle")
        setBackground(majorTitle?: "")
        showProgress()
        getData()
    }

    /**
     * 设置头部背景
     */
    private fun setBackground(title: String) {
        //fixme 不应该用title做判断，o(╥﹏╥)o
        when(title) {
            "每日特惠" -> {
                iv_subject_top.setImageResource(R.drawable.icon_subject_daily_discount_top)
                iv_title.setImageResource(R.drawable.icon_subject_title_daily_discount)
            }
            "新品首推" -> {
                iv_subject_top.setImageResource(R.drawable.icon_subject_new_recommend_top)
                iv_title.setImageResource(R.drawable.icon_subject_title_new_recommend)
            }

            "高毛专区" -> {
                iv_subject_top.setImageResource(R.drawable.icon_subject_gross_top)
                iv_title.setImageResource(R.drawable.icon_subject_title_gross)
            }

            else -> {
                iv_subject_top.setImageResource(R.drawable.icon_subject_gross_top)
                iv_title.setImageResource(R.drawable.icon_subject_title_gross)
                tv_title.text = majorTitle
                tv_title.visibility = View.VISIBLE
                iv_title.visibility = View.GONE
            }
        }
        //设置头部背景尺寸
        val topRat = 375f / 122f
        val screenWidth = ScreenUtils.getScreenWidth(this)
        val topHeight = screenWidth / topRat
        val topLp: ConstraintLayout.LayoutParams = iv_subject_top.layoutParams as ConstraintLayout.LayoutParams
        topLp.height = topHeight.toInt()
        topLp.width = screenWidth
        iv_subject_top.layoutParams = topLp
    }

    /**
     * 获取tab数据
     */
    private fun getData() {
        val params = RequestParams().also {
            it.put("strategyTypeId", strategyTypeId)
        }
        HttpManager.getInstance().post(if (isKaUser)AppNetConfig.KA_HOME_SUBJECT_TABS else AppNetConfig.HOME_SUBJECT_TABS, params, object : BaseResponse<HomeSubjectTab>(){
            override fun onSuccess(content: String?, obj: BaseBean<HomeSubjectTab>?, t: HomeSubjectTab?) {
                super.onSuccess(content, obj, t)
                dismissProgress()
                t?.also {
                    var index = 0
                    val fragmentList = java.util.ArrayList<Fragment>()
                    val titles = arrayOfNulls<String>(size = t.subjectTabList?.size?:0)
                    t.subjectTabList?.forEach {homeSubjectTabItem ->
                        titles[index] = homeSubjectTabItem.name?: ""
                        val fragment = SubjectFragment()
                        fragment.arguments = Bundle().also {
                            it.putString("strategyTypeId", strategyTypeId)
                            it.putString("strategyCategoryId", homeSubjectTabItem.id)
                            it.putString("strategyCategoryType", homeSubjectTabItem.type)
                            it.putString("majorTitle", majorTitle)
                            it.putString("minorTitle", homeSubjectTabItem.name)
                        }
                        fragmentList.add(fragment)
                        index ++
                    }
                    if(titles.isNotEmpty()) {
                        stl_subject.setViewPager(vp_subject, titles, this@SubjectActivity, fragmentList)
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
            }
        })
    }
}