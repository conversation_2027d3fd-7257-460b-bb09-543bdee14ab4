package com.ybmmarket20.activity;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.fragments.BonusPoolsInvitationFragment;
import com.ybmmarket20.fragments.BoundsPoolsRewardFragment;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/*
 * 我的奖励池
 * */
@Router("bonuspools")
public class BonusPoolsActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.ps_tab)
    SlidingTabLayout psTab;
    @Bind(R.id.vp_client)
    ViewPager vpClient;

    private List<Fragment> list_fragment;
    private List<String> list_title;

    BonusPoolsInvitationFragment bonusPoolsInvitationFragment;
    BoundsPoolsRewardFragment boundsPoolsRewardFragment;

    private int tabs = 0;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_bonus_pools;
    }

    @Override
    protected void initData() {

        setTitle("我的奖励池");
        initFragment();

        BonusPoolsAdapter adapter = new BonusPoolsAdapter(getSupportFragmentManager(), list_fragment, list_title);
        vpClient.setAdapter(adapter);
        vpClient.setOffscreenPageLimit(list_title.size() + 1);
//        psTab.setupWithViewPager(vpClient);
        psTab.setViewPager(vpClient);
        vpClient.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                tabs = position;
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }

    private void initFragment() {

        list_title = new ArrayList<>();
        list_fragment = new ArrayList<>();

        list_title.add("邀请成功");
        bonusPoolsInvitationFragment = BonusPoolsInvitationFragment.getInstance(0);
        list_fragment.add(bonusPoolsInvitationFragment);

        list_title.add("获得奖励");
        boundsPoolsRewardFragment = BoundsPoolsRewardFragment.getInstance(1);
        list_fragment.add(boundsPoolsRewardFragment);
    }

    private class BonusPoolsAdapter extends FragmentPagerAdapter {
        private List<Fragment> listFragment;                         //fragment列表
        private List<String> listTitle;                              //tab名的列表

        public BonusPoolsAdapter(FragmentManager fm, List<Fragment> list_fragment, List<String> list_Title) {
            super(fm);
            this.listFragment = list_fragment;
            this.listTitle = list_Title;
        }

        @Override
        public Fragment getItem(int position) {
            if (listFragment == null || listFragment.isEmpty() || listFragment.size() <= position || listFragment.get(position) == null) {
                if (listFragment == null) {
                    listFragment = new ArrayList<>();
                }
            }
            return listFragment.get(position);
        }

        @Override
        public int getCount() {
            return listTitle.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return listTitle.get(position);
        }
    }

    @Override
    public String getPageName() {
        if (tabs == 0) {
            return XyyIoUtil.PAGE_MEINVITEDGIFTREWARDPOOLINVITATIONSUCCESSFUL;
        } else {
            return XyyIoUtil.PAGE_MEINVITEDGIFTREWARDPOOLREWARDS;
        }
    }

}
