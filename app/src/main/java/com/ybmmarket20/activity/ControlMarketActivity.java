package com.ybmmarket20.activity;

import android.content.Intent;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 控销专区
 * Created by asus on 2016/11/22.
 */
@Router("controlmarketactivity")
public class ControlMarketActivity extends BaseProductActivity {

    @Bind(R.id.lv)
    CommonRecyclerView lv;

    private List<RowsBean> controlPage;
    private List<RowsBean> rowsList = new ArrayList<>();

    private int pageSize = 10;
    private int pager_1 = 0;
    private String controlFlag = "0";

    @Override
    protected String getRawAction() {
        return "ybmpage://controlmarketactivity/";
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_control_market;
    }

    @Override
    protected void initData() {
        super.initData();
        setTitle("我的控销");
        detailAdapter = new GoodsListAdapter(R.layout.item_goods, rowsList, false, false);
        detailAdapter.setEmptyView(this,R.layout.layout_empty_view, R.drawable.icon_empty, "这里没有可采购的控销商品\n我们去看看别的宝贝吧!");
        detailAdapter.openLoadMore(pageSize, true);
        detailAdapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                if (rows != null) {
                    Intent intent = new Intent(ControlMarketActivity.this, ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    startActivity(intent);
                }
            }
        });
        lv.setListener(new CommonRecyclerView.Listener() {

            @Override
            public void onRefresh() {
                getControlResult(0);
            }

            @Override
            public void onLoadMore() {
                getControlResult(pager_1);
            }
        });
        lv.setEnabled(true);
        lv.setAdapter(detailAdapter);
    }

    /**
     * 控销列表
     *
     * @param pager 分页
     *              controlFlag 全部控销或者我的控销
     *              url => "controlSales/fetchData"
     */
    private void getControlResult(final int pager) {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(10));
        params.put("offset", String.valueOf(0));
        if (pager >= 1) {
            params.put("offset", String.valueOf(pager));
        }
        params.put("controlFlag", controlFlag);

        HttpManager.getInstance().post(AppNetConfig.CONTROL_SALES, params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> obj, RowsListBean bean) {
                if (lv == null) {
                    return;
                }
                completion();
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (bean != null && bean.rows != null) {
                            if (pager < 1) {//下拉刷新

                                pager_1 = 1;

                                controlPage = bean.rows;
                                if (controlPage != null) {
                                    diffData(controlPage, rowsList);
                                }
                                detailAdapter.setNewData(rowsList);
                                if (controlPage != null && controlPage != null && controlPage.size() < 10) {
                                    detailAdapter.notifyDataChangedAfterLoadMore(true);
                                }
                            } else {

                                controlPage = bean.rows;
                                if (controlPage != null) {

                                    int size_2 = controlPage.size();

                                    for (RowsBean b : controlPage) {
                                        if (rowsList.contains(b)) {
                                            rowsList.remove(b);
                                        }
                                    }

                                    rowsList.addAll(controlPage);

                                    if (size_2 >= pageSize) {
                                        pager_1++;
                                    }

                                    detailAdapter.notifyDataChangedAfterLoadMore(size_2 >= pageSize);
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (lv == null) {
                    return;
                }
                completion();
            }
        });
    }

    private void diffData(List<RowsBean> news, List<RowsBean> old) {
        if (news == null || news.size() <= 0) {
            return;
        }

        if (old == null) {
            old = new ArrayList<>();
            old.addAll(news);
            return;
        }

        for (RowsBean controlBean : news) {
            if (old.contains(controlBean)) {
                old.remove(controlBean);
            }
        }
        old.addAll(0, news);
    }

    private void completion() {
        if (lv != null) {
            lv.setRefreshing(false);
        }
    }

    /**
     * 如果是当前用户
     */
    public void refreshList() {
        if (lv != null) {
            lv.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (detailAdapter != null && detailAdapter.getData() != null && !detailAdapter.getData().isEmpty()) {
                        detailAdapter.notifyDataSetChanged();
                    }
                }
            }, 500);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshList();
    }
}
