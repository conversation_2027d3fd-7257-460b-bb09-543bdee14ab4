package com.ybmmarket20.activity

import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.InvoiceEditHistoryBean
import com.ybmmarket20.common.vm.BaseVMActivity
import com.ybmmarket20.databinding.ActivityInvoiceInfoBinding
import com.ybmmarket20.viewmodel.EditInvoiceViewModel

/**
 *    author : hcq
 *    date   : 2025/9/18 15:40
 *    desc   : 发票信息页面
 */
@Router("InvoiceInfoActivity")
class InvoiceInfoActivity :
    BaseVMActivity<ActivityInvoiceInfoBinding, EditInvoiceViewModel>(ActivityInvoiceInfoBinding::inflate) {
    override val viewModel: EditInvoiceViewModel by viewModels()
    override fun initData() {
        setTitle("发票信息")

        // 获取Intent参数
        getIntentParams()

        // 初始化UI
        initViews()

        // 设置观察者
        setupObservers()
    }

    /**
     * 获取Intent参数
     */
    private fun getIntentParams() {

    }

    /**
     * 初始化视图
     */
    private fun initViews() {

    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        val data = getMockData()

        if(data == null){
            showEmptyView();
            return
        }

        // 定义需要处理的属性名列表
        val propertyNames = arrayOf(
            "invoiceType",
            "companyName",
            "taxNumber",
            "address",
            "phone",
            "bankName",
            "bankAccount",
            "editTime",
            "editStatusName"
        )

        // 遍历赋值
        propertyNames.forEach { propertyName ->
            // 获取数据属性值
            val propertyValue = getPropertyValue(data, propertyName)

            if (propertyValue.isNullOrEmpty()) {
                // 数据为空时隐藏对应的LinearLayout
                getLinearLayoutByName(propertyName)?.visibility = View.GONE
            } else {
                // 数据不为空时显示LinearLayout并设置TextView文本
                getLinearLayoutByName(propertyName)?.visibility = View.VISIBLE
                getTextViewByName(propertyName)?.text = propertyValue
            }
        }

        vBinding.rvEditHistory.visibility = View.VISIBLE
        vBinding.llEmptyView.visibility = View.GONE
    }

    /**
     * 显示空状态视图
     */
    private fun showEmptyView() {
        vBinding.rvEditHistory.visibility = View.GONE
        vBinding.llEmptyView.visibility = View.VISIBLE
    }

    /**
     * 通过反射获取对象属性值
     */
    private fun getPropertyValue(obj: InvoiceEditHistoryBean, propertyName: String): String? {
        return when (propertyName) {
            "invoiceType" -> obj.invoiceType
            "companyName" -> obj.companyName
            "taxNumber" -> obj.taxNumber
            "address" -> obj.address
            "phone" -> obj.phone
            "bankName" -> obj.bankName
            "bankAccount" -> obj.bankAccount
            "editTime" -> obj.editTime
            "editStatusName" -> obj.editStatusName
            else -> null
        }
    }



    /**
     * 根据属性名获取对应的LinearLayout
     */
    private fun getLinearLayoutByName(propertyName: String): LinearLayout? {
        return when (propertyName) {
            "invoiceType" -> vBinding.invoiceItem.llInvoiceType
            "companyName" -> vBinding.invoiceItem.llCompanyName
            "taxNumber" -> vBinding.invoiceItem.llTaxNumber
            "address" -> vBinding.invoiceItem.llAddress
            "phone" -> vBinding.invoiceItem.llPhone
            "bankName" -> vBinding.invoiceItem.llBankName
            "bankAccount" -> vBinding.invoiceItem.llBankAccount
            "editTime" -> vBinding.invoiceItem.llEditTime
            "editStatusName" -> vBinding.invoiceItem.llStatusDesc
            else -> null
        }
    }

    /**
     * 根据属性名获取对应的TextView
     */
    private fun getTextViewByName(propertyName: String): TextView? {
        return when (propertyName) {
            "invoiceType" -> vBinding.invoiceItem.tvInvoiceType
            "companyName" -> vBinding.invoiceItem.tvCompanyName
            "taxNumber" -> vBinding.invoiceItem.tvTaxNumber
            "address" -> vBinding.invoiceItem.tvAddress
            "phone" -> vBinding.invoiceItem.tvPhone
            "bankName" -> vBinding.invoiceItem.tvBankName
            "bankAccount" -> vBinding.invoiceItem.tvBankAccount
            "editTime" -> vBinding.invoiceItem.tvEditTime
            "editStatusName" -> vBinding.invoiceItem.tvStatusDesc
            else -> null
        }
    }


    fun getMockData(): InvoiceEditHistoryBean {

        return InvoiceEditHistoryBean(
            id = "1",
            editTime = "2025年9月18日 13:30",
            editStatus = 2,
            editStatusName = "审核通过",
            operator = "系统",
            remark = "审核通过",
            invoiceType = "增值税专用发票",
            companyName = "辽宁市西安区永和大药房",
            taxNumber = "91420112MA4KLGHW01",
            address = "武汉市东湖新技术开发区关东大道77号金融港后台服务中心一期A1栋第3层301室（注册地址：武汉市武昌区）",
            phone = "027-********",
            bankName = "中国建设银行武汉白沙洲支行",
            bankAccount = "62043098712004076353"
        )
    }
}