package com.ybmmarket20.activity

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.bean.NetError
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.loadmore.IPage
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.LicenseStatusActivity
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.utils.SpUtil
import java.lang.reflect.Type
import java.util.*

/**
 * 刷新Activity
 * <AUTHOR>
 * 2019-10-25
 */
abstract class RefreshActivity<E, T : IPage<E>> : LicenseStatusActivity(), CommonRecyclerView.Listener {


    private val mStartPage = 1
    var page: Int = getStartPage()
    val size = 10
    val total: Int = 0
    var dataList: MutableList<E> = ArrayList()

    override fun onRefresh() {
        page = getStartPage()
        loadData()
    }

    override fun onLoadMore() {
        page++
        loadData()
    }

    /**
     * 获取初始
     * @return
     */
    protected open fun getStartPage(): Int {
        return mStartPage
    }

    override fun initData() {
        getCommonRecyclerView().apply {
            setShowAutoRefresh(false)
            setLoadMoreEnable(true)
            setRefreshEnable(true)
            isEnabled = true
            setListener(this@RefreshActivity)
            setAdapter(getAdapter(dataList))
            getAdapter(dataList).openLoadMore(size, true)
            if (getEmptyMsg().isEmpty() || getEmptyImg() == -1) {
                getAdapter(dataList).setEmptyView(context, R.layout.layout_empty_view, R.drawable.icon_empty, resources.getString(R.string.no_data))
            } else {
                getAdapter(dataList).setEmptyView(context, R.layout.layout_empty_view, R.drawable.icon_empty, getEmptyMsg())
            }
        }
    }

    fun loadData() {
        takeIf { getStartPage() == page }?.apply { showProgress() }
        val params = getRequestParams()
        params.put("offset", page.toString() + "")
        params.put("limit", size.toString() + "")
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("timestamp", System.currentTimeMillis().toString() + "")
        HttpManager.getInstance().post(getUrl(), params, object : BaseResponse<T>() {
            override fun onSuccess(content: String, obj: BaseBean<T>, t: T?) {
                super.onSuccess(content, obj, t)
                dismissProgress()
                getCommonRecyclerView().setRefreshing(false)
                if (obj.isSuccess && t != null) {
                    if (page == getStartPage()) {
                        dataList.clear()
                    }
                    if (t.rowsList != null) {
                        dataList.addAll(t.rowsList)
                    }
                    if (t.totalPages > 0) {
                        getAdapter(dataList).notifyDataChangedAfterLoadMore(getNoMoreDataCondition(t))
                    } else {
                        getAdapter(dataList).notifyDataChangedAfterLoadMore(t.rowsList != null && t.rowsList.size >= size)
                    }
                    onResponseSuccess(content, obj, t)
                }
            }

            override fun onFailure(error: NetError) {
                dismissProgress()
                super.onFailure(error)
                getCommonRecyclerView().setRefreshing(false)
                getAdapter(dataList).setNewData(dataList)
                if (page != getStartPage()) page--
                onResponseFailure(error)
            }

            override fun json(content: String, type: Type): BaseBean<*> {
                return super.json(content, getType())
            }
        })
    }

    /**
     * 是否没有更多
     * @param t
     * @return
     */
    private fun getNoMoreDataCondition(t: T): Boolean {
        return if (getStartPage() == 1)
            t.curPage < t.totalPages
        else
            t.curPage + 1 < t.totalPages
    }

//    protected fun getParams(): RequestParams? {
//        return null
//    }

    protected abstract fun getRequestParams(): RequestParams

    protected abstract fun getAdapter(rows: List<E>): YBMBaseAdapter<E>

    protected abstract fun getType(): Type

    protected abstract fun getUrl(): String

    protected abstract fun getCommonRecyclerView(): CommonRecyclerView

    protected open fun getEmptyMsg(): String = ""

    protected open fun getEmptyImg(): Int = -1

    protected open fun onResponseSuccess(content: String, obj: BaseBean<T>, t: T) {}

    protected open fun onResponseFailure(error: NetError) {}

    override fun onLicenseStatusEnable(): Boolean = false

    override fun handleLicenseStatusChange(status: Int) {
    }
}