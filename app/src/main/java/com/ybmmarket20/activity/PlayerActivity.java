package com.ybmmarket20.activity;

import android.content.res.Configuration;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.utils.NetUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;

import org.lynxz.zzplayerlibrary.controller.IPlayerImpl;
import org.lynxz.zzplayerlibrary.util.NetworkUtil;
import org.lynxz.zzplayerlibrary.util.OrientationUtil;
import org.lynxz.zzplayerlibrary.widget.VideoPlayer;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 视频播放-点播全屏竖屏场景
 */
@Router({"playeractivity", "playeractivity/:url"})
public class PlayerActivity extends BaseActivity {

    @Bind(R.id.vp)
    VideoPlayer mVp;
    @Bind(R.id.rl_layout)
    RelativeLayout rlLayout;
    @Bind(R.id.btn_ok)
    Button btnOk;
    @Bind(R.id.tv)
    TextView tv;

    private String mUrl;
    private String mVideoUrl;
    boolean isNetWork = false;

    @Override
    protected void initData() {

        mVp.setTitle("药帮忙");
        mUrl = getIntent().getStringExtra("url");
        if (!TextUtils.isEmpty(mUrl)) {
            mVideoUrl = AppNetConfig.LORD_IMAGESVIDEOS + mUrl;
        } else {
            finish();
            ToastUtils.showShort("请求参数异常");
            return;
        }
        if (NetworkUtil.isNetworkAvailable(this)) {
            if (NetUtil.isWifiConnected(this)) {
                if (mVp != null) {
                    mVp.loadAndStartVideo(PlayerActivity.this, mVideoUrl);
                }
            } else if (NetUtil.isMobileConnected(this)) {
                isNetWork = true;
                rlLayout.setVisibility(View.VISIBLE);
            } else {
                setErrorView();
            }
        } else {
            setErrorView();
        }

        //设置控制栏播放/暂停/全屏/退出全屏按钮图标
        mVp.setIconPlay(R.drawable.play);
        mVp.setIconPause(R.drawable.pause);
        mVp.setIconExpand(R.drawable.expand);
        mVp.setIconShrink(R.drawable.shrink);
        //隐藏/显示控制栏时间值信息
        // mVp.hideTimes();
        // mVp.showTimes();
        // 自定义加载框图标
        mVp.setIconLoading(R.drawable.loading);
        // 设置进度条样式
        mVp.setProgressThumbDrawable(R.drawable.progress_thumb);
        mVp.setProgressLayerDrawables(R.drawable.biz_video_progressbar);//自定义的layer-list
        // mVp.setProgressLayerDrawables(0, 0, R.drawable.shape_progress);//逐层设置,0的话表示保持默认
        //mVp.setControlFlag(VideoPlayer.FLAG_DISABLE_VOLUME_CHANGE);
        // 是否允许滑动调整音量,默认enable
        mVp.setControlFlag(VideoPlayer.FLAG_DISABLE_VOLUME_CHANGE);
        // 是否允许滑动调整亮度,默认enable
        mVp.setControlFlag(VideoPlayer.FLAG_DISABLE_BRIGHTNESS_CHANGE);
        initListener();

    }

    public void setErrorView() {
        isNetWork = true;
        tv.setText(getResources().getString(R.string.zz_player_network_invalid));
        btnOk.setVisibility(View.INVISIBLE);
        rlLayout.setVisibility(View.VISIBLE);
    }

    @OnClick({R.id.btn_ok})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                isNetWork = false;
                rlLayout.setVisibility(View.INVISIBLE);
                if (mVp != null) {
                    mVp.loadAndStartVideo(PlayerActivity.this, mVideoUrl);
                }
                break;
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_player;
    }

    private IPlayerImpl playerImpl = new IPlayerImpl() {

        @Override
        public void onNetWorkError() {
            showToast(null);
        }

        @Override
        public void onBack() {
            // 全屏播放时,单击左上角返回箭头,先回到竖屏状态,再关闭
            // 这里功能最好跟onBackPressed()操作一致
            int orientation = OrientationUtil.getOrientation(PlayerActivity.this);
            if (orientation == OrientationUtil.HORIZONTAL) {
                OrientationUtil.forceOrientation(PlayerActivity.this, OrientationUtil.VERTICAL);
            } else {
                finish();
            }
        }

        @Override
        public void onError() {
            showToast("播放器发生异常");
        }
    };

    private void initListener() {
        if (mVp != null) {
            mVp.setPlayerController(playerImpl);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mVp != null) {
            mVp.updateActivityOrientation();
        }
    }

    private void showToast(String msg) {
        if (TextUtils.isEmpty(msg)) {
            msg = getResources().getString(R.string.zz_player_network_invalid);
        }
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mVp != null && !isNetWork) {
            mVp.onHostResume();

        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mVp != null) {
            mVp.onHostPause();
        }
    }

    @Override
    protected void onDestroy() {
        if (mVp != null && !isNetWork) {
            mVp.onHostDestroy();
        }
        super.onDestroy();
    }
}
