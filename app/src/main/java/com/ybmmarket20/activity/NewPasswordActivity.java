package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.util.constant.RegexConstants;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.RegexUtil;
import com.ybmmarket20.view.ButtonObserver;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 设置密码
 */
@Router("newpassword")
public class NewPasswordActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView mIvBack;
    @Bind(R.id.tv_title)
    TextView mTvTitle;
    @Bind(R.id.tv_right)
    TextView mTvRight;
    @Bind(R.id.iv_right)
    ImageView mIvRight;
    @Bind(R.id.ll_title)
    RelativeLayout mLlTitle;
    @Bind(R.id.new_forget_password_btn)
    ButtonObserver mNewForgetPasswordBtn;
    @Bind(R.id.et_new_forget_password)
    EditText mEtNewForgetPassword;
    @Bind(R.id.et_again_new_forget_password)
    EditText mEtAgainNewForgetPassword;

    private String phone;
    private String verificationCode;

    @Override
    protected void initData() {
        setTitle("设置密码");
        phone = getIntent().getStringExtra(IntentCanst.ACTION_PHONE);
        verificationCode = getIntent().getStringExtra(IntentCanst.ACTION_VERIFICATIONCODE);

        mEtAgainNewForgetPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String et = mEtAgainNewForgetPassword.getText().toString();
                mNewForgetPasswordBtn.setEnabled(!TextUtils.isEmpty(et));
            }
        });
    }

    @OnClick({R.id.new_forget_password_btn})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.new_forget_password_btn:
                hideSoftInput();
                String new_password = mEtNewForgetPassword.getText().toString().trim();
                String affirm_password = mEtAgainNewForgetPassword.getText().toString().trim();

                if (TextUtils.isEmpty(new_password) || TextUtils.isEmpty(affirm_password)) {

                    ToastUtils.showShort("密码不能为空");
                    return;
                }
                //密码长度大于8位
                if (new_password.trim().length() < 8) {
                    ToastUtils.showShort(R.string.validate_pwd_error);
                    return;
                }
                if (!new_password.equals(affirm_password)) {
                    ToastUtils.showShort("请确保两次输入的密码一致");
                    return;
                }
                //校验新密码规则是否正确
                if (!TextUtils.isEmpty(new_password) && !RegexUtil.Companion.getInstance().match(new_password, RegexConstants.REGEX_PWD_SERVER)) {
                    ToastUtils.showShort(R.string.validate_pwd_rule);
                    return;
                }



                if (!TextUtils.isEmpty(phone)) {

                    postNewPasswordResponse(new_password, phone,verificationCode);
                } else {
                    ToastUtils.showShort("数据异常，请重新修改密码!");
                }

                break;
        }
    }

    /**
     * 新密码请求方法
     *  @param new_password 新密码
     * @param phone        电话
     * @param verificationCode
     */
    private void postNewPasswordResponse(String new_password, String phone, String verificationCode) {
        showProgress();
        mNewForgetPasswordBtn.setEnabled(false);

        RequestParams params = new RequestParams();
        params.put("mobileNumber", phone);
        params.put("newPassword", new_password);
        params.put("code", verificationCode);
        HttpManager.getInstance().post(AppNetConfig.PASSWORD_AFFIRM, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                mNewForgetPasswordBtn.setEnabled(true);
                dismissProgress();
                if (obj != null) {
                    if (obj.isSuccess()) {
                        loginOut();
                        ToastUtils.showShort("密码修改成功，请重新登录");
                        Intent intent = new Intent(NewPasswordActivity.this, LoginActivity.class);
                        startActivity(intent);
                        finish();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                mNewForgetPasswordBtn.setEnabled(true);
                dismissProgress();
            }
        });
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_new_password;
    }

}
