package com.ybmmarket20.activity;

import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.ReplenishProductBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 手工添加补货单商品
 */
@Router({"replenishproduct", "replenishproduct/:hope_record/:plan_id/:plan_name"})
public class ReplenishProductActivity extends BaseActivity {

    public static final String INTENT_PRODUCT = "intent_product";
    public static final String INTENT_SEARCH_KEY = "search_key";
    public static final int REQUEST_CODE = 100;
    public static final int REQUEST_MODIFY = 101;

    @Bind(R.id.tv_failed_tip)
    TextView tvFailedTip;
    @Bind(R.id.tv_product_name)
    EditText tvProductName;
    @Bind(R.id.tv_product_spec)
    EditText tvProductSpec;
    @Bind(R.id.tv_manufacturer)
    EditText tvManufacturer;
    @Bind(R.id.et_product_num)
    EditText etProductNum;
    @Bind(R.id.et_product_price)
    EditText etProductPrice;
    private String mPlanId, mPlanName;
    //从搜索页返回过来的商品信息
    private String mProductName, mProductCode;
    //商品的名称是否编辑过,是否来自搜索页
    private boolean mHasEdit, mFromSearch;
    //点击的是那个EditText（厂家，规格）
    private int mClickTab;

    @Override
    protected void initData() {
        mPlanId = getIntent().getStringExtra("plan_id");
        mPlanName = getIntent().getStringExtra("plan_name");
        String fromRecord = getIntent().getStringExtra("hope_record");
        boolean hideTip = !TextUtils.isEmpty(fromRecord) && fromRecord.equals("1");
        tvFailedTip.setVisibility(hideTip ? View.GONE : View.VISIBLE);

        setTitle(TextUtils.isEmpty(mPlanName) ? "补货登记" : mPlanName);
        tvProductName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (mFromSearch && s != null && !s.toString().equals(mProductName)) {
                    mHasEdit = true;
                    changeEditTextClickable(tvManufacturer, false);
                    changeEditTextClickable(tvProductSpec, false);
                }
            }
        });
        etProductPrice.addTextChangedListener(new TextWatcher() {
            public void afterTextChanged(Editable edt) {
                String temp = edt.toString();
                int posDot = temp.indexOf(".");
                if (posDot <= 0) return;
                if (temp.length() - posDot - 1 > 2) {
                    edt.delete(posDot + 3, posDot + 4);
                }
            }

            public void beforeTextChanged(CharSequence arg0, int arg1, int arg2, int arg3) {
            }

            public void onTextChanged(CharSequence arg0, int arg1, int arg2, int arg3) {
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_replenish_product;
    }

    @OnClick({R.id.tv_search, R.id.tv_product_spec, R.id.tv_manufacturer, R.id.tv_add_plan})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_search:
                searchProduct(tvProductName.getText().toString());
                break;
            case R.id.tv_product_spec:
                if (!mHasEdit && mFromSearch) {
                    hideSoftInput();
                    mClickTab = 1;
                    gotoSpecManufacturer(SpecManufacturerActivity.FROM_SPEC, tvProductName.getText().toString(),
                            tvManufacturer.getText().toString(), tvProductSpec.getText().toString());
                }
                break;
            case R.id.tv_manufacturer:
                if (!mHasEdit && mFromSearch) {
                    hideSoftInput();
                    mClickTab = 2;
                    gotoSpecManufacturer(SpecManufacturerActivity.FROM_MANU, tvProductName.getText().toString(),
                            tvManufacturer.getText().toString(), tvProductSpec.getText().toString());
                }
                break;
            case R.id.tv_add_plan:
                addReplenishProduct(mPlanId);
                break;
        }
    }

    private void gotoSpecManufacturer(int from, String name, String manu, String spec) {
        Intent intent = new Intent(this, SpecManufacturerActivity.class);
        intent.putExtra(SpecManufacturerActivity.INTENT_FROM, from);
        intent.putExtra(SpecManufacturerActivity.INTENT_NAME, name);
        intent.putExtra(SpecManufacturerActivity.INTENT_MANU, manu);
        intent.putExtra(SpecManufacturerActivity.INTENT_SPEC, spec);
        intent.putExtra(SpecManufacturerActivity.INTENT_TYPE, 1);//1.表示全部
        startActivityForResult(intent, REQUEST_MODIFY);
    }

    private void searchProduct(String productName) {
        if (TextUtils.isEmpty(productName)) {
            ToastUtils.showShort("请输入产品名称");
            return;
        }
        Intent intent = new Intent(this, ReplenishProductSearchActivity.class);
        intent.putExtra(INTENT_SEARCH_KEY, productName);
        startActivityForResult(intent, REQUEST_CODE);
    }

    /**
     * 添加商品到计划单
     */
    private void addReplenishProduct(String planningId) {
        if (TextUtils.isEmpty(planningId)) {
            return;
        }
        String productNameHint = "请输入产品名称";
        String productName = tvProductName.getText().toString().trim();
        String spec = tvProductSpec.getText().toString().trim();
        String manufacturer = tvManufacturer.getText().toString().trim();
        String purchaseNumber = etProductNum.getText().toString().trim();
        String price = etProductPrice.getText().toString().trim();

        if (TextUtils.isEmpty(productName)) {
            ToastUtils.showShort(productNameHint);
            return;
        }
        if (TextUtils.isEmpty(spec)) {
            spec = "暂无";
        }
        if (TextUtils.isEmpty(manufacturer)) {
            manufacturer = "暂无";
        }
        if (TextUtils.isEmpty(purchaseNumber) || Integer.parseInt(purchaseNumber) < 1) {
            purchaseNumber = "1";
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("manufacturer", manufacturer);
        params.put("productName", productName);
        params.put("spec", spec);
        params.put("purchaseNumber", purchaseNumber);
        params.put("price", price);
        params.put("planningScheduleId", planningId);
        if (!TextUtils.isEmpty(mProductCode) && mFromSearch && !mHasEdit) {
            params.put("code", mProductCode);
        }

        final String buyNum = purchaseNumber;
        HttpManager.getInstance().post(AppNetConfig.REPLENISHMENT_ADD_PRODUCTTOPLAN, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                ToastUtils.showShort("添加成功");
                if (!isFinishing()) {
                    Intent intent = new Intent();
                    intent.putExtra(IntentCanst.REPLENISHMENTPROGRAM_NUMBER, buyNum);
                    setResult(RESULT_OK, intent);
                    finish();
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (!TextUtils.isEmpty(error.message)) {
                    ToastUtils.showShort(error.message);
                }
            }
        });
    }

    private void changeEditTextClickable(EditText et, boolean clickable) {
        if (et == null) {
            return;
        }
        et.setFocusable(!clickable);
        et.setFocusableInTouchMode(!clickable);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            if (requestCode == REQUEST_CODE) {
                mProductName = mProductCode = "";
                ReplenishProductBean.ProductBean bean = (ReplenishProductBean.ProductBean) data.getSerializableExtra(INTENT_PRODUCT);
                if (bean != null && tvProductName != null) {
                    mFromSearch = true;
                    mProductName = bean.productName;
                    mProductCode = bean.code;
                    changeEditTextClickable(tvManufacturer, true);
                    changeEditTextClickable(tvProductSpec, true);
                    tvProductName.setText(bean.productName);
                    tvManufacturer.setText(bean.manufacturer);
                    tvProductSpec.setText(bean.spec);
                    etProductNum.requestFocus();
                }
            } else if (requestCode == REQUEST_MODIFY) {
                String text = data.getStringExtra("select");
                //筛选页选了“全部”，之前的值不变
                if (TextUtils.isEmpty(text)) {
                    return;
                }
                if (tvManufacturer != null && tvProductSpec != null) {
                    if (mClickTab == 1) {
                        tvProductSpec.setText(text);
                    } else if (mClickTab == 2) {
                        tvManufacturer.setText(text);
                    }
                }
            }

        }
    }
}
