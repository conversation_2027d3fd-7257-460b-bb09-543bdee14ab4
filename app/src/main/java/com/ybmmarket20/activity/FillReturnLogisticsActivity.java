package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Html;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RefundOrderStatusBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.common.widget.RoundEditText;
import com.ybmmarket20.common.widget.RoundRelativeLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.fragments.AddImage3Fragment;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ButtonObserver;
import com.ybmmarket20.view.MyImageSpan;
import com.zxing.activity.CaptureActivity;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/*
 *填写退货物流信息
 * */
@Router({"fillreturnlogistics"})
public class FillReturnLogisticsActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView ivBack;
    @Bind(R.id.tv_title)
    TextView tvTitle;
    @Bind(R.id.tv_right)
    TextView tvRight;
    @Bind(R.id.iv_right)
    ImageView ivRight;
    @Bind(R.id.ll_title)
    RelativeLayout llTitle;
    @Bind(R.id.tv_hint)
    TextView tvHint;
    @Bind(R.id.tv_amount)
    TextView tvAmount;
    @Bind(R.id.tv_balance)
    TextView tvBalance;
    @Bind(R.id.iv_code)
    ImageView ivCode;
    @Bind(R.id.fragment)
    RoundRelativeLayout fragment;
    @Bind(R.id.btn_ok)
    ButtonObserver btnOk;
    @Bind(R.id.et_express_name)
    RoundEditText etExpressName;
    @Bind(R.id.et_express_no)
    RoundEditText etExpressNo;

    private String refundOrderId;
    private String id;

    private String refundFee;
    private String refundBalance;

    private String expressName;
    private String expressNo;
    private String expressEvidence;

    protected AddImage3Fragment imageFragment;
    protected Bundle arg;

    int REQUESTCODE = 1001;
    private static final int WHAT_CAPTURE_REFUND = 10;


    @Override
    protected int getContentViewId() {
        return R.layout.activity_fill_return_logistics;
    }

    public static void getInstance(Context from, String refundOrderId, String id, String refundFee, String refundBalance, String expressName, String expressNo, String expressEvidence) {
        Intent intent = new Intent(from, FillReturnLogisticsActivity.class);
        intent.putExtra("refundOrderId", refundOrderId);
        intent.putExtra("id", id);
        intent.putExtra("refundFee", refundFee);
        intent.putExtra("refundBalance", refundBalance);
        intent.putExtra("expressName", expressName);
        intent.putExtra("expressNo", expressNo);
        intent.putExtra("expressEvidence", expressEvidence);
        from.startActivity(intent);
    }

    @Override
    protected void initData() {

        setTitle("编辑退货物流信息");
        refundOrderId = getIntent().getStringExtra("refundOrderId");

        id = getIntent().getStringExtra("id");

        refundFee = getIntent().getStringExtra("refundFee");
        refundBalance = getIntent().getStringExtra("refundBalance");

        expressName = getIntent().getStringExtra("expressName");
        expressNo = getIntent().getStringExtra("expressNo");
        expressEvidence = getIntent().getStringExtra("expressEvidence");

        if (TextUtils.isEmpty(expressName) || TextUtils.isEmpty(expressNo) || expressName.equals("null") || expressNo.equals("null")) {
            setTitle("填写退货物流信息");
        }

        if (TextUtils.isEmpty(refundOrderId)) {
            ToastUtils.showShort("参数错误");
            finish();
            return;
        }

        btnOk.observer(etExpressName, etExpressNo);
        btnOk.setOnItemClickListener(new ButtonObserver.OnButtonObserverListener() {
            @Override
            public void onButtonObserver(boolean isFlag) {
                if (isFlag) {
                    setButtonStyle(R.drawable.bg_image_apply_for_convoy_btn, UiUtils.getColor(R.color.white));
                } else {
                    setButtonStyle(R.drawable.bg_image_apply_for_convoy_btn_2, UiUtils.getColor(R.color.white));
                }
            }
        });

        if (!TextUtils.isEmpty(refundFee)) {
            tvAmount.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_money), "¥" + refundFee)));
        }
        if (!TextUtils.isEmpty(refundBalance)) {
            tvBalance.setText(Html.fromHtml(String.format(getResources().getString(R.string.text_refund_balance), "¥" + refundBalance)));
        }

        if (expressName != null && !TextUtils.isEmpty(expressName) && !expressName.equals("null")) {
            etExpressName.setText(expressName);
        }
        if (expressNo != null && !TextUtils.isEmpty(expressNo) && !expressNo.equals("null")) {
            etExpressNo.setText(expressNo);
        }
//        if (expressEvidence != null && !TextUtils.isEmpty(expressEvidence) && !expressEvidence.equals("null")) {

        //运单截图
        ArrayList list = new ArrayList();
        if (!TextUtils.isEmpty(expressEvidence) && !expressEvidence.equals("null") && expressEvidence.length() > 2) {
            list.add(expressEvidence);
        }

        imageFragment = new AddImage3Fragment();
        arg = AddImage3Fragment.getBundle2Me(1, true, false, true);
        arg.putBoolean("allowe_add", true);
        arg.putCharSequence("hint", "");
        arg.putStringArrayList(AddImage3Fragment.EXTRA_DATA, list);
        imageFragment.setArguments(arg);
        getSupportFragmentManager().beginTransaction().replace(R.id.fragment, imageFragment).commit();

        SpannableStringBuilder shopName = getName(getResources().getString(R.string.fillreturnlogistics_hint), R.drawable.icon_refund_optimize_hint);
        if (!TextUtils.isEmpty(shopName)) tvHint.setText(shopName);

    }

    /**
     * 设置按钮风格
     *
     * @param drawableStyle 背景样式
     * @param colorStyle    文字颜色
     */
    public void setButtonStyle(int drawableStyle, int colorStyle) {
        if (btnOk == null) {
            return;
        }
        btnOk.setBackgroundResource(drawableStyle);
        btnOk.setTextColor(colorStyle);
    }

    private SpannableStringBuilder getName(String shopName, Integer icons) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        Drawable drawable = getResources().getDrawable(icons);
        drawable.setBounds(0, 0, ConvertUtils.dp2px(15), ConvertUtils.dp2px(15));
        MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
        //占个位置
        spannableString.insert(0, "-");
        spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return spannableString;
    }

    @OnClick({R.id.btn_ok, R.id.iv_code})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                requestData();
                break;
            case R.id.iv_code:
                toUpdateLogistics();
                break;
        }
    }

    private void requestData() {

        if (btnOk == null) {
            return;
        }
        String etExpressNameStr = etExpressName.getText().toString().trim();
        if (TextUtils.isEmpty(etExpressNameStr)) {
            UiUtils.toast("快递名称不能为空");
            return;
        }
        if (etExpressNameStr.length() > 10) {
            UiUtils.toast("快递名称限10个字，请检查");
            return;
        }
        String etExpressNoStr = etExpressNo.getText().toString().trim();
        if (TextUtils.isEmpty(etExpressNoStr)) {
            UiUtils.toast("快递单号不能为空");
            return;
        }
//        if (!UiUtils.isExpressNO(etExpressNoStr)) {
//            UiUtils.toast("请输入正确的快递单号");
//            return;
//        }

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("orderRefundId", refundOrderId);
        params.put("id", id);
        params.put("expressName", etExpressNameStr);
        params.put("expressNo", etExpressNoStr);

        if (imageFragment.confirm()) {
            btnOk.setEnabled(false);
            List<String> list = imageFragment.getFileNameList();
            if (list == null) {
                list = new ArrayList<>();
            }
            String imageStr = getImageUrl(list);
            expressEvidence = imageStr;
        }

//        if (!TextUtils.isEmpty(expressEvidence)) {
        params.put("expressEvidence", expressEvidence);
//        }
        HttpManager.getInstance().post(AppNetConfig.SAVE_OR_UPDATE_ORDER_REFUND_EXPRESS, params, new BaseResponse<RefundOrderStatusBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RefundOrderStatusBean> obj, RefundOrderStatusBean data) {

                if (btnOk == null) {
                    return;
                }
                btnOk.setEnabled(true);

                if (obj != null && obj.isSuccess()) {

                    if (data != null && data.checkExpressState != 0) {
                        if (!TextUtils.isEmpty(obj.msg)) {
                            ToastUtils.showShort(obj.msg);
                        }
                        finish();
                    } else {
                        showHintDialog(new AlertDialogEx.OnClickListener() {
                            @Override
                            public void onClick(AlertDialogEx dialog, int button) {
                                Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + RoutersUtils.kefuPhone));
                                BaseYBMApp.getApp().getCurrActivity().startActivity(intent);
                            }
                        }, "当前退款单退货仓库已入库，如需提供退货物流信息或有其他问题请联系客服人员!");
                    }

                }
            }

            @Override
            public void onFailure(NetError error) {
                btnOk.setEnabled(true);
                super.onFailure(error);
            }
        });

    }

    public String getImageUrl(List<String> list) {

        StringBuffer imagUrl = new StringBuffer();
        for (String s : list) {
            imagUrl.append(s);
            imagUrl.append(";");
        }
        if (imagUrl.length() > 0) {
            imagUrl.deleteCharAt(imagUrl.length() - 1);
        }

        return imagUrl.toString();
    }

    private void showHintDialog(AlertDialogEx.OnClickListener listener, String title) {
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setMessage(title).setCancelButton("我知道了", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
                finish();
            }
        }).setCancelable(false).setConfirmButton("联系客服", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }

    private void toUpdateLogistics() {
        if (btnOk == null) {
            return;
        }
        Intent intent = new Intent(FillReturnLogisticsActivity.this, CaptureActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString(IntentCanst.CAPTURE_REFUND, "capture_refund");
        intent.putExtras(bundle);
        startActivityForResult(intent, REQUESTCODE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUESTCODE && resultCode == RESULT_OK) {
            String mCode = data.getStringExtra("mCode");
            Handler.sendMessage(Handler.obtainMessage(WHAT_CAPTURE_REFUND, mCode));
        }
    }

    /****
     * 刷新数据
     * **/
    @SuppressLint("HandlerLeak")
    public Handler Handler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (etExpressNo == null) {
                return;
            }
            if (msg.what == WHAT_CAPTURE_REFUND) {
                String mCode = (String) msg.obj;
                if (!TextUtils.isEmpty(mCode)) {
                    etExpressNo.setText(mCode);
                    etExpressNo.setSelection(mCode.length());
                }
            }

        }
    };


}
