package com.ybmmarket20.activity

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.tbruyelle.rxpermissions2.RxPermissions
import com.ybm.app.bean.NetError
import com.ybm.app.utils.PermissionDialogUtil
import com.ybmmarket20.R
import com.ybmmarket20.adapter.LicensePicListAdapter
import com.ybmmarket20.adapter.RejectRefundAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.ShowAptitudeBottomAddImageDialog
import com.ybmmarket20.view.WrapContentLinearLayoutManager
import com.ybmmarket20.viewmodel.RejectRefundViewModel
import kotlinx.android.synthetic.main.activity_reject_refund.*
import java.io.File
import java.util.*

/**
 * 拒绝退款
 */
@Router("rejectrefund")
class RejectRefundActivity: BaseActivity(), LicensePicListAdapter.Listener {

    private val mViewModel: RejectRefundViewModel by viewModels()
    private var isSmallPayment: Boolean = false

    override fun getContentViewId(): Int = R.layout.activity_reject_refund

    override fun initData() {
        val refundId = intent.getStringExtra("refundId")
        isSmallPayment = TextUtils.equals(intent.getStringExtra("isSmallPayment"), "1")
        if(isSmallPayment) {
            setTitle("拒绝小额赔偿")
            rvRejectRefundReasonTitle.text = "拒绝小额赔偿原因"
        } else {
            setTitle("拒绝退款")
        }
        showProgress()
        initObserver()
        mViewModel.getRejectRefundReason()
        rtvRejectRefundSubmit.setOnClickListener {
            val adapter = rvRejectRefundReason.adapter as? RejectRefundAdapter
            showProgress()
            val rejectReason = adapter?.rejectReason ?: ""
            mViewModel.auditOrderRefund("2", refundId?: "", rejectReason,  getImages(), etRejectRefundExplain.text.toString())
        }
        setUploadImages()
    }

    private fun getImages(): String {
        return currentPicListAdapter?.data?.map {
            (it as LicensePicListAdapter.ImageInfo).newPath
        }?.filter {
            it != null && it.startsWith("http")
        }?.joinToString(",")?: ""
    }

    private fun initObserver() {
        mViewModel.rejectRefundLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                rvRejectRefundReason.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
                val adapter = RejectRefundAdapter(it.data)
                rvRejectRefundReason.adapter = adapter
                adapter.setOnClickCallback {
                    rtvRejectRefundSubmit.setBackgroundColor(Color.parseColor("#00b377"))
                    rtvRejectRefundSubmit.isEnabled = true
                }
            }
        }

        mViewModel.auditOrderRefundLiveData.observe(this) {
            dismissProgress()
            if (!it.isSuccess) return@observe
            setResult(100)
            finish()
        }
    }

    private fun setUploadImages() {
        val linearLayoutManager = WrapContentLinearLayoutManager(this)
        linearLayoutManager.orientation = WrapContentLinearLayoutManager.HORIZONTAL
        rvRejectRefundUpload.layoutManager = linearLayoutManager
        val imageUrlList: MutableList<LicensePicListAdapter.ImageInfo> = mutableListOf()
        val imageInfo = LicensePicListAdapter.ImageInfo()
        imageInfo.localPath = LicensePicListAdapter.EDIT_FLAG
        imageUrlList.add(imageInfo)
        val adapter = LicensePicListAdapter(R.layout.item_image, imageUrlList, true, linearLayoutManager)
        adapter.setItemStatus(0)
        adapter.setMaxSize(3)
        adapter.setListener(this)
        adapter.setEditFlagDrawable(R.drawable.icon_reject_refund_upload_button)
        rvRejectRefundUpload.adapter = adapter
    }

    override fun addImage(adapter: LicensePicListAdapter?,typeStr: String?) {
        val rxPermissions = RxPermissions(this)
        if (rxPermissions.isGranted(Manifest.permission.READ_EXTERNAL_STORAGE)
            && rxPermissions.isGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            && rxPermissions.isGranted(Manifest.permission.CAMERA)
        ) {
            getRootPermissions(adapter)
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(
                this,
                "药帮忙App需要申请存储权限和相机权限，用于拍照并存储照片"
            ) { getRootPermissions(adapter) }
        }
    }

    private var currentPicListAdapter: LicensePicListAdapter? = null

    /**
     * 获取6.0读取文件的权限
     */
    @SuppressLint("CheckResult")
    private fun getRootPermissions(adapter: LicensePicListAdapter?) {
        val rxPermissions = RxPermissions(this) // where this is an Activity instance
        rxPermissions.request(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
        ).subscribe({ granted: Boolean ->
            if (granted) { // 在android 6.0之前会默认返回true
                currentPicListAdapter = adapter
                selectPics(adapter)
            } else {
                // 未获取权限
                Toast.makeText(this@RejectRefundActivity, "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG)
                    .show()
            }
        }) { _: Throwable? -> }
    }

    private fun selectPics(adapter: LicensePicListAdapter?) {
        val mDialogLayout = ShowAptitudeBottomAddImageDialog(mySelf)
        mDialogLayout.setOnCancelClickListener { mDialogLayout.dismiss() }
        mDialogLayout.setOnPhotoGalleryClickListener { _: View? ->
            // 调用图库
            PictureSelector.create(mySelf)
                .openGallery(PictureMimeType.ofImage())
                .maxSelectNum(adapter?.allowAddSize ?: 3)
                .minSelectNum(1)
                .imageSpanCount(4)
                .compress(true)
                .selectionMode(PictureConfig.MULTIPLE)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            mDialogLayout.dismiss()
        }
        mDialogLayout.setOnTakingPicturesClickListener { v: View? ->

            //调用系统相机程序
            PictureSelector.create(mySelf)
                .openCamera(PictureMimeType.ofImage())
                .maxSelectNum(adapter?.allowAddSize ?: 3)
                .minSelectNum(1)
                .compress(true)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            mDialogLayout.dismiss()
        }
        mDialogLayout.show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) {
            val images = PictureSelector.obtainMultipleResult(data)
            if (images == null || images.isEmpty()) {
                ToastUtils.showShort("未找到图片")
                return
            }
            val selectList = ArrayList<LocalMedia>(3)
            selectList.addAll(images)
            val imageInfos = ArrayList<LicensePicListAdapter.ImageInfo>()
            for (localMedia in selectList) {
                val imageInfo = LicensePicListAdapter.ImageInfo()
                imageInfo.localPath = localMedia.compressPath
                imageInfos.add(imageInfo)
            }
            //currentPicListAdapter 空指针
            try {
                currentPicListAdapter!!.addSelectPic(imageInfos)
                uploadImage(imageInfos)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun uploadImage(imageInfoList: List<LicensePicListAdapter.ImageInfo>) {
        for (info in imageInfoList) {
            val file = File(info.localPath)
            if (!file.exists()) {
                ToastUtils.showShort("图片未找到：" + info.localPath, Toast.LENGTH_LONG)
                //                dismissProgress();
                return
            }
        }
        //        showProgress("资质上传中……");
        showProgress(false)
        val linkedList = LinkedList(imageInfoList)
        uploadCircle(linkedList)
    }

    fun uploadCircle(linkedList: LinkedList<LicensePicListAdapter.ImageInfo>) {
        if (linkedList.isEmpty()) {
            dismissProgress()
            ToastUtils.showShort("上传成功", Toast.LENGTH_LONG)
            return
        }
        val s1 = linkedList.removeFirst()
        val file = File(s1.localPath)
        if (!file.exists()) {
            ToastUtils.showShort("上传文件不存在")
            return
        }
        // showProgress();
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("file", file)
        HttpManager.getInstance().post(AppNetConfig.LICENSE_AUDIT_UPLOADIMG, params, object: BaseResponse<List<String>>(){
            override fun onSuccess(content: String?, obj: BaseBean<List<String>>?, bean: List<String>?) {
                super.onSuccess(content, obj, bean)
                if (!bean.isNullOrEmpty()) {
                    //拼上上传的图片全路径 /ybm/license/**********/334dd569-5716-45d4-999d-46833ac9633c.jpeg
                    s1.newPath = AppNetConfig.getCDNHost() + bean[0]
                    uploadCircle(linkedList)
                } else {
                    dismissProgress()
                    ToastUtils.showShort("上传失败")
                    currentPicListAdapter!!.data.clear()
                    currentPicListAdapter!!.notifyDataSetChanged()
                    currentPicListAdapter!!.showAddView()
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                dismissProgress()
                ToastUtils.showShort("上传失败")
                currentPicListAdapter!!.data.clear()
                currentPicListAdapter!!.notifyDataSetChanged()
                currentPicListAdapter!!.showAddView()
            }
        })
    }

}