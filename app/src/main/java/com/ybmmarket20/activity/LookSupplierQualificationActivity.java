package com.ybmmarket20.activity;

import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.Toast;

import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.SupplierQualificationAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RequestSupplierBean;
import com.ybmmarket20.bean.SupplierQualificationBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * 查看我的供应商资质证书
 */

public class LookSupplierQualificationActivity extends BaseActivity implements RecyclerRefreshLayout.OnRefreshListener, View.OnClickListener, CompoundButton.OnCheckedChangeListener, BaseQuickAdapter.OnItemClickListener {

    public CheckBox cb_all_select;
    private ViewGroup ll_cb_all;
    private CommonRecyclerView rv;
    private SupplierQualificationAdapter qualificationAdapter;
    private List<SupplierQualificationBean> beanList = new ArrayList<>();
    private RequestSupplierBean.SupplierBean supplierBean;

    @Override
    public int getContentViewId() {
        return R.layout.activity_look_supplier_qualification;
    }

    private void findView() {
        cb_all_select = findViewById(R.id.cb_all);
        ll_cb_all = findViewById(R.id.ll_cb_all);
        rv = findViewById(R.id.rv);
    }

    @Override
    protected void initData() {
        findView();

        supplierBean = (RequestSupplierBean.SupplierBean) getIntent().getBundleExtra("bundle").getSerializable("supplierBean");

        setTitle(supplierBean.getCompanyName());
        setRigthText(this, "保存到相册");
        cb_all_select.setOnCheckedChangeListener(this);
        ll_cb_all.setVisibility(View.GONE);

        qualificationAdapter = new SupplierQualificationAdapter(beanList, this);
        qualificationAdapter.setOnItemClickListener(this);
        rv.setAdapter(qualificationAdapter);
        rv.setOnRefreshListener(this);

        showProgress();
        requestData();

    }

    private void requestData() {
        RequestParams params = new RequestParams();
        params.put("orgId", supplierBean.getOrgId());
        HttpManager.getInstance().post(AppNetConfig.GET_SUPPLIER_QUALIFICATION, params, new BaseResponse<List<SupplierQualificationBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<SupplierQualificationBean>> obj, List<SupplierQualificationBean> list) {
                dismissProgress();
                rv.setRefreshing(false);
                if (obj != null && obj.getData() != null && list != null && list.size() > 0) {
                    beanList.clear();
                    beanList.addAll(list);
                    ll_cb_all.setVisibility(View.VISIBLE);
                } else {
                    beanList.clear();
                    qualificationAdapter.setEmptyView(LayoutInflater.from(getApplicationContext()).inflate(R.layout.layout_empty_view, (ViewGroup) rv.getParent(), false));
                    ll_cb_all.setVisibility(View.GONE);
                }
                qualificationAdapter.notifyDataSetChanged();
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
                rv.setRefreshing(false);
            }
        });
    }

    @Override
    public void onRefresh() {
        requestData();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_right://保存相册
                for (int i = 0; i < beanList.size(); i++) {
                    if (beanList.get(i).isChecked()) {
                        downloadTotalCount++;
                    }
                }
                if (downloadTotalCount == 0) {
                    ToastUtils.showShort("请选择要保存的选项");
                    return;
                }
                for (int i = 0; i < beanList.size(); i++) {
                    if (beanList.get(i).isChecked()) {
                        downloadBitmap(beanList.get(i).getUrl());
                    }
                }
                break;
        }
    }


    private static int downloadFinishCount = 0;
    private int downloadTotalCount = 0;

    private synchronized void setDownloadFinish() {
        //计算成功的个数
        downloadFinishCount = downloadFinishCount + 1;
        if (downloadFinishCount == downloadTotalCount) {
            dismissProgress();
            Toast.makeText(YBMAppLike.getAppContext(), "图片已保存到相册", Toast.LENGTH_SHORT).show();
        }
    }


    private void downloadBitmap(String url) {
        ImageHelper.with(YBMAppLike.getAppContext()).load(url).asBitmap().into(new SimpleTarget<Bitmap>() {
            @Override
            public void onResourceReady(Bitmap resource, GlideAnimation<? super Bitmap> glideAnimation) {
                if (resource == null) {
                    dismissProgress();
                    return;
                }
                //保存到相册
                MediaStore.Images.Media.insertImage(getContentResolver(), resource, "title", "description");
                setDownloadFinish();
            }

            @Override
            public void onLoadFailed(Exception e, Drawable errorDrawable) {
                super.onLoadFailed(e, errorDrawable);
                dismissProgress();
                Toast.makeText(YBMAppLike.getAppContext(), "图片下载失败", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (!buttonView.isPressed()) {
            return;
        }
        if (buttonView.getId() == R.id.cb_all) {
            //全选
            if (isChecked) {
                for (int i = 0; i < beanList.size(); i++) {
                    beanList.get(i).setChecked(true);
                    qualificationAdapter.notifyDataSetChanged();
                }
            } else {
                for (int i = 0; i < beanList.size(); i++) {
                    beanList.get(i).setChecked(false);
                    qualificationAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    @Override
    public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {

    }
}

