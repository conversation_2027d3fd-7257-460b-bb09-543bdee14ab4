package com.ybmmarket20.activity;

import android.text.Html;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.util.Linkify;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.InvoiceBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 发票信息
 */
@Router("invoiceinformation")
public class InvoiceInformationActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView mIvBack;
    @Bind(R.id.tv_title)
    TextView mTvTitle;
    @Bind(R.id.tv_right)
    TextView mTvRight;
    @Bind(R.id.iv_right)
    ImageView mIvRight;
    @Bind(R.id.ll_title)
    RelativeLayout mLlTitle;
    @Bind(R.id.tv_title_class)
    TextView mTvTitleClass;
    @Bind(R.id.tv_zzs_title)
    TextView mTvZzsTitle;
    @Bind(R.id.tv_zzs_text)
    TextView mTvZzsText;
    @Bind(R.id.tv_fp_title)
    TextView mTvFpTitle;
    @Bind(R.id.tv_fp_gs)
    TextView mTvFpGs;
    @Bind(R.id.tv_fp_ydmc)
    TextView mTvFpYdmc;
    @Bind(R.id.tv_fp_nsrsbh)
    TextView mTvFpNsrsbh;
    @Bind(R.id.tv_fp_memo)
    TextView mTvFpMemo;

    @Override
    protected void initData() {
        setTitle("发票信息");
        getData();
    }

    private void getData() {

        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        HttpManager.getInstance().post(AppNetConfig.ORDER_INVOICE, params, new BaseResponse<InvoiceBean>() {

            @Override
            public void onSuccess(String content, BaseBean<InvoiceBean> data, InvoiceBean bean) {
                if (data != null && data.isSuccess() && bean !=null) {
                    setData(bean);
                }
            }
        });
    }

    private void setData(InvoiceBean bean) {

        mTvTitleClass.setText(bean.title);
        mTvZzsTitle.setText(bean.zzsTitle);
        mTvZzsText.setText(bean.zzsText);
        mTvFpTitle.setText(bean.fpTitle);
        mTvFpGs.setText(bean.fpGs);
        mTvFpYdmc.setText(bean.fpYdmc + ":" + bean.fpYdmcValue);
        mTvFpNsrsbh.setText(bean.fpNsrsbh + ":" + (!TextUtils.isEmpty(bean.fpNsrsbhValue) ? bean.fpNsrsbhValue : ""));
        mTvFpMemo.setText(Html.fromHtml(bean.fpMemo));
        mTvFpMemo.setAutoLinkMask(Linkify.ALL);
        mTvFpMemo.setMovementMethod(LinkMovementMethod.getInstance());
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_invoice_information;
    }

    @OnClick({R.id.tv_title_class})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_title_class:
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.BILL_REULE);
                //RoutersUtils.open("ybmaction://tel?num=400-0505-111&show=show");

                break;
        }
    }

}
