package com.ybmmarket20.widget

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.text.style.ReplacementSpan

class AlphaBackgroundSpan(
    private val backgroundColor: Int,  // 背景颜色
    private val textColor: Int,        // 文本颜色
    private val backgroundAlpha: Float, // 背景透明度 (0.0f - 1.0f)
    private val textAlpha: Float,      // 文本透明度 (0.0f - 1.0f)
    private val cornerRadius: Float,   // 圆角半径
    private val padding: Float         // 内边距
) : ReplacementSpan() {

    override fun getSize(
        paint: Paint,
        text: CharSequence,
        start: Int,
        end: Int,
        fm: Paint.FontMetricsInt?
    ): Int {
        // 返回文本宽度 + 2 * 内边距
        return (paint.measureText(text, start, end) + 2 * padding).toInt()
    }

    override fun draw(
        canvas: Canvas,
        text: CharSequence,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        // 保存原始颜色和透明度
        val originalColor = paint.color
        val originalAlpha = paint.alpha

        // 设置背景透明度并绘制圆角背景
        paint.color = backgroundColor
        paint.alpha = (255 * backgroundAlpha).toInt() // 设置背景透明度
        val rect = RectF(
            x,
            top.toFloat(),
            x + paint.measureText(text, start, end) + 2 * padding,
            bottom.toFloat()
        )
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, paint)

        // 设置文本透明度并绘制文本
        paint.color = textColor
        paint.alpha = (255 * textAlpha).toInt() // 设置文本透明度
        canvas.drawText(text, start, end, x + padding, y.toFloat(), paint)

        // 恢复原始颜色和透明度
        paint.color = originalColor
        paint.alpha = originalAlpha
    }
}
