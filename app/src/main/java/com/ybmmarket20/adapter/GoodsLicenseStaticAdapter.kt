package com.ybmmarket20.adapter

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.YBMMultiViewAdapter
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.viewmodel.PaymentGoodsViewModel

/**
 * 企业相关资质
 */
class GoodsLicenseStaticAdapter(
    goodsList: MutableList<RefundProductListBean>,
    private val paymentGoodsViewModel: PaymentGoodsViewModel
) : YBMMultiViewAdapter<RefundProductListBean>(R.layout.item_goods_license, goodsList) {

    override fun bindMultiView(holder: YBMBaseHolder, bean: RefundProductListBean) {
        val ivGoods = holder.getView<ImageView>(R.id.ivGoods)
        ImageUtil.load(mContext, "${AppNetConfig.LORD_IMAGE}${bean.imageUrl}", ivGoods)
        holder.setText(R.id.tvProductName, "${bean.productName}/${bean.spec}")

        val llReport = holder.getView<LinearLayout>(R.id.llReport)
        val ivReport = holder.getView<ImageView>(R.id.ivReport)
        val tvReport = holder.getView<TextView>(R.id.tvReport)
        val space = holder.getView<Space>(R.id.space)
        val isReportSelected = paymentGoodsViewModel.isSelectReport(bean.productId)
        llReport.visibility = if (isReportSelected) View.VISIBLE else View.GONE
        //占位处理边距
        space.visibility = if (isReportSelected) View.VISIBLE else View.GONE
        setSelectStatus(ivReport, tvReport)

        val llLicense = holder.getView<LinearLayout>(R.id.llLicense)
        val ivLicense = holder.getView<ImageView>(R.id.ivLicense)
        val tvLicense = holder.getView<TextView>(R.id.tvLicense)
        val isLicenseSelected = paymentGoodsViewModel.isSelectLicense(bean.productId)
        llLicense.visibility = if (isLicenseSelected) View.VISIBLE else View.GONE
        setSelectStatus(ivLicense, tvLicense)
    }

    private fun setSelectStatus(ivStatus: ImageView, textStatus: TextView) {
        ivStatus.setImageResource(R.drawable.icon_license_selected_static)
        textStatus.setTextColor(Color.parseColor("#333333"))
    }
}