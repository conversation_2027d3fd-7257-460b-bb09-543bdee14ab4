package com.ybmmarket20.adapter;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import androidx.core.content.ContextCompat;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.util.TypedValue;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.resource.drawable.GlideDrawable;
import com.bumptech.glide.request.animation.GlideAnimation;
import com.bumptech.glide.request.target.SimpleTarget;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.TextImage;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.MyImageSpan;
import com.ybmmarket20.view.ProductEditLayout;
import com.ybmmarket20.view.PromotionTagView;
import com.ybmmarket20.view.ShowPromotionPopWindow;
import com.ybmmarket20.view.TagView;

import java.util.ArrayList;
import java.util.List;

/*
 * 搜索高毛商品adapter
 * */
public class SearchSkuGrossGoodsListAdapter extends YBMBaseMultiItemAdapter<RowsBean> {

    protected boolean isShop = false;
    protected int currPosition = -1;
    protected int pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
    protected boolean mIsCollect = false;//是否显示收藏按钮
    private boolean isShowSalesQuantity = false;//是否显示销量
    private boolean isCanDelete = false;//是否显示删除

    private boolean isHaveGoods = true;//是否有货
    private boolean isCanBuy = true;//是否控销可以购买
    private boolean isOEMAndNotSigned = false;//是OEM商品并且没有签署协议
    private boolean isShowRemind = false;//是否显示到货提醒

    private OnDeleteItemClickCallback onDeleteItemClickCallback = null;
    private ShowPromotionPopWindow mPopWindowPromotion;
    private boolean isKaUser;

    public SearchSkuGrossGoodsListAdapter(List<RowsBean> data, boolean isBrand, boolean isCollect, boolean isKaUser) {
        super(data);
        this.isKaUser = isKaUser;
        this.mIsCollect = isCollect;
        if (isBrand) {
            pageFrom = ProductEditLayout.FROMPAGE_ALL_PRODUCT;
        } else {
            pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS;
        }
        addItemType(RowsBean.header_41, R.layout.item_goods_search_header_01);
        addItemType(RowsBean.header_42, R.layout.item_goods_search_header_02);
        addItemType(RowsBean.content_43, R.layout.item_goods);
        addItemType(RowsBean.content_44, R.layout.item_goods_style);
    }

    /**
     * 设置是否显示显示销售量
     *
     * @param isShowSalesQuantity
     */
    public void setIsShowSalesQuantity(boolean isShowSalesQuantity) {
        this.isShowSalesQuantity = isShowSalesQuantity;
    }

    /**
     * 设置是否显示删除功能
     *
     * @param isCanDelete
     */
    public void setIsCanDelete(boolean isCanDelete) {
        this.isCanDelete = isCanDelete;
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {

        switch (rowsBean.getItemType()) {
            case RowsBean.header_41:
                bindTitle(ybmBaseHolder, rowsBean);
                break;
            case RowsBean.header_42:
                bindSubTitle(ybmBaseHolder, rowsBean);
                break;
            case RowsBean.content_43:
            case RowsBean.content_44:
                bindItem(ybmBaseHolder, rowsBean);
                break;
        }

    }

    /*
     * title1
     * */
    private void bindTitle(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {

        ybmBaseHolder.setText(R.id.tv_title_01, rowsBean.getShowName());

    }

    /*
     * title2
     * */
    private void bindSubTitle(YBMBaseHolder ybmBaseHolder, RowsBean rowsBean) {


    }

    /*
     * 商品数据
     * */
    private void bindItem(YBMBaseHolder baseViewHolder, RowsBean rowsBean) {

        baseViewHolder.getConvertView().setOnClickListener(v -> {

            if (mOnItemClickListener != null) {
                currPosition = baseViewHolder.getAdapterPosition();
                mOnItemClickListener.onItemClick(rowsBean);
            }
        });

        isHaveGoods = true;
        isCanBuy = true;
        isOEMAndNotSigned = false;
        isShowRemind = false;

        TextView tv_shop_name = baseViewHolder.getView(R.id.shop_name);
        TextView tvBrandControl = baseViewHolder.getView(R.id.tv_brand_control);
        TextView tv_shop_pack_size = baseViewHolder.getView(R.id.shop_price_tv);
        TextView tv_shop_price = baseViewHolder.getView(R.id.shop_price);
        CheckBox shop_ck = baseViewHolder.getView(R.id.shop_ck);
        LinearLayout shop_ck_ll = baseViewHolder.getView(R.id.shop_ck_ll);
        ImageView ivShopMark = baseViewHolder.getView(R.id.iv_shop_mark);
        ProductEditLayout editLayout = baseViewHolder.getView(R.id.el_edit);
        TextView tvOEM = baseViewHolder.getView(R.id.tv_oem);
        LinearLayout llCompanyName = (LinearLayout) getSafeView(baseViewHolder, R.id.ll_company_name);
        LinearLayout ll_gong = (LinearLayout) getSafeView(baseViewHolder, R.id.ll_gong);
        TextView tv_original_price = baseViewHolder.getView(R.id.tv_original_price);
        TextView shop_no_limit_tv01 = baseViewHolder.getView(R.id.shop_no_limit_tv01);
        TextView tv_ziying = baseViewHolder.getView(R.id.icon_cart_proprietary);
        ImageView iv_remind = (ImageView) getSafeView(baseViewHolder, R.id.iv_remind);//到货提醒按钮，有可能返回null 需注意
        TextView tv_tag = (TextView) getSafeView(baseViewHolder, R.id.tv_health_insurance);//医保等标签
        ImageView iv_delete = (ImageView) getSafeView(baseViewHolder, R.id.iv_goods_item_delete);//删除整个item
        LinearLayout llShowPromotion = baseViewHolder.getView(R.id.ll_show_promotion);
        TextView ivGrossMargin = baseViewHolder.getView(R.id.icon_gross_margin);

        TextView tv_validity_period = baseViewHolder.getView(R.id.tv_validity_period);
        LinearLayout ll_validity_period = baseViewHolder.getView(R.id.ll_validity_period);
        if (tv_validity_period != null && rowsBean != null && !StringUtil.isEmpty(rowsBean.getNearEffect()) && !StringUtil.isEmpty(rowsBean.getFarEffect())) {
            setViewVisibility(ll_validity_period, View.VISIBLE);
            tv_validity_period.setText(rowsBean.getNearEffect() + "/" + rowsBean.getFarEffect());
        } else if (ll_validity_period != null) {
            setViewVisibility(ll_validity_period, View.GONE);
        }

        //是否高毛
        if (ivGrossMargin != null) {
            setViewVisibility(ivGrossMargin,rowsBean.getHighGross() == 2 ? View.VISIBLE : View.GONE);
        }

        baseViewHolder.setOnClickListener(R.id.ll_gong, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rowsBean.getIsThirdCompany() == 1) {
                    RoutersUtils.open("ybmpage://shopactivity?orgId=" + rowsBean.getOrgId());
                }
            }
        });

        List<LabelIconBean> mCxTagList = rowsBean.getTagList();
        if (null != llShowPromotion) {
            llShowPromotion.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
//                if (mPopWindowPromotion == null) {
                    mPopWindowPromotion = new ShowPromotionPopWindow(mContext);
                    mPopWindowPromotion.setNewData(mCxTagList);
//                }
                    mPopWindowPromotion.show(llShowPromotion);
                }
            });
        }

        // 设置促销活动标签
        PromotionTagView mPtv = baseViewHolder.getView(R.id.view_ptv);
        mPtv.setShowData(rowsBean.getActivityTag());

        //自营和厂家
        if (rowsBean.getIsThirdCompany() == 0) {
            //自营
            setViewVisibility(llCompanyName, View.VISIBLE);
            setViewVisibility(tv_ziying, View.VISIBLE);
            tv_ziying.setText("自营");
            setViewVisibility(ll_gong, View.GONE);
        } else {
            setViewVisibility(llCompanyName, View.GONE);
            setViewVisibility(tv_ziying, View.GONE);
            setViewVisibility(ll_gong, View.VISIBLE);
            TextView tv_open = baseViewHolder.getView(R.id.tv_open);//供应商
            setViewVisibility(tv_open, View.VISIBLE);
            tv_open.setText(rowsBean.getCompanyName());
            setViewVisibility(ll_gong, isShop ? View.GONE : View.VISIBLE);

        }

        baseViewHolder.setText(R.id.tv_chang_name, rowsBean != null ? rowsBean.getManufacturer() : "");

        //活动价
        boolean isShowUrl = rowsBean.isReducePrice() && rowsBean.isMarkerUrl();
        baseViewHolder.setText(R.id.tv_activity_price, String.valueOf("药采节价:" + UiUtils.transform(rowsBean.getReducePrice())));
        baseViewHolder.setGone(R.id.tv_activity_price, isShowUrl);

        // 控销价，毛利
        TextView tvRetailPrice = baseViewHolder.getView(R.id.tv_retail_price);
        setGoodsName(tv_shop_name, rowsBean);
        //商品区间价格-商品列表
        tv_shop_price.setText(UiUtils.showProductPrice(rowsBean));
        // 折后价
        tv_original_price.setText(rowsBean.showPriceAfterDiscount);
        if (!TextUtils.isEmpty(rowsBean.showPriceAfterDiscount)) {
            tv_original_price.setVisibility(View.VISIBLE);
            tv_original_price.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 11);
            tv_original_price.setTextColor(UiUtils.getColor(R.color.color_676773));
        } else {
            tv_original_price.setVisibility(View.GONE);
        }
        /*//原价
        if (!TextUtils.isEmpty(rowsBean.getRetailPrice())) {
            tv_original_price.setText("¥" + UiUtils.transform(rowsBean.getRetailPrice()), UnderlineTextView.MIDDLE);
            setViewVisibility(tv_original_price, View.VISIBLE);
        } else {
            setViewVisibility(tv_original_price, View.GONE);
        }*/
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl()).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(((ImageView)
                baseViewHolder.getView(R.id.icon)));
        if (TextUtils.isEmpty(rowsBean.getMarkerUrl())) {
            ImageHelper.with(mContext).load(R.drawable.transparent).into(ivShopMark);
        } else {
            String markerUrl = rowsBean.getMarkerUrl();
            if (!markerUrl.startsWith("http")) {
                markerUrl = AppNetConfig.LORD_TAG + markerUrl;
            }
            ImageHelper.with(mContext).load(markerUrl).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform()
                    .into(ivShopMark);
        }
        TagView tagView = baseViewHolder.getView(R.id.rl_icon_type);
        ImageView imageView = baseViewHolder.getView(R.id.iv_service);

        //设置标签图片
        setIcon(tagView, imageView, rowsBean);
//        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) llActivityTag.getLayoutParams();
//        if (params == null) {
//            params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//        }
        //控制横线显示
        if (tagView.getVisibility() == View.VISIBLE || tv_tag.getVisibility() == View.VISIBLE) {
//            params.setMargins(0, 0, 0, dp2px(22));
        } else {
//            params.setMargins(0, 0, 0, dp2px(15));
        }
//        llActivityTag.setLayoutParams(params);
        //中包装数量,不可拆零才显示,4.2.0以后所有的商品都显示
        if (null != tv_shop_pack_size) {
            if (!TextUtils.isEmpty(rowsBean.getMediumPackageTitle())) {
                tv_shop_pack_size.setText(rowsBean.getMediumPackageTitle().replace("：", ":"));
            }
            tv_shop_pack_size.setVisibility(!TextUtils.isEmpty(rowsBean.getMediumPackageTitle()) ? View.VISIBLE : View.GONE);
        }
        //如果建议零售价存在或者是控销并且不可购买，控销不显示
        if (TextUtils.isEmpty(rowsBean.getSuggestPrice()) && TextUtils.isEmpty(rowsBean.getUniformPrice())) {
            tvRetailPrice.setVisibility(View.INVISIBLE);
        } else {
            tvRetailPrice.setVisibility(View.VISIBLE);
            //建议零售价
            if (!TextUtils.isEmpty(rowsBean.getSuggestPrice())) {
                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
                        mContext.getResources().getString(R.string.product_list_lsj_title),
                        StringUtil.getUniformPrice2Double(rowsBean.getSuggestPrice()),
                        rowsBean.getGrossMargin()));
            }
            //控销价
            if (!TextUtils.isEmpty(rowsBean.getUniformPrice())) {
                tvRetailPrice.setText(String.format(mContext.getResources().getString(R.string.product_retail_price),
                        mContext.getResources().getString(R.string.product_list_kxj_title),
                        StringUtil.getUniformPrice2Double(rowsBean.getUniformPrice()),
                        rowsBean.getGrossMargin()));
            }
        }

        /**==============控销并且不可购买，控销和毛利率不显示===============
         * 商品设置了控销价，同时设置了价格区间，则商品在用户端显示控销价；
         * 若商品在促销活动中的设置了促销价格，同时该商品设置了价格区间，则商品在用户端显示促销价；
         * 如果是OEM商品，控销优先级高于OEM
         * **/
        boolean isBuy = true;
        if (editLayout != null) {
            setViewVisibility(editLayout, View.VISIBLE);
        }
        setViewVisibility(iv_remind, View.GONE);
        String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
        String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
        if (rowsBean.getStatus() == 2 || rowsBean.getStatus() == 4 || rowsBean.getAvailableQty() <= 0) {
            shop_no_limit_tv01.setVisibility(View.VISIBLE);
            setViewVisibility(tv_shop_pack_size, View.GONE);
            isHaveGoods = false;
            if (rowsBean.getStatus() == 2 || rowsBean.getAvailableQty() <= 0) {
                //售罄
                shop_no_limit_tv01.setText(sellOutStr);
            } else {
                //已下架
                shop_no_limit_tv01.setText((rowsBean.getStatus() == 4 ? soldOutStr : ""));
            }
        } else {
            //售罄等字样不显示
            isHaveGoods = true;
            setViewVisibility(shop_no_limit_tv01, View.INVISIBLE);
            setViewVisibility(tv_shop_pack_size, View.VISIBLE);
        }

        setViewVisibility(tv_shop_price, View.VISIBLE);

        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            tv_shop_price.setText("¥" + UiUtils.transform(rowsBean.getFob()));
            setViewVisibility(tv_shop_price, View.VISIBLE);
        }

        //加减按钮
        if (editLayout != null) {
            editLayout.bindData(rowsBean.getId(), rowsBean.getStatus(), isBuy, pageFrom, (ImageView) baseViewHolder.getView(R.id.icon), true, rowsBean.getStepNum(), rowsBean.getIsSplit() == 1);
        }

        //收藏
        if (null != shop_ck && null != shop_ck_ll) {
            setViewVisibility(shop_ck_ll, mIsCollect ? View.VISIBLE : View.GONE);
            shop_ck.setChecked(rowsBean.isFavoriteStatus());
            shop_ck_ll.setTag(rowsBean);
            shop_ck_ll.setOnClickListener(new onClickListener(shop_ck));
        }

        if (isShowSalesQuantity) {
            try {
                TextView tv_sales_quantity = baseViewHolder.getView(R.id.tv_sales_quantity);
                if (tv_sales_quantity != null) {
                    setViewVisibility(tv_sales_quantity, View.VISIBLE);
                    tv_sales_quantity.setText("销量 " + UiUtils.getNumber(rowsBean.getThirtyDaysAmount()) + "件");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /*
         * =============OEM============
         * isOEM true是OEM协议商品，为空或者false为非OEM协议商品 true/false
         * signStatus 协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
         * */

        //是否控销  true:可购买 false:不可买
        if (rowsBean.getIsControl() == 1) {
            //控销可购买
            if (rowsBean.isPurchase()) {
                isCanBuy = true;
                //商品区间价格
                if (rowsBean.getPriceType() == 1) {
                    tv_shop_price.setText("¥" + UiUtils.transform(rowsBean.getFob()));
                } else {
                    tv_shop_price.setText(UiUtils.showProductPrice(rowsBean));
                }

                //是否是OEM协议商品
                setIsOEM(rowsBean, tvRetailPrice);
            } else {
                //控销不可购买
                isCanBuy = false;
                tv_shop_price.setText("¥--");
                setViewVisibility(tvRetailPrice, View.GONE);
            }
        } else {
            //是否是OEM协议商品
            setIsOEM(rowsBean, tvRetailPrice);
        }

        // 到货提醒
        if (iv_remind != null) {
            if ((rowsBean.isArrivalReminder() || rowsBean.getStatus() == 2) && !isKaUser) {//增加ka用户不显示到货提醒
                //显示到货提醒
                isShowRemind = true;
                setViewVisibility(iv_remind, View.VISIBLE);
                if (rowsBean.getBusinessType() == 1) {//已经订阅了到货提醒
                    iv_remind.setEnabled(false);
                } else {
                    iv_remind.setEnabled(true);
                }
            } else {
                //不显示到货提醒
                isShowRemind = false;
                setViewVisibility(iv_remind, View.GONE);
            }
        } else {
            isShowRemind = false;
        }

        //控制显示加入购物车按钮显示
        if (isCanBuy && isHaveGoods && !isOEMAndNotSigned && !isShowRemind) {
            setViewVisibility(editLayout, View.VISIBLE);
        } else {
            setViewVisibility(editLayout, View.GONE);
        }

        //控制显示价格等布局
        if (!isCanBuy || isOEMAndNotSigned) {
            if (!isCanBuy) {
                setViewVisibility(tvBrandControl, View.VISIBLE);//显示暂无购买权限
                setViewVisibility(tvOEM, View.GONE);
            } else if (isOEMAndNotSigned) {
                setViewVisibility(tvOEM, View.VISIBLE);//显示签署协议可见
                setViewVisibility(tvBrandControl, View.GONE);
            }
            setViewVisibility(tv_shop_price, View.GONE);
            setViewVisibility(tv_original_price, View.GONE);
            setViewVisibility(tv_shop_pack_size, View.GONE);
            setViewVisibility(tvRetailPrice, View.INVISIBLE);

        } else {
            setViewVisibility(tvBrandControl, View.GONE);
            setViewVisibility(tvOEM, View.GONE);
            setViewVisibility(tv_shop_price, View.VISIBLE);
            setViewVisibility(tv_shop_pack_size, View.VISIBLE);
            setViewVisibility(tv_original_price, View.VISIBLE);
        }

        //到货提醒按钮
        if (iv_remind != null) {
            iv_remind.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setRemindCollect(v.getContext(), rowsBean);
                }
            });
        }

        if (!isCanDelete) {
            setViewVisibility(iv_delete, View.GONE);
        } else {
            setViewVisibility(iv_delete, View.VISIBLE);
            iv_delete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDeleteItemClickCallback != null) {
                        onDeleteItemClickCallback.onDeleteItemClick(baseViewHolder.getAdapterPosition());
                    }
                }
            });
        }

    }

    //可以适配多手机
    public int dp2px(int dp) {
        if (dp == 0) {
            return 0;
        }
        return ConvertUtils.dp2px(dp);
    }

    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, RowsBean rowsBean) {

        List<Drawable> list = new ArrayList<>();
        String showContent = rowsBean.getShowName() + "/" + rowsBean.getSpec();//商品名称和规格
        int nameLength = rowsBean.getShowName().length();

        if (rowsBean.getAgent() == 1) {//显示独家
            Drawable drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_dujia);
            drawable.setBounds(0, 0, ConvertUtils.dp2px(28), ConvertUtils.dp2px(15));
            list.add(drawable);
        }

        if (rowsBean.isGift()) {//显示药彩节
            list.add(ContextCompat.getDrawable(mContext, R.drawable.icon_procurement_festival));
        }

        try {

            if (rowsBean.getActivityTag() != null && !TextUtils.isEmpty(rowsBean.getActivityTag().tagUrl)) {
                String url = rowsBean.getActivityTag().tagUrl;
                if (!rowsBean.getActivityTag().tagUrl.startsWith("http")) {
                    url = AppNetConfig.LORD_TAG + rowsBean.getActivityTag().tagUrl;
                }

                ImageHelper.with(mContext).load(url)
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate().dontTransform().into(new SimpleTarget<GlideDrawable>() {
                    @Override
                    public void onResourceReady(GlideDrawable resource, GlideAnimation<? super GlideDrawable> glideAnimation) {
                        list.add(resource);
                        setShowActivityTag(tv_shop_name, showContent, nameLength, list);
                    }
                });

            } else {
                setShowActivityTag(tv_shop_name, showContent, nameLength, list);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private void setShowActivityTag(TextView textView, String showName, int nameLength, List<Drawable> list) {

        SpannableStringBuilder shopName = getShopNameIcon(showName, nameLength, list);
        textView.setText(shopName);

    }

    private SpannableStringBuilder getShopNameIcon(String shopName, int nameLength, List<Drawable> icons) {

        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        spannableString.setSpan(new AbsoluteSizeSpan(15, true), 0, nameLength + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new AbsoluteSizeSpan(12, true), nameLength + 1, shopName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        if (icons != null && icons.size() > 0) {

            for (int i = 0; i < icons.size(); i++) {
                Drawable drawable = icons.get(i);
                //适配图标大小问题

                if (drawable != null) {
                    Rect dRect = drawable.getBounds();
                    int height = ConvertUtils.dp2px(13);
                    float width = (int) (((float) dRect.width() / (float) dRect.height()) * height);
                    if (dRect.height() != 0)
                        drawable.setBounds(0, 0, (int) width, ConvertUtils.dp2px(13));
                    else drawable.setBounds(0, 0, ConvertUtils.dp2px(35), ConvertUtils.dp2px(13));
                    MyImageSpan imageSpan = new MyImageSpan(drawable, 2);
                    //占个位置
                    spannableString.insert(0, "-");
                    spannableString.setSpan(imageSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }
        return spannableString;
    }

    /**
     * TextView 文字和图片混合显示，先显示图片后显示文字
     *
     * @param textView
     * @param drawables
     */
    private void textHeadImg(TextView textView, SpannableString ss, Drawable... drawables) {
        int position = 0;
        if (drawables != null && drawables.length > 0) {
            for (int i = 0; i < drawables.length; i++) {
                Drawable drawable = drawables[i];
                if (drawable != null) {
                    drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
                    TextImage span = new TextImage(drawable);
                    ss.setSpan(span, position, position + 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    position += 1;
                }
            }
        }
        textView.setText(ss);
    }


    /**
     * 由于该项目好多页面都公用这个Adpater，有些界面找不到id，会报错，因此捕获异常
     *
     * @param baseViewHolder
     * @return
     */
    private View getSafeView(YBMBaseHolder baseViewHolder, int resId) {
        try {
            return baseViewHolder.getView(resId);
        } catch (Exception e) {
            e.printStackTrace();
            LogUtils.e(e);
        }
        return null;
    }


    /*
     * =============OEM============
     * isOEM true是OEM协议商品，为空或者false为非OEM协议商品 true/false
     * signStatus 协议签署状态(0-未签署,1-已签署)是普通商品或者(OEM协议商品且已签署协议)才会显示价格
     * */
    private void setIsOEM(RowsBean rowsBean, TextView tvRetailPrice) {
        if (rowsBean.getIsOEM()) {//是OEM协议商品
            //是否签署协议
            if (rowsBean.getSignStatus() == 1) {//已经签署
                isOEMAndNotSigned = false;
                boolean isShow = (!StringUtil.isEmpty(rowsBean.getUniformPrice()) || !StringUtil.isEmpty(rowsBean.getSuggestPrice()));
                setViewVisibility(tvRetailPrice, isShow ? View.VISIBLE : View.INVISIBLE);
            } else {//未签署
                isOEMAndNotSigned = true;
                setViewVisibility(tvRetailPrice, View.INVISIBLE);
            }
        } else {
            isOEMAndNotSigned = false;
            //是否符合协议标准展示价格,1:符合0:不符合
            if (rowsBean.showAgree == 0) {
                isOEMAndNotSigned = true;
                setViewVisibility(tvRetailPrice, View.INVISIBLE);
            }
        }
    }

    /**
     * 设置图标
     *
     * @param rowsBean
     */
    private void setIcon(TagView tagView, ImageView imageView, RowsBean rowsBean) {
        //在这里对标签数量进行处理
        if (rowsBean.getTagList() != null && rowsBean.getTagList().size() > 0) {
            tagView.bindData(rowsBean.getTagList(), 3, true);
            if (null != imageView) {
                imageView.setVisibility(View.VISIBLE);
            }
        } else {
            tagView.setVisibility(View.GONE);
            if (null != imageView) {
                imageView.setVisibility(View.GONE);
            }
        }

    }

    private void setViewVisibility(View view, int visibility) {
        if (view == null) {
            LogUtils.e("view == null");
            return;
        }
        if (view.getVisibility() != visibility) {
            view.setVisibility(visibility);
        }
    }

    /**
     * 设置控销价或者零售价
     *
     * @param tv_shop_price_kxj_number
     * @param tv_shop_price_ml_number
     * @param uniformPrice2Double
     */
    private void setSuggestOrGrossMargin(String grossMarginStr, TextView tv_shop_price_kxj_number, TextView tv_shop_price_ml_number, String uniformPrice2Double) {
        try {
            //建议零售价或者控销价
            tv_shop_price_kxj_number.setText(uniformPrice2Double);
            //毛利率
            if (!TextUtils.isEmpty(uniformPrice2Double) && uniformPrice2Double.length() > 6 && mContext instanceof MainActivity) {
                tv_shop_price_ml_number.setText("...");//只显示两个..
            } else {//正常显示价格
                tv_shop_price_ml_number.setText(grossMarginStr);
            }
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }

    /*
     * 收藏
     * */
    private class onClickListener implements View.OnClickListener {

        private CheckBox cb;

        private onClickListener(CheckBox cb) {
            this.cb = cb;
        }

        @Override
        public void onClick(View v) {

            RowsBean rowsBean = ((RowsBean) v.getTag());
            switch (v.getId()) {
                case R.id.shop_ck_ll:
                    checkCollect(rowsBean, cb);
                    break;
            }
        }
    }

    /*
     *收藏-取消收藏
     * */
    private void checkCollect(final RowsBean rowsBean, final CheckBox checkBox) {

        final long id = rowsBean.getId();
        final String collect_net = checkBox.isChecked() ? AppNetConfig.CANCEL_COLLECT : AppNetConfig.COLLECT;
        final String collect_str = checkBox.isChecked() ? "取消收藏" : "收藏成功";

        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(id));

        HttpManager.getInstance().post(collect_net, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                if (null != obj) {
                    if (obj.isSuccess()) {
                        if (checkBox.isChecked()) {
                            checkBox.setChecked(false);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        } else {
                            checkBox.setChecked(true);
                            DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, collect_str);
                        }

                    }
                }

            }
        });
    }

    public interface OnDeleteItemClickCallback {
        void onDeleteItemClick(int position);
    }

    public void setOnDeleteItemClickCallback(OnDeleteItemClickCallback onDeleteItemClickCallback) {
        this.onDeleteItemClickCallback = onDeleteItemClickCallback;
    }

    protected OnListViewItemClickListener mOnItemClickListener = null;

    public interface OnListViewItemClickListener {
        void onItemClick(RowsBean rows);
    }

    public void setOnItemClickListener(OnListViewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

    public int getCurrPosition() {
        return currPosition;
    }

    /**
     * 订阅提醒
     */
    private void setRemindCollect(Context context, RowsBean rowsBean) {
        int businessType = 1;
        requestData(context, rowsBean, businessType, rowsBean.getId());
    }

    /**
     * businessType 收藏类型 默认收藏不传，1: 表示有货提醒业务类型；2：降价提醒
     * merchantId   商品业务相关id
     * skuId        商户id
     * 服务端请求
     */
    private void requestData(Context context, RowsBean rowsBean, final int businessType, final long skuId) {
        ((BaseActivity) context).showProgress();
        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(skuId));
        params.put("businessType", String.valueOf(businessType));

        HttpManager.getInstance().post(AppNetConfig.COLLECT, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                ((BaseActivity) context).dismissProgress();
                if (null != obj) {
                    if (obj.isSuccess()) {
                        showRemindDialog(context);
                        rowsBean.setBusinessType(1);
                        notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                ((BaseActivity) context).dismissProgress();
            }
        });
    }

    private void showRemindDialog(Context context) {

        String str = "若该商品在45天内到货，药帮忙会提醒您！ 同时您可以在我的收藏夹查看您订阅过的所有商品";
        AlertDialogEx dialogEx = new AlertDialogEx(context);
        dialogEx.setMessage(str).setCancelButton("我知道啦", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle("订阅成功").show();
    }

}
