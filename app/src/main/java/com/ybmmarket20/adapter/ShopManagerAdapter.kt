package com.ybmmarket20.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SelectLoginShopInfo
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.utils.textWithSuffixTag

/**
 * 店铺管理列表
 */
class ShopManagerAdapter(data: MutableList<SelectLoginShopInfo>) :
    YBMBaseAdapter<SelectLoginShopInfo>(R.layout.item_shop_manager, data) {

    var mOSelectListener: ((Int) -> Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SelectLoginShopInfo?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            val shopName = holder.getView<TextView>(R.id.tvShopName)
            shopName.textWithSuffixTag(listOf(bean.tagBean), "${bean.name} ", 1)
            holder.setText(R.id.tvShopAddress, "${bean.province}${bean.city}${bean.district}${bean.street}${bean.address}")
            val tvCheckClerk = holder.getView<TextView>(R.id.tvCheckClerk)
            tvCheckClerk.setOnClickListener {
                RoutersUtils.open("ybmpage://clerkinfo?merchantId=${bean.merchantId}")
                XyyIoUtil.trackForAccountWithMerchantId("action_managePoi_viewEmployees_click")
            }
            val ivEditStatus = holder.getView<ImageView>(R.id.ivEditStatus)
            ivEditStatus.setOnClickListener {
                if (bean.editStatus == 2) {
                    mOSelectListener?.invoke(holder.bindingAdapterPosition)
                    XyyIoUtil.trackForAccountWithMerchantId("action_managePoi_disassociate_click")
                }
            }
            when (bean.editStatus) {
                0 -> {
                    ivEditStatus.visibility = View.GONE
                    tvCheckClerk.visibility = if (bean.role == 1) View.VISIBLE else View.GONE
                }
                1 -> {
                    ivEditStatus.setImageResource(R.drawable.icon_correct)
                    ivEditStatus.visibility = View.VISIBLE
                    tvCheckClerk.visibility = if (bean.role == 1) View.VISIBLE else View.GONE
                }
                2 -> {
                    ivEditStatus.setImageResource(R.drawable.icon_shop_info_delete)
                    ivEditStatus.visibility = View.VISIBLE
                    tvCheckClerk.visibility = View.GONE
                }
            }

        }
    }

    fun setOnSelectListener(onSelectListener: (Int) -> Unit) {
        mOSelectListener = onSelectListener
    }

}