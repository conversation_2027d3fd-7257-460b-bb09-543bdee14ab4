package com.ybmmarket20.adapter

import android.content.Intent
import android.widget.ImageView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.CouponScopeGoods
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.ifNotNull


/**
 * 优惠券使用商品
 */

const val ITEM_TYPE_UNLAST = 0
const val ITEM_TYPE_LAST = 1

class CouponScopeGoodsAdapter(
        data: List<CouponScopeGoods>,
        private val couponId: String
) : YBMBaseMultiItemAdapter<CouponScopeGoods>(data) {

    init {
        addItemType(ITEM_TYPE_UNLAST, R.layout.item_coupon_scope_goods_unlast)
        addItemType(ITEM_TYPE_LAST, R.layout.item_coupon_scope_goods_last)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: CouponScopeGoods?) {
        ifNotNull(baseViewHolder, t) {holder, bean ->
            if(bean.itemType == ITEM_TYPE_UNLAST) {
                //非最后一个
                val iv = holder.getView<ImageView>(R.id.iv_coupon_scope_goods)
                ImageHelper.with(mContext).load("${AppNetConfig.LORD_IMAGE}${bean.imageUrl}")
                        .placeholder(R.drawable.jiazaitu_min)
                        .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                        .dontAnimate()
                        .into(iv)
                holder.getConvertView().setOnClickListener {
                    val intent = Intent(mContext, ProductDetailActivity::class.java)
                    intent.putExtra(IntentCanst.PRODUCTID, bean.skuId.toString() + "")
                    mContext.startActivity(intent)
                }
            } else {
                //最后一个
                holder.getConvertView().setOnClickListener {
                    RoutersUtils.open("ybmpage://couponavailableactivity/$couponId")
                }
            }
        }
    }
}