package com.ybmmarket20.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.CheckBox
import androidx.core.text.isDigitsOnly
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

class FiltrateClassShopAdapter(layoutResId: Int, val list: MutableList<SearchFilterBean>?)
    : YBMBaseAdapter<SearchFilterBean>(layoutResId, list) {
    var mCallback: ((selectedSpecCount: String, key: String, isSelect: Boolean)-> Unit)? = null

    @SuppressLint("NotifyDataSetChanged")
    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchFilterBean?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val cbItem = baseViewHolder.getView<CheckBox>(R.id.cb_item)
            cbItem.text = bean.showName
            cbItem.isChecked = bean.isSelected
            holder.setOnClickListener(R.id.ll_item) {
                bean.apply {
                    isSelected = !isSelected
                    notifyDataSetChanged()
                    if (mCallback != null) {
                        mCallback!!.invoke(bean.showName, bean.key, isSelected)
                    }
                }
            }
        }
    }

    fun setOnClickListener(callback: ((selectedSpecCount: String, key: String, isSelect: Boolean)-> Unit)?) {
        mCallback = callback
    }
}