package com.ybmmarket20.adapter;

import android.graphics.Color;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.PlanProductInfoBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ProductEditLayout2;
import com.ybmmarket20.view.SwipeMenuLayout;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by mbdn on 2017/4/7.
 */

public class PlanProduct2Adapter extends PlanProductAdapter {

    public PlanProduct2Adapter(List<PlanProductInfoBean> data) {
        super(R.layout.list_item_plan_product, data);
    }


    /**
     * 获取添加采购单的商品和名称
     */
    public Map<String, String> getSelectProduct() {
        Map<String, String> select = new HashMap<>();
        PlanProductInfoBean item;
        for (int i = 0, len = mData.size(); i < len; i++) {
            item = (PlanProductInfoBean) mData.get(i);
            if (item == null) {
                continue;
            }
            if (item.isChecked == 1) {
                select.put(item.productId + "", item.purchaseNumber + "");
            }
        }
        return select;
    }


    @Override
    protected void bindItemView(final YBMBaseHolder holder, final PlanProductInfoBean bean) {
        ((SwipeMenuLayout) holder.itemView).setExpandListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSwipeListener != null) {
                    mSwipeListener.onSwipe(holder.getAdapterPosition());
                }
            }
        });
//        holder.setText(R.id.tv_product_name, TextUtils.isEmpty(bean.productName) ? "暂无" : bean.productName);
//        holder.setText(R.id.tv_product_standard, "规格:" + bean.spec);
        if (TextUtils.isEmpty(bean.productName)) {
            bean.productName = "暂无";
        }
        TextView tv_shop_name = holder.getView(R.id.tv_product_name);
        tv_shop_name.setText(bean.productName + "/" + bean.spec);
        setGoodsName(tv_shop_name, bean);
        holder.setText(R.id.tv_product_company, bean.manufacturer);

        TextView tvPrice = holder.getView(R.id.tv_product_price);
        String fob = "¥" + UiUtils.transform(bean.fob);
        setFobPrice(tvPrice, fob);

        boolean isShow = (bean.productStatus == 2 || bean.productStatus == 4);
        holder.setGone(R.id.iv_cart, bean.isCart == 1 && !isShow);

        String sellOutStr = mContext.getResources().getString(R.string.text_sell_out);
        String soldOutStr = mContext.getResources().getString(R.string.text_sold_out);
        holder.setGone(R.id.shop_no_limit_tv01, isShow);
        holder.setText(R.id.shop_no_limit_tv01, bean.productStatus == 2
                ? sellOutStr : (bean.productStatus == 4 ? soldOutStr : ""));


        CheckBox cbSelect = holder.getView(R.id.cb_select_state);
        ProductEditLayout2 tvNum = holder.getView(R.id.tv_product_num);
        tvNum.bindData(bean);
        ImageView ivPhoto = holder.getView(R.id.iv_product_photo);
//        tvNum.setText("补货数量：" + bean.purchaseNumber);
        tvPrice.setVisibility(TextUtils.isEmpty(bean.fob) ? View.GONE : View.VISIBLE);
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + bean.photo).placeholder(R.drawable.transparent).error(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(ivPhoto);
        cbSelect.setOnCheckedChangeListener(null);
        cbSelect.setChecked(bean.isChecked == 1);
        cbSelect.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    bean.isChecked = 1;
                } else {
                    bean.isChecked = 0;
                }
                if (mSelectAllListener != null) {
                    mSelectAllListener.isChecked(isChecked, bean);
                }
            }
        });

        holder.getView(R.id.rl_root).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mItemClickListener != null) {
                    mItemClickListener.onItemClick(holder.getAdapterPosition());
                }
            }
        });

        holder.getView(R.id.tv_delete).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSwipeListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mSwipeListener.onDelete(holder.getAdapterPosition());
                }
            }
        });
        handleAuditPassedVisible(holder);
    }

    private void setFobPrice(TextView tvPrice, String fob) {
        int fobLastLength = fob.indexOf(".");
        tvPrice.setText(fob);
        setFabTaxAmount(tvPrice, fob, 1, fobLastLength);
    }

    /*
     * 设置价格显示不同大小
     * */
    public void setFabTaxAmount(TextView tvPrice, String str, int fobLength, int fobLastLength) {
        if (TextUtils.isEmpty(str) || str.length() == fobLength) {
            return;
        }
        try {
            SpannableString spanText = new SpannableString(str);
            spanText.setSpan(new ForegroundColorSpan(UiUtils.getColor(R.color.tv_tax)) {

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setTextSize(UiUtils.sp2px(20));
                }

            }, fobLength, fobLastLength, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            tvPrice.setText(spanText);
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }

    }

    /**
     * 显示商品name+规格 and head show tag
     *
     * @param tv_shop_name
     * @param rowsBean
     */
    private void setGoodsName(TextView tv_shop_name, PlanProductInfoBean rowsBean) {

        String showContent = rowsBean.productName + "/" + rowsBean.spec;//商品名称和规格
        int nameLength = TextUtils.isEmpty(rowsBean.productName) ? 0 : rowsBean.productName.length();
        setShowActivityTag(tv_shop_name, showContent, nameLength);

    }

    private void setShowActivityTag(TextView textView, String showName, int nameLength) {

        SpannableStringBuilder shopName = getShopNameIcon(textView, showName, nameLength);
        textView.setText(shopName);

    }

    private SpannableStringBuilder getShopNameIcon(TextView textView, String shopName, int nameLength) {
        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
        try {
            spannableString.setSpan(new ClickableSpan() {

                @Override
                public void updateDrawState(TextPaint ds) {
                    super.updateDrawState(ds);
                    //设置文件颜色
                    ds.setColor(UiUtils.getColor(R.color.text_9494A6));
                    //设置下划线
                    ds.setTypeface(Typeface.DEFAULT);
                    ds.setUnderlineText(false);
                }

                @Override
                public void onClick(View view) {

                }
            }, nameLength, spannableString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            //设置点击后的颜色为透明，否则会一直出现高亮
            textView.setHighlightColor(Color.TRANSPARENT);
            textView.setText(spannableString);
            //开始响应点击事件
            textView.setMovementMethod(LinkMovementMethod.getInstance());

//        SpannableStringBuilder spannableString = new SpannableStringBuilder(shopName);
            spannableString.setSpan(new AbsoluteSizeSpan(16, true), 0, nameLength + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
            spannableString.setSpan(new AbsoluteSizeSpan(12, true), nameLength + 1, shopName.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        } catch (Exception e) {
            BugUtil.sendBug(e);

        }
        return spannableString;
    }

    /**
     * 处理价格认证资质可见
     */
    private void handleAuditPassedVisible(YBMBaseHolder baseViewHolder) {
        TextView tvAuditPassedVisible = baseViewHolder.getView(R.id.tv_audit_passed_visible);
        //审核通过
        if(AuditStatusSyncUtil.getInstance().isAuditFirstPassed()) {
            tvAuditPassedVisible.setVisibility(View.GONE);
        } else {
            tvAuditPassedVisible.setVisibility(View.VISIBLE);
            baseViewHolder.setGone(R.id.tv_product_price, false);
            baseViewHolder.setGone(R.id.tv_product_num, false);
        }
    }

}
