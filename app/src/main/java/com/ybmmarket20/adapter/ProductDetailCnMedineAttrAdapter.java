package com.ybmmarket20.adapter;

import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ProductDetailKeyAttr;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025.3.28
 * @desc 商品详情：中药属性列表
 */

public class ProductDetailCnMedineAttrAdapter extends YBMBaseAdapter<ProductDetailKeyAttr> {

    public ProductDetailCnMedineAttrAdapter(List<ProductDetailKeyAttr> items) {
        super(R.layout.item_product_detail_cnmedine_attr, items);
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, ProductDetailKeyAttr rowsBean) {
        TextView tvValue = ybmBaseHolder.getView(R.id.tvKeyAttrValue);
        TextView tvTitle = ybmBaseHolder.getView(R.id.tvKeyAttrTitle);
        // 最后一个
        ybmBaseHolder.setVisible(R.id.viewLine,ybmBaseHolder.getAbsoluteAdapterPosition() != getData().size() - 1);
        tvTitle.setText(rowsBean.attrTitle);
        tvValue.setText(rowsBean.attrValue);
    }
}
