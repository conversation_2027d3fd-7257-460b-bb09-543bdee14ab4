package com.ybmmarket20.adapter

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_MULTI_SHOP
import com.ybmmarket20.bean.OPERATION_POSITION_TYPE_SINGLE_SHOP
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_GOODS
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION
import com.ybmmarket20.bean.SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS
import com.ybmmarket20.bean.SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI
import com.ybmmarket20.bean.SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE
import com.ybmmarket20.bean.SEARCH_LIST_ITEMTYPE_RECOMMEND
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageOPListExposureWithCode
import com.ybmmarket20.utils.analysis.getOpenUrlNotJump
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.view.operationposition.OPCardAdapter
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsCombinedBuyMultiAdapter
import com.ybmmarketkotlin.adapter.GoodsCombinedBuySingleAdapter
import com.ybmmarketkotlin.adapter.GoodsCombinedBuySingleBannerController
import com.ybmmarketkotlin.adapter.GoodsListAnalysisAdapter
import com.ybmmarketkotlin.adapter.GoodsSearchRecommendAdapter
import com.ybmmarketkotlin.adapter.SpellGroupLimitTimePremiumAdapter
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener

/**
 * 大搜Adapter(商品+运营位)
 */
class SearchAdapter(
    val context: Context,
    data: MutableList<SearchRowsBean>,
    private val mSingleBannerController: GoodsCombinedBuySingleBannerController? = null
) : GoodsListAnalysisAdapter<SearchRowsBean>(data) {
    // 是否为加载更多：组合购需要此状态
    var loadMoreFlag = false
        set(value) {
            field = value
            mGoodsCombinationSingleAdapter.loadMoreFlag = field
            mGoodsSearchRecommendAdapter.loadMoreFlag = field
        }

    //fromSearchFlag old true 大搜 false 常购常搜
    //fromSearchFlag new  1 大搜 2常购常搜 3 一键补货
    constructor(iCouponEntryType: ICouponEntryType, context: Context
                , data: MutableList<SearchRowsBean>,fromSearchFlag:Int = 1
                , singleBannerController: GoodsCombinedBuySingleBannerController? = null
    ): this(context, data, singleBannerController) {
        goodsAdapter.mCouponEntryType = iCouponEntryType
        goodsAdapter.isFromSearch = fromSearchFlag== 1
        goodsAdapter.isFromFrequently = fromSearchFlag== 2
        goodsAdapter.isFromRestocking = fromSearchFlag == 3
        mLimitTimePremiumAdapter.mCouponEntryType = iCouponEntryType
        mLimitTimePremiumAdapter.isFromSearch = fromSearchFlag==1
        mLimitTimePremiumAdapter.isFromFrequently = fromSearchFlag== 2
        mLimitTimePremiumAdapter.isFromRestocking = fromSearchFlag==3
    }


    private val goodsAdapter: GoodListAdapterNew = GoodListAdapterNew(R.layout.item_goods_new, mutableListOf(), true)
    private val mLimitTimePremiumAdapter: SpellGroupLimitTimePremiumAdapter = SpellGroupLimitTimePremiumAdapter(mutableListOf())
    private val mGoodsCombinationSingleAdapter = GoodsCombinedBuySingleAdapter(mutableListOf(), mSingleBannerController)
    private val mGoodsCombinationMultiAdapter = GoodsCombinedBuyMultiAdapter(mutableListOf())
    private val mGoodsSearchRecommendAdapter = GoodsSearchRecommendAdapter(mutableListOf())

    fun getListGoodsAdapter(): GoodListAdapterNew = goodsAdapter

    private val opAdapter = OPCardAdapter(mutableListOf())

    companion object {
//        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
//        private const val TRACK_DURATION = 0 //无时间限时
    }

    override var flowData: BaseFlowData? = null
        set(value) {
            field = value
            goodsAdapter.flowData = value
            mLimitTimePremiumAdapter.flowData = value
            opAdapter.flowData = value
        }

    init {
        //商品
        addItemType(SEARCH_LIST_CARD_TYPE_GOODS, goodsAdapter.layoutResId)
        //运营位
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION, opAdapter.layoutResId)
        //单品使用商品样式
        addItemType(SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS, goodsAdapter.layoutResId)
        //限时加补拼团品样式
//        addItemType(SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS,R.layout.item_goods_new_limit_time_premium)
        // 组合购
        addItemType(SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE,mGoodsCombinationSingleAdapter.layoutResId)
        // 加价购
        addItemType(SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI,mGoodsCombinationMultiAdapter.layoutResId)
        // 搜索推荐
        addItemType(SEARCH_LIST_ITEMTYPE_RECOMMEND,mGoodsSearchRecommendAdapter.layoutResId)
        goodsAdapter.setContext(context)
        mLimitTimePremiumAdapter.setContext(context)
        opAdapter.setContext(context)
        mGoodsCombinationSingleAdapter.setContext(context)
        mGoodsCombinationMultiAdapter.setContext(context)
        mGoodsSearchRecommendAdapter.setContext(context)
    }

    override fun setBaseFlowData(flowData: BaseFlowData?) {

    }


    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean) {
        super.bindItemView(baseViewHolder, t)
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when(bean.itemType) {
                SEARCH_LIST_CARD_TYPE_GOODS -> bindGoodsItemView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION_SINGLE_GOODS -> bindOPItemGoodsView(holder, bean)
                SEARCH_LIST_CARD_TYPE_OPERATION_POSITION -> bindOPItemView(holder, bean)
//                SEARCH_SPELL_GROUP_LIMIT_TIME_PREMIUM_GOODS -> bindLimitTimePremiumGoodsItemView(holder, bean)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE -> bindCombinationSingleItemView(holder, bean)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI -> bindCombinationMultiItemView(holder, bean)
                SEARCH_LIST_ITEMTYPE_RECOMMEND -> bindRecommendItemView(holder, bean)
            }
        }
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchRowsBean, payloads: List<Any?>) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when(bean.itemType) {
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_SINGLE -> bindCombinationSingleItemView(holder, bean,payloads)
                SEARCH_LIST_ITEMTYPE_COMBINATIONBUY_MULTI -> bindCombinationMultiItemView(holder, bean,payloads)
                SEARCH_LIST_ITEMTYPE_RECOMMEND -> bindRecommendItemView(holder, bean,payloads)
            }
        }
    }

    override fun setNewData(data: MutableList<Any?>?) {
        super.setNewData(data)
    }

    /**
     * 商品
     */
    private fun bindGoodsItemView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        bean.productInfo?.let {
            goodsAdapter.bindItemViewWithBackground(holder, it, getGoodsViewItemBackgroundResId(holder))
        }
    }

    /**
     * 运营位
     */
    private fun bindOPItemView(
            holder: YBMBaseHolder,
            bean: SearchRowsBean
    ) {
        opAdapter.bindItemViewWithBackground(
                holder,
                bean)
    }

    /**
     * 运营位展示商品样式
     */
    private fun bindOPItemGoodsView(holder: YBMBaseHolder, bean: SearchRowsBean) {
        try {
            bean.operationInfo?.let {
                val rowsBean = it.products[0]
                rowsBean.acptUrl = it.jumpUrl
                rowsBean.isOPSingleGoods = true
                rowsBean.directModule = "2"
                rowsBean.oPSingleGoodsActName = it.title
                rowsBean.opSingleGoodsActJumpUrl = it.jumpUrl
                rowsBean.shopName = it.products[0].shopName
                rowsBean.shopUrl = it.products[0].shopUrl
                bean.productInfo = rowsBean
                bindGoodsItemView(holder, bean)
                traceGoodsExposure(rowsBean,holder)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 组合购
     */
    private fun bindCombinationSingleItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean
    ) {
        // 转为List 供 banner使用。 主品不复用
        val purchaseInfos = bean.newGroupPurchaseInfo!!.groupPurchaseInfos
        mGoodsCombinationSingleAdapter.mListener =  object : CombinedBuyListener {
            override fun refresh(preItem:RowsBeanCombinedExt,preSubPosition: Int,item:RowsBeanCombinedExt,subPosition: Int,firstFlag:Boolean) {
                // 刷新 需要将新的副品设置为选中
                purchaseInfos[subPosition].subProducts[0].selectStatus = 1
                GoodsPlaceExposureRecord.get(mContext).clearRecordByKey(preItem.getGroupGoodsPlaceInfo()?.getRecordKey(mContext))
                if(firstFlag && subPosition == 0){
                    AdapterUtils.getGroupPurchaseInfo(purchaseInfos[subPosition], this@SearchAdapter,true,holder.layoutPosition,true,null)
                }else{
                    AdapterUtils.checkCombinedProductNum(purchaseInfos[subPosition],item,subPosition,this@SearchAdapter,holder.layoutPosition,false,true)
                }
            }

            override fun pageSeleted(position: Int, item: GroupPurchaseInfo) {
                super.pageSeleted(position, item)
                mGoodsCombinationSingleAdapter.bannerPos = position
                try {
                    Log.i("组合购滚动", "搜索")
                    //主品曝光
                    if (position == 0) {
                        trackSingleCombineGoodsExposure(item.mainProduct, holder.bindingAdapterPosition)
                    }
                    //副品曝光
                    trackSingleCombineGoodsExposure(item.subProducts[0], holder.bindingAdapterPosition)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                jumpToProductDetail(bean, holder)
                SearchProductReport.trackSearchGoodsClick(mContext, holder.bindingAdapterPosition, bean)
            }

            override fun preSettle(subPosition: Int) {
                AdapterUtils.preSettle(mContext, purchaseInfos[subPosition],subPosition)
                SearchProductReport.trackSearchPlaceOrderClick(mContext, purchaseInfos[subPosition].mainProduct, listOf(purchaseInfos[subPosition].subProducts[0]), true, true)
            }

            override fun changeNum(itemBean: RowsBeanCombinedExt,curSubPos:Int,addFlag:Boolean,preNum:Int) {
                AdapterUtils.checkCombinedProductNum(purchaseInfos[curSubPos],itemBean,curSubPos,this@SearchAdapter,holder.layoutPosition,true,false)
            }

            override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int,addFlag:Boolean, preNum: Int) {
                trackCombinationBtnClick(bean, curSubPosition, addFlag, preNum)
            }
        }
        mGoodsCombinationSingleAdapter.bindItemView(
            holder,
            bean.newGroupPurchaseInfo!!)
    }
    private fun bindCombinationSingleItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean,
        payloads:List<Any?>
    ){
        mGoodsCombinationSingleAdapter.bindItemView(
            holder,
            bean.newGroupPurchaseInfo!!,
            payloads
        )
    }

    /**
     * 加价购
     */
    private fun bindCombinationMultiItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean
    ) {
        val purchaseInfo = bean.additionalPurchaseInfo!!
        mGoodsCombinationMultiAdapter.mListener =  object : CombinedBuyListener {
            override fun preSettle(subPosition: Int) {
                AdapterUtils.preSettle(mContext, purchaseInfo)
                SearchProductReport.trackSearchPlaceOrderClick(mContext, purchaseInfo.mainProduct, purchaseInfo.subProducts, true)
            }

            override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                jumpToProductDetail(bean, holder)
                SearchProductReport.trackSearchGoodsClick(mContext, holder.bindingAdapterPosition, bean)
            }

            override fun changeNum(itemBean: RowsBeanCombinedExt,subPos:Int,addFlag: Boolean,preNum:Int) {
                if(itemBean.newQty == 0){
                    AdapterUtils.getAdditionalPurchaseInfo(purchaseInfo,this@SearchAdapter,true,holder.layoutPosition,false,null)
                }else{
                    AdapterUtils.checkCombinedProductNum(purchaseInfo,itemBean,-1,this@SearchAdapter,holder.layoutPosition,true,false)
                }
            }

            override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int,addFlag:Boolean, preNum: Int) {
                trackCombinationBtnClick(bean, curSubPosition, addFlag, preNum)
            }
        }
        mGoodsCombinationMultiAdapter.bindItemView(
            holder,
            bean.additionalPurchaseInfo!!)
    }
    private fun bindCombinationMultiItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean,
        payloads:List<Any?>
    ){
        mGoodsCombinationMultiAdapter.bindItemView(
            holder,
            bean.additionalPurchaseInfo!!,
            payloads)
    }

    /**
     * 推荐商品
     */
    private fun bindRecommendItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean
    ) {
        mGoodsSearchRecommendAdapter.mListener =  object : CombinedBuyListener {

            override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                jumpToProductDetail(bean, holder)
                SearchProductReport.trackSearchGoodsClick(mContext, holder.bindingAdapterPosition, bean)
            }

            override fun scrollHorinal(scrollPosition: Int, scrollOffset: Int) {
                mGoodsSearchRecommendAdapter.scrollPosition = scrollPosition
                mGoodsSearchRecommendAdapter.scrollOffset = scrollOffset
                super.scrollHorinal(scrollPosition, scrollOffset)
            }

            override fun btnClickTrack(buttonType: Int, buttonText: String, position: Int) {
                super.btnClickTrack(buttonType, buttonText, position)
                val rowsBeanInfo = bean.recommendInfo?.products?.get(position)
                when (buttonType) {
                    0 -> {
                        //加购无需处理，加购按钮会在控件中统一处理
//                        SearchProductReport.trackGroupCombinationBtnClickAddCart(mContext, position, rowsBeanInfo)
                    }
                    1 -> {
                        //拼团
                        SearchProductReport.trackGroupCombinationBtnClickSpellGroup(mContext, position, rowsBeanInfo)
                    }
                    2 -> {
                        //批购包邮
                        SearchProductReport.trackGroupCombinationBtnClickWholeSale(mContext, position, rowsBeanInfo)
                    }
                }
            }
        }
        mGoodsSearchRecommendAdapter.bindItemView(
            holder,
            bean.recommendInfo!!)
    }
    /**
     * 推荐商品
     */
    private fun bindRecommendItemView(
        holder: YBMBaseHolder,
        bean: SearchRowsBean,
        payloads:List<Any?>
    ) {
        mGoodsSearchRecommendAdapter.bindItemView(
            holder,
            bean.recommendInfo!!,
            payloads)
    }

    fun jumpToProductDetail(rowsBean: RowsBeanCombinedExt, holder: YBMBaseHolder) {
        XyyIoUtil.track("Test_Product_Action", rowsBean)
        var url =
            "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}&sourceType=${rowsBean.sourceType}&search_sort_strategy_id=${rowsBean.searchSortStrategyCode}&index=${holder.bindingAdapterPosition}"
        getOpenUrlNotJump(
            url,
            flowData
        )?.let {
            url = it
        }

        val mParams = Bundle().apply {
            putString(IntentCanst.PRODUCTID, rowsBean.id.toString())
            putString("nsid", rowsBean.nsid ?: "")
            putString("sdata", rowsBean.sdata ?: "")
            putString("sourceType", rowsBean.sourceType)
            putString("search_sort_strategy_id", rowsBean.searchSortStrategyCode)
            putInt("index", holder.adapterPosition)
            putBoolean(IntentCanst.SHOW_GROUPPURCHASE_FLAG, rowsBean.showGroupPurchase())
            putBoolean(IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG, rowsBean.showAdditinalPurchase())
        }

        //这里带参太多了 只能Intent跳了 不能用路由
        val intent = Intent(mContext, ProductDetailActivity::class.java)
        intent.putExtras(mParams)
        mContext.startActivity(intent)
    }

    /**
     * 商品曝光埋点
     */
    private fun traceGoodsExposure(rowsBean: RowsBean, holder: YBMBaseHolder) {
        if (flowData != null) {
            flowDataPageOPListExposureWithCode(
                    flowData,
                    rowsBean.productId,
                    rowsBean.showName,
                    "0",
                    rowsBean.searchSortStrategyCode,
                    "0",
                    rowsBean.operationExhibitionId,
                    rowsBean.operationId,
                    "${holder.bindingAdapterPosition}"
            )
        }
    }

    /**
     * 设置商品View背景圆角
     */
    private fun getGoodsViewItemBackgroundResId(holder: YBMBaseHolder): Int {
        val currPosition = holder.bindingAdapterPosition
        //是否是第一个
        val isFirst = currPosition == 0
        //是否是最后一个
        val isLast = mData.size == currPosition + 1
        //上一个item是否是运营位类型（非单品）
        val isTopCircle = if (!isFirst) {
            val preBean = mData[currPosition - 1] as SearchRowsBean
            preBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && preBean.operationInfo != null && (preBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || preBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        //下一个item是否是运营位类型（非单品）
        val isBottomCircle = if (!isLast) {
            val nextBean = mData[currPosition + 1] as SearchRowsBean
            nextBean.cardType == SEARCH_LIST_CARD_TYPE_OPERATION_POSITION && nextBean.operationInfo != null && (nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_SINGLE_SHOP || nextBean.operationInfo.showType == OPERATION_POSITION_TYPE_MULTI_SHOP)
        } else false
        return if (isTopCircle && isBottomCircle) R.drawable.shape_op_bg_radius_all
        else if (isTopCircle) R.drawable.shape_op_bg_radius_tl_tr
        else if (isBottomCircle) R.drawable.shape_op_bg_radius_bl_br
        else R.drawable.shape_op_bg_radius_nothing
    }

}