package com.ybmmarket20.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RefundOrderTimeBean

class TimeRangeAdapter(val resId: Int, timeRanges: List<RefundOrderTimeBean>) : YBMBaseAdapter<RefundOrderTimeBean>(resId, timeRanges) {
    var selectedItemPosition = 0

    // 新增方法：允许外部设置默认选中项
    fun setDefaultSelectedPosition(position: Int) {
        selectedItemPosition = position
        notifyDataSetChanged()
    }
    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: RefundOrderTimeBean) {
        val textView = baseViewHolder.getView<TextView>(R.id.text_view_time_range)
        textView.text = t.text
        textView.isSelected = baseViewHolder.position == selectedItemPosition
    }
}