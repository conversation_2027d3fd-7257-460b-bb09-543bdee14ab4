package com.ybmmarket20.adapter

import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.InvoiceEditHistoryBean

/**
 * 发票修改记录适配器
 * author: hcq
 * date: 2025/9/18
 */
class InvoiceEditHistoryAdapter(data: MutableList<InvoiceEditHistoryBean>) :
    YBMBaseAdapter<InvoiceEditHistoryBean>(R.layout.item_invoice_edit_history, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder, item: InvoiceEditHistoryBean) {
        // 设置发票类型
        baseViewHolder.setText(R.id.tv_invoice_type, item.invoiceType)

        // 设置发票详细信息
        baseViewHolder.setText(R.id.tv_company_name, item.companyName)
        baseViewHolder.setText(R.id.tv_tax_number, item.taxNumber)
        baseViewHolder.setText(R.id.tv_address, item.address)
        baseViewHolder.setText(R.id.tv_phone, item.phone)
        baseViewHolder.setText(R.id.tv_bank_name, item.bankName)
        baseViewHolder.setText(R.id.tv_bank_account, item.bankAccount)

        // 设置修改时间
        baseViewHolder.setText(R.id.tv_edit_time, item.editTime)

        // 设置审核状态描述
        baseViewHolder.setText(R.id.tv_status_desc, item.editStatusName)
    }
}
