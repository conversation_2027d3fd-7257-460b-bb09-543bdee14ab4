package com.ybmmarket20.adapter;

import android.view.View;

import com.ybmmarket20.view.MarqueeViewSpellGroup;

import java.util.ArrayList;
import java.util.List;

public abstract class MarqueeViewSpellGroupAdapter<T> {

    protected List<T> mDatas;

    private OnDataChangedListener mOnDataChangedListener;

    public MarqueeViewSpellGroupAdapter(List<T> datas) {
        if (datas == null) {
            datas = new ArrayList<>();
        }
        this.mDatas = datas;
    }

    public void setData(List<T> datas) {
        this.mDatas = datas;
        notifyDataChanged();
    }

    public int getItemCount() {
        return mDatas == null ? 0 : mDatas.size();
    }

    public abstract View onCreateView(MarqueeViewSpellGroup parent);

    public abstract void onBindView(View parent, View view, int position);

    public void setOnDataChangedListener(OnDataChangedListener onDataChangedListener) {
        mOnDataChangedListener = onDataChangedListener;
    }

    public void notifyDataChanged() {
        if (mOnDataChangedListener != null) {
            mOnDataChangedListener.onChanged();
        }
    }

    public interface OnDataChangedListener {
        void onChanged();
    }
}
