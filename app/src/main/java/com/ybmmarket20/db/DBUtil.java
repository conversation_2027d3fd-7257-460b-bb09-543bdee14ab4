package com.ybmmarket20.db;

public class DBUtil {
    //note:空格不要去掉
    public static final String SQL_CREATE_HEADER = "create table if not exists ";
    public static final String SQL_LEFT_BRACKET = "(";
    public static final String SQL_RIGHT_BRACKET = ")";
    public static final String SQL_COMMA_SYMBOL = ",";
    public static final String SQL_PRIMARY_KEY = " primary key ";
    public static final String SQL_INTEGER = " integer ";
    public static final String SQL_TEXT = " text ";
    public static final String SQL_NOT_NULL = " not null";
    public static final String SQL_TEXT_PRIMARY_KEY = " text primary key ";
    public static final String SQL_INTEGER_PRIMARY_KEY = " integer primary key autoincrement";
}
