package com.ybmmarket20.bean;

import java.util.List;

public class OrderEscortInfoBean {

    public String branchCode;
    public String contactor;
    public String createBy;
    public long createTime;
    public String distributorContact;
    public String distributorName;
    public String drugstoreName;
    public int id;
    public String imageUrl;
    public String merchantId;
    public String mobile;
    public int orderDetailId;
    public List<OrderEscortInvoiceListBean> orderEscortInvoiceList;
    public String orderNo;
    public long orderTime;
    public int productAmount;
    public String productName;
    public double productPrice;
    public int skuId;
    public String spec;
    public double subTotal;
    public long updateTime;
}
