package com.ybmmarket20.bean;

public class YBMPayEntity {
    // 请求后端支付接口的入参
    public String payTypeForFrontKey; //支付方式
    public String order_id;//订单号
    public String payRoute;//支付来源 0：下单流程支付，1：订单列表支付，3: 药学院（H5进来的，此来源是客户端自己定义的）不能为空
    public String payId;
    public String payChannel;//20 降级处理
    public String cardId; //银行卡支付时必传，这个是收银台获取的cardList里的cardId
    public String token; //银行卡支付校验密码后生成的，用于支付
    public String bankShowName;
    public String orderNo;
    public String reqScene;//settle:待确认订单页 cashier:收银台
    public String tranNo;
    public String bizCode;//农行支付状态

    //购物金 支付时需要带参start
    public String amount; //支付金额
    public String rechargeType;//充值类型，购物金充值 2 收银台购物金充值 3
    public boolean useVirtualGold;//新增字段 是否使用购物金：true 使用 false 不使用  勾选了是使用
    //end
}