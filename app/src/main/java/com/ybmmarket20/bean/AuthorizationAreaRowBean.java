package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class AuthorizationAreaRowBean implements Parcelable {
	private String applicant;
	private String content;
	private String createTime;
	private String id;
	private String merchantId;
	private String mobile;
	private int readFlag;
	private int status;
	private String title;

	public String getApplicant() {
		return applicant;
	}

	public void setApplicant(String applicant) {
		this.applicant = applicant;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public int getReadFlag() {
		return readFlag;
	}

	public void setReadFlag(int readFlag) {
		this.readFlag = readFlag;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	@Override
	public int describeContents() {
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeString(this.applicant);
		dest.writeString(this.content);
		dest.writeString(this.createTime);
		dest.writeString(this.id);
		dest.writeString(this.merchantId);
		dest.writeString(this.mobile);
		dest.writeInt(this.readFlag);
		dest.writeInt(this.status);
		dest.writeString(this.title);
	}

	public AuthorizationAreaRowBean() {
	}

	protected AuthorizationAreaRowBean(Parcel in) {
		this.applicant = in.readString();
		this.content = in.readString();
		this.createTime = in.readString();
		this.id = in.readString();
		this.merchantId = in.readString();
		this.mobile = in.readString();
		this.readFlag = in.readInt();
		this.status = in.readInt();
		this.title = in.readString();
	}

	public static final Parcelable.Creator<AuthorizationAreaRowBean> CREATOR = new Parcelable.Creator<AuthorizationAreaRowBean>() {
		@Override
		public AuthorizationAreaRowBean createFromParcel(Parcel source) {
			return new AuthorizationAreaRowBean(source);
		}

		@Override
		public AuthorizationAreaRowBean[] newArray(int size) {
			return new AuthorizationAreaRowBean[size];
		}
	};
}
