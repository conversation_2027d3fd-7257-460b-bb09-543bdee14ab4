package com.ybmmarket20.bean

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/4/7 17:14
 *    desc   :
 */
data class LoginAgreementBean(
    val privacyPolicy: PrivacyPolicyBean,
    val userServiceAgreement: PrivacyPolicyBean
)

data class PrivacyPolicyBean(
    val agreementName: String? = "",
    val agreementType: Int,
    val clientType: String? = "",
    val createTime: Long,
    val id: Int,
    val isActive: Int,
    val lang: String? = "",
    val url: String? = "",
    val version: String? = ""
)

data class PrivacyPolicyUploadBean(
    // 隐私政策id
    var privacyAgreementId: String? = "",
    // 用户服务协议id
    var userServiceAgreementId: String? = "",
    // 隐私政策版本
    var privacyAgreementVersion: String? = "",
    // 用户服务协议版本
    var userServiceAgreementVersion: String? = "",
    //登录手机号
    var mobile: String? = "",
    //勾选时间
    var checkTime: String? = "",
    //操作类型(1-注册 2-登录)
    var operateType: Int
)

data class PrivacyAgreementsBean(
    var agreementId: Int,
    var agreementVersion: String? = ""
)