package com.ybmmarket20.bean;

import java.util.List;

public class CommentDetailBean {

    /**
     * 订单信息
     */
    public Long id;

    /**
     * 商户编号
     */
    public Long merchantId;

    /**
     * 商户名称
     */
    public String merchantName;

    /**
     * 订单编号
     */
    public String orderNo;

    /**
     * 销售服务质量标签 21-吐槽；22-满意；23-超赞
     */
    public int saleServiceLabelProperty;

    /**
     * 销售服务质量标签
     */
    public String saleServiceLabel;

    public List<String> saleServiceLabelList;

    /**
     * 销售服务质量标签id集合,多个以,隔开
     */
    public String saleServiceLabelIds;

    /**
     * 销售服务评价
     */
    public String saleServiceText;

    /**
     * 销售服务评价附件地址
     */
    public List<String> saleServicePhotoUrl;

    /**
     * 客户服务标签属性 21-吐槽；22-满意；23-超赞
     */
    public int clientServiceLabelProperty;

    /**
     * 客户服务标签
     */
    public String clientServiceLabel;

    public List<String> clientServiceLabelList;

    /**
     * 客户服务标签id集合,多个以,隔开
     */
    public String clientServiceLabelIds;

    /**
     * 客户服务评价
     */
    public String clientServiceText;

    /**
     * 客户服务质量图片地址
     */
    public List<String> clientServicePhotoUrl;

    /**
     * 物流服务标签属性 21-吐槽；22-满意；23-超赞
     */
    public int transportServiceLabelProperty;

    /**
     * 物流服务标签
     */
    public String transportServiceLabel;

    /**
     * 物流服务标签id集合,多个以,隔开
     */
    public String transportServiceLabelIds;

    public List<String> transportServiceLabelList;


    /**
     * 物流服务评价
     */
    public String transportServiceText;

    /**
     * 物流服务质量图片地址
     */
    public List<String> transportServicePhotoUrl;


    /**
     * 是否需要联系 0不联系 1需要联系
     */
    public Integer needContact;

    /**
     * 称呼
     */
    public String appellation;

    /**
     * 联系方式
     */
    public String contactNo;

    /**
     * 性别 1.男  2.女
     */
    public int sex;

}
