package com.ybmmarket20.bean

import com.ybm.app.bean.AbstractMutiItemEntity

/**
 * 订单搜索提示列表项
 */
data class OrderSearchTipItem(
    var itemName: String?,
    var field: String?,
    var param: String?,
    var tip: String?
)

data class AggregationItem(
    var title: String?,
    var tips: String?,
    var items: List<OrderSearchTipItem>?
)

data class OrderSearchTipBean(
    var aggregationItemList: List<AggregationItem>?,
    var itemList: List<OrderSearchTipItem>?
)

data class OrderSearchTipEntry(
    var tipType: Int,
    var tipList: List<OrderSearchTipItem>?,
    var item: OrderSearchTipItem?
): AbstractMutiItemEntity() {
    override fun getItemType(): Int = tipType
}