package com.ybmmarket20.bean.cms;

import java.util.List;
import java.util.Objects;

public class ModuleStyles {

    public int height;//模块高度
    public List<Integer> margin;// [left,top,rigth,bottom]  外边距 单位dp | int型数组
    public List<Integer> padding;// [left,top,rigth,bottom] 内边距 单位dp       | int型数组

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ModuleStyles that = (ModuleStyles) o;

        boolean equalMain = false;
        boolean equalPadding = false;
        if (margin == null && that.margin == null) {
            equalMain = true;
        }
        if (padding == null && that.padding == null) {
            equalPadding = true;
        }

        if ((margin != null && margin.equals(that.margin))) {
            equalMain = true;
        }


        if (padding != null && padding.equals(that.padding)) {
            equalPadding = true;
        }


        return height == that.height && equalMain && equalPadding;

    }

    @Override
    public int hashCode() {
        return Objects.hash(height, margin, padding);
    }
}
