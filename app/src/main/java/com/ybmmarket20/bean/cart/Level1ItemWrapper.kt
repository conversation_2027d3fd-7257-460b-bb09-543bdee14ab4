package com.ybmmarket20.bean.cart

import android.text.SpannableStringBuilder
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.ybmmarket20.bean.LabelIconBean
import com.ybmmarket20.bean.RangePriceBean
import com.ybmmarket20.bean.SpecialtyCouponTag
import com.ybmmarket20.bean.cart.CartItemBean.LimitedTimeSupplement
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.home.CartItemAdapter
import com.ybmmarket20.home.CartItemAdapter3

abstract class Level1ItemWrapper : MultiItemEntity {

    var amount: String? = null                          // 购买数量
    var canSplit: Boolean = false                       // 是否可拆零
    var mediumPackageNum: Int = 1                       // 中包装数量
    var selected = false

    override fun getItemType() = 1
}


abstract class Level1ItemGoodsBeanAbs : Level1ItemWrapper() {
    var skuid: String? = null
    var name: SpannableStringBuilder? = null
    var invalidStatus: String? = null
    var spec: String? = null
    var unitPrice: Double = 0.0
    var price: SpannableStringBuilder? = null
    var tagList: List<LabelIconBean?>? = null
    var tagWholeOrderList: List<LabelIconBean>? = null
    var effect: String? = null
    var actPurchaseTip: String? = null//智能补贴提示
    var markerUrl: String? = null           //标签url
        get() {
            return field?.let { AppNetConfig.LORD_TAG + field }
        }
    var imageUrl: String? = null            //图片url
        get() {
            return field?.let { AppNetConfig.LORD_IMAGE + field }
        }

    var subtotal: SpannableStringBuilder? = null            // 小计
    var showPriceAfterDiscount: String? = null  // 折后价
    var dataTagList: List<LabelIconBean>? = null //数据标签 如比加入时降xxx
    var purchaseLimitStr: String? = null    // 限购文案
    var purchaseLimitTittle: String? = null    // 限购文案头
    var seckillRemainingTime: Long = 0L    // 秒杀剩余时间
    var responseLocalTime: Long = 0L       // 购物车刷新数据时间

    var packageProductQty = 0               //  套餐中每个商品的数量
    var isPersonalShop: Boolean = false     //  是否是自然人店铺
    var gift: Boolean = false //是否是赠品
    var rangePriceBean: RangePriceBean? = null//普通品阶梯价

    var tagTitle : String? = null // 商品包邮tag
    var specialtyCouponTag: SpecialtyCouponTag? = null //券信息
    var limitedTimeSupplement: LimitedTimeSupplement? = null

    fun getProdId(): Long {
        return try {
            skuid?.toLong()?: 0L
        }catch (e: Exception) {
            e.printStackTrace()
            0L
        }
    }
}

abstract class Level1InvalidItemGoodsBeanAbs : Level0ItemWrapper() {
    var name: SpannableStringBuilder? = null
    var skuid: String? = null
    var imageUrl: String? = null
        get() {
            return field?.let { AppNetConfig.LORD_IMAGE + field }
        }
    var markerUrl: String? = null           //标签url
        get() {
            return field?.let { AppNetConfig.LORD_TAG + field }
        }
    var groupGoodsNum: String? = null
    var invalidStatus: String? = null
    var invalidContent: String? = null
}

class Level1ItemSubShopHeaderBean : Level1ItemWrapper() {
    var activityId: String? = null
    var companyName: String? = null
    var shopJumpUrl: String? = null
    var shopCode: String? = null
    var id: String? = null
    var isThirdCompany: Boolean = false
    var orgId: String? = null
    var isHaveVoucher: Boolean = false
    var skuids: String? = null              // 该店铺下的商品id组合，用来获取优惠券
    override fun getItemType(): Int = CartItemAdapter.level1_sub_shop_header
}

class Level1ItemCommonGoodsBean : Level1ItemGoodsBeanAbs() {
    var activityBean: Level1ItemActivityHeaderBean? = null

    override fun getItemType(): Int = CartItemAdapter.level1_common_good
}

class Level1ItemActivityHeaderBean : Level1ItemWrapper() {

    var type: Int? = 1                      //活动类型 1:满减 2:满折 3:满赠 4:满减赠 9-不参与活动，10-套餐，5:大礼包 6 一口价 新增11:满返; 12:满减返券
    var combinationType: Int? = 1           // 1:单品满减;  2组合满减
    var activityTypeText: String? = null    // 单品满减，组合满减，一口价等等

    var title: String? = null
    var titleUrl: String? = null
    var titleUrlText: String? = null

    var shopCode: String? = null
    var activityId: String? = null
    var activityType: String? = null

    override fun getItemType(): Int = CartItemAdapter.level1_activity_header
}

class Level1ItemActivityGiftSelectBean: Level1ItemWrapper() {
    var content:String? = ""
    var cartShoppingGroupFrontBean: CartShoppingGroupFrontBean? = null
    override fun getItemType(): Int = CartItemAdapter3.level1_activity_good_gift_select

    fun isSelected():Boolean{
        return content=="已选赠品"
    }
}

class Level1ItemActivityGoodHeaderBean : Level1ItemGoodsBeanAbs() {
    override fun getItemType(): Int = CartItemAdapter.level1_activity_good_header
}

open class Level1ItemActivityGoodBean : Level1ItemGoodsBeanAbs() {
    var combinationType = 1
    override fun getItemType(): Int = CartItemAdapter.level1_activity_good
}

class Level1ItemActivityGoodGiftBean : Level1ItemActivityGoodBean() {
    var activityId: String? = null
    var productUnit: String? = null
    var priceNum: Double? = null
    var priceDesc: String? = null
    override fun getItemType(): Int = CartItemAdapter3.level1_group_goods_gift
}

class Level1ItemActivityGoodEndBean : Level1ItemGoodsBeanAbs() {
    override fun getItemType(): Int = CartItemAdapter.level1_activity_good_end
}

class Level1ItemGroupHeaderBean : Level1ItemWrapper() {
    override fun getItemType(): Int = CartItemAdapter.level1_group_header
    var origPrice: String? = null   //套餐原价
    var price: String? = null       //当前售价
    var packageId: String? = null       //
    var realPrice: Float = 0f //套餐真实价格
}

class Level1ItemGroupGoodBean : Level1ItemGoodsBeanAbs() {
    var origPrice: String? = null   //套餐原价
    var packageId: String? = null       //
    override fun getItemType(): Int = CartItemAdapter.level1_group_good
}

class Level1ItemGroupFooterBean : Level1ItemWrapper() {
    var subtotal: String? = null    //小计
    var packageId: String? = null      //
    var realPrice: Float = 0f //套餐真实价格
    var rangePriceBean: RangePriceBean? = null
    override fun getItemType(): Int = CartItemAdapter.level1_group_footer
}


// ========= 失效这几个比较特殊，不能折叠

class Level1InvalidGoodBean : Level1InvalidItemGoodsBeanAbs() {
    override fun getItemType(): Int = CartItemAdapter.level1_Invalid_good
}

class Level1InvalidGroupHeaderBean : Level0ItemWrapper() {
    var origPrice: String? = null   //套餐原价
    var price: String? = null       //当前售价
    var packageId: String? = null       //
    override fun getItemType(): Int = CartItemAdapter.level1_Invalid_group_header
}

class Level1InvalidGroupGoodBean(
    var packageId: String? = null
) : Level1InvalidItemGoodsBeanAbs() {
    override fun getItemType(): Int = CartItemAdapter.level1_Invalid_group_good
}

/**
 * 无效商品信息
 */
class CollectionInvalidGoodsInfo(
    var packageState: Int = INVALID_GOODS_ALL, //是否是无效套餐商品
    var packageId: String = "",//套餐id
    var ids: String? // 商品id （xxx,xxx）
)

const val INVALID_GOODS_ALL = 0    //所有无效商品（无效套餐 + 无效商品）
const val INVALID_GOODS_PACKAGE = 1//无效套餐
const val INVALID_GOODS_NORMAL = 3 //无效商品