package com.ybmmarket20.bean.cart;

import com.google.gson.annotations.SerializedName;
import com.ybm.app.bean.AbstractMutiItemEntity;
import com.ybmmarket20.bean.CartActivityBean;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.OemModelRespBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.RangePriceBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.SpecialtyCouponTag;

import java.util.List;

/*
 * 购物车商品item
 * */
public class CartItemBean extends AbstractMutiItemEntity {

    private static final long serialVersionUID = 2851867974535496989L;

    public static final int content_normal = 0;                 //内容样式1-正常条目布局
    public static final int content_combo = 1;                  //内容样式2-套餐条目布局
    public static final int content_end = 2;                    //内容样式3-最后一个商品item布局
    public static final int content_commodity = 3;              //内容样式4-失效商品
    public static final int content_invalid_commodity_end = 5;  //内容样式5-失效最后一个商品
    public static final int content_preferential_combo = 7;     //内容样式7-满减条目布局
    public static final int content_preferential_combo_end = 8; //内容样式8-满减最后一个商品item布局
    public static final int content_preferential_end = 9;       //内容样式9-最后一个商品item布局

    public static final int header_lose_efficacy = 10;          //头部样式1-失效商品头部
    public static final int header_money_off = 11;              //头部样式2-满减头部样式
    public static final int header_combo_head = 12;             //头部样式3-套餐头部
    public static final int header_combo_end = 13;              //头部样式4-套餐尾部
    public static final int header_lose_combo_head = 14;        //头部样式5-失效套餐头部
    public static final int header_combo_lose_end = 18;         //头部样式8-失效商品尾部
    public static final int header_proprietary_head = 15;       //头部样式6-自营和非自营（POP）头部
    public static final int header_shop_head = 17;              //头部样式7-显示商城样式头部
    public static final int header_return_ticket_head = 19;     //头部样式9-显示返券头部

    public static final int header_recommend = 20;              //猜你喜欢头部样式
    public static final int content_recommend = 21;             //猜你喜欢内容样式
    public RowsBean rowsBean;

    private int amount;// 购买数量
    private double balanceAmount = 0.00;//预返余额明细
    private String blackSkuText; //是否返点 有就提示
    private String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    private String costPrice;//成本价：成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-返利；
    private double discount;//套餐优惠
    private double discountAmount;//单品优惠金额, 单品优惠总金额 = 单品优惠金额 + 抵扣余额明细
    private boolean gift;//享礼
    private String imageUrl;//图片
    private int isSplit;//是否可拆零 0:不可拆零；1:可拆零 默认1
    private String loseTagText;//失效商品，是否售罄或者下架文案
    private String markerUrl;
    private int mediumPackageNum;       //中包装数量 默认为1
    public int packageProductQty;       //套餐中每个商品的数量
    private String mediumPackageTitle;  //中包装显示在加减上面的文案  中包装：500
    private String name;//商品名称
    private boolean newUser;
    private int packageId;//套餐id
    private double price;//当前售价
    private double purchasePrice;//入库价格
    private double realPayAmount;//单品实付金额
    public long seckillRemainingTime;//秒杀品剩余时间
    private ProductDetailBean sku;
    private int skuId;//商品id
    @SerializedName("preferentialPrice")
    public String showPriceAfterDiscount;   // 折后价
    private int skuStatus;//2是已售罄//4是已下架
    private String spec;//规格
    private int status; //0-未选中，1-选中
    private String stockTitle;
    private double subtotal;//小计
    private List<LabelIconBean> tagList;
    private List<LabelIconBean> tagWholeOrderList;
    private String tagTitle;
    private SpecialtyCouponTag  specialtyCouponTag;
    private double useBalanceAmount;//抵扣余额明细
    private String validity;//临期文案
    public String nearEffect;// 效期
    public String actPurchaseTip;// 智能补贴提示
    private boolean isGroup = false;//group是否被选中
    private double origPrice;//套餐原价

    /*--------------------暂定----------------------*/
    private int id;
    private double downPrice;
    private String promotionTag;
    private String isSplitTitle;//中包装是否可拆零提示文案
    private int agent;////是否独家：0否，1是
    private int isUsableMedicalStr;//是否医保：0否，1是
    private OemModelRespBean oemModelResp;//OEM

    /*------------------公司属性--------------------*/
    private String companyCode;//公司编码
    private String companyName;//公司名称
    private String shopJumpUrl;//跳转店铺页面
    private int companyType;//公司类型
    private boolean iscompanyGroup = false;//group是否被选中
    private int parentPostPos;
    //下面已定义private String freightTips;//已包邮|还差xx元包邮
    // 下面已定义private int freightTipsShowStatus;//是否展示运费提示语1：展示; 0：不展示
    private String freightUrlText;//去凑单连接文字："去凑单"
    private int freightIconShowStatus;  //是否展示运费！icon 1展示 0不展示
    private String freightJumpUrl;  //为去凑单跳转url

    /*------------------店铺属性--------------------*/
    private String shopCode;//店铺编码
    private String shopName;//店铺名称
    private String shopType;//店铺类型
    private String mainShopCode;    //卖家shopcode
    private int isHaveVoucher;//是否有店铺优惠券 0无 1有
    private String voucherUrl;
    private String appLinkUrl;//跳转shop商城
    private CartActivityBean returnVoucherInfo;//优惠券信息
    private int isMatch;//  是否满足条件  0 满足 1 不满足
    public String originalShopCode;

    /*----------------活动商品组属性-------------------*/
    private int isThirdCompany;//是否第三方厂家（0：否；1：是）
    private int productTotalNum;//商品的总数量
    private int productVarietyNum;//商品品种,共几件
    private double totalAmount;//订单总金额

    /*-------------------商品组属性--------------------*/
    private String title;               //显示
    private String titleUrl;            //title标题APP跳转链接
    private String titleUrlText;        //title标题跳转链接的文案
    private int type;                   //活动类型 1:满减 2:满折 3:满赠 4:满减赠 9-不参与活动，10-套餐，5:大礼包 6 一口价 新增11:满返; 12:满减返券
    public int combinationType;        // 1:单品;2组合
    public String activityTypeText;    // 单品满减，组合满减，一口价等等
    public int valid;//0=失效商品，1=正常商品
    private String orgId;//卖家ID自营或非自营勾选的id

    //skuids 店铺下的商品集合
    private List<CartItemBean> shopItemList;

    private int normalQty;//促销可超出限购数量时，包含购买的商品普通数量
    private int promoQty; //促销可超出限购数量时，包含购买的商品优惠数量

    public int freightTipsShowStatus;//控制是否展示运费提示语, 0: 不展示；1：展示
    public String freightTips;//提示语
    public List<LabelIconBean> dataTagList;//数据标签

    public String priceDesc;

    private LimitedTimeSupplement limitedTimeSupplement;

    public LimitedTimeSupplement getLimitedTimeSupplement() {
        return limitedTimeSupplement;
    }

    public void setLimitedTimeSupplement(LimitedTimeSupplement limitedTimeSupplement) {
        this.limitedTimeSupplement = limitedTimeSupplement;
    }

    @SerializedName("levelPriceDTO")
    public RangePriceBean rangePriceBean;//普通品阶梯价

    public int getNormalQty() {
        return normalQty;
    }

    public void setNormalQty(int normalQty) {
        this.normalQty = normalQty;
    }

    public int getPromoQty() {
        return promoQty;
    }

    public void setPromoQty(int promoQty) {
        this.promoQty = promoQty;
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public double getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(double balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public String getBlackSkuText() {
        return blackSkuText;
    }

    public void setBlackSkuText(String blackSkuText) {
        this.blackSkuText = blackSkuText;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(String costPrice) {
        this.costPrice = costPrice;
    }

    public double getDiscount() {
        return discount;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public boolean isGift() {
        return gift;
    }

    public void setGift(boolean gift) {
        this.gift = gift;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(int isSplit) {
        this.isSplit = isSplit;
    }

    public String getLoseTagText() {
        return loseTagText;
    }

    public void setLoseTagText(String loseTagText) {
        this.loseTagText = loseTagText;
    }

    public String getMarkerUrl() {
        return markerUrl;
    }

    public void setMarkerUrl(String markerUrl) {
        this.markerUrl = markerUrl;
    }

    public int getMediumPackageNum() {
        return mediumPackageNum;
    }

    public void setMediumPackageNum(int mediumPackageNum) {
        this.mediumPackageNum = mediumPackageNum;
    }

    public String getMediumPackageTitle() {
        return mediumPackageTitle;
    }

    public void setMediumPackageTitle(String mediumPackageTitle) {
        this.mediumPackageTitle = mediumPackageTitle;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isNewUser() {
        return newUser;
    }

    public void setNewUser(boolean newUser) {
        this.newUser = newUser;
    }

    public int getPackageId() {
        return packageId;
    }

    public void setPackageId(int packageId) {
        this.packageId = packageId;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(double purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public double getRealPayAmount() {
        return realPayAmount;
    }

    public void setRealPayAmount(double realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

    public ProductDetailBean getSku() {
        return sku;
    }

    public void setSku(ProductDetailBean sku) {
        this.sku = sku;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public int getSkuStatus() {
        return skuStatus;
    }

    public void setSkuStatus(int skuStatus) {
        this.skuStatus = skuStatus;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getStockTitle() {
        return stockTitle;
    }

    public void setStockTitle(String stockTitle) {
        this.stockTitle = stockTitle;
    }

    public double getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }

    public List<LabelIconBean> getTagList() {
        return tagList;
    }

    public void setTagList(List<LabelIconBean> tagList) {
        this.tagList = tagList;
    }

    public List<LabelIconBean> getTagWholeOrderList() {
        return tagWholeOrderList;
    }

    public void setTagWholeOrderList(List<LabelIconBean> tagWholeOrderList) {
        this.tagWholeOrderList = tagWholeOrderList;
    }

    public String getTagTitle() {
        return tagTitle;
    }

    public void setTagTitle(String tagTitle) {
        this.tagTitle = tagTitle;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public double getUseBalanceAmount() {
        return useBalanceAmount;
    }

    public void setUseBalanceAmount(double useBalanceAmount) {
        this.useBalanceAmount = useBalanceAmount;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getValidity() {
        return validity;
    }

    public void setValidity(String validity) {
        this.validity = validity;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getShopJumpUrl() {
        return shopJumpUrl;
    }

    public void setShopJumpUrl(String shopJumpUrl) {
        this.shopJumpUrl = shopJumpUrl;
    }

    public int getCompanyType() {
        return companyType;
    }

    public void setCompanyType(int companyType) {
        this.companyType = companyType;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleUrl() {
        return titleUrl;
    }

    public void setTitleUrl(String titleUrl) {
        this.titleUrl = titleUrl;
    }

    public String getTitleUrlText() {
        return titleUrlText;
    }

    public void setTitleUrlText(String titleUrlText) {
        this.titleUrlText = titleUrlText;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getMainShopCode() {
        return mainShopCode;
    }

    public void setMainShopCode(String mainShopCode) {
        this.mainShopCode = mainShopCode;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getDownPrice() {
        return downPrice;
    }

    public void setDownPrice(double downPrice) {
        this.downPrice = downPrice;
    }

    public String getPromotionTag() {
        return promotionTag;
    }

    public void setPromotionTag(String promotionTag) {
        this.promotionTag = promotionTag;
    }

    public String getIsSplitTitle() {
        return isSplitTitle;
    }

    public void setIsSplitTitle(String isSplitTitle) {
        this.isSplitTitle = isSplitTitle;
    }

    public int getAgent() {
        return agent;
    }

    public void setAgent(int agent) {
        this.agent = agent;
    }

    public int getIsUsableMedicalStr() {
        return isUsableMedicalStr;
    }

    public void setIsUsableMedicalStr(int isUsableMedicalStr) {
        this.isUsableMedicalStr = isUsableMedicalStr;
    }

    public OemModelRespBean getOemModelResp() {
        return oemModelResp;
    }

    public void setOemModelResp(OemModelRespBean oemModelResp) {
        this.oemModelResp = oemModelResp;
    }

    public boolean isGroup() {
        return isGroup;
    }

    public void setGroup(boolean group) {
        isGroup = group;
    }

    public boolean isCompanyType() {
        return companyType == 0;
    }

    public double getOrigPrice() {
        return origPrice;
    }

    public void setOrigPrice(double origPrice) {
        this.origPrice = origPrice;
    }

    public int getIsHaveVoucher() {
        return isHaveVoucher;
    }

    public void setIsHaveVoucher(int isHaveVoucher) {
        this.isHaveVoucher = isHaveVoucher;
    }

    public String getVoucherUrl() {
        return voucherUrl;
    }

    public void setVoucherUrl(String voucherUrl) {
        this.voucherUrl = voucherUrl;
    }

    public int getIsThirdCompany() {
        return isThirdCompany;
    }

    public void setIsThirdCompany(int isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public int getProductTotalNum() {
        return productTotalNum;
    }

    public void setProductTotalNum(int productTotalNum) {
        this.productTotalNum = productTotalNum;
    }

    public int getProductVarietyNum() {
        return productVarietyNum;
    }

    public void setProductVarietyNum(int productVarietyNum) {
        this.productVarietyNum = productVarietyNum;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public boolean isIscompanyGroup() {
        return iscompanyGroup;
    }

    public void setIscompanyGroup(boolean iscompanyGroup) {
        this.iscompanyGroup = iscompanyGroup;
    }

    public List<CartItemBean> getShopItemList() {
        return shopItemList;
    }

    public void setShopItemList(List<CartItemBean> shopItemList) {
        this.shopItemList = shopItemList;
    }

    public int getParentPostPos() {
        return parentPostPos;
    }

    public void setParentPostPos(int parentPostPos) {
        this.parentPostPos = parentPostPos;
    }

    public String getAppLinkUrl() {
        return appLinkUrl;
    }

    public void setAppLinkUrl(String appLinkUrl) {
        this.appLinkUrl = appLinkUrl;
    }

    public CartActivityBean getReturnVoucherInfo() {
        return returnVoucherInfo;
    }

    public void setReturnVoucherInfo(CartActivityBean returnVoucherInfo) {
        this.returnVoucherInfo = returnVoucherInfo;
    }

    public int getIsMatch() {
        return isMatch;
    }

    public void setIsMatch(int isMatch) {
        this.isMatch = isMatch;
    }

    public String getFreightUrlText() {
        return freightUrlText;
    }

    public void setFreightUrlText(String freightUrlText) {
        this.freightUrlText = freightUrlText;
    }

    public String getFreightJumpUrl() {
        return freightJumpUrl;
    }

    public void setFreightJumpUrl(String freightJumpUrl) {
        this.freightJumpUrl = freightJumpUrl;
    }

    public int getFreightTipsShowStatus() {
        return freightTipsShowStatus;
    }

    public void setFreightTipsShowStatus(int freightTipsShowStatus) {
        this.freightTipsShowStatus = freightTipsShowStatus;
    }

    public String getFreightTips() {
        return freightTips;
    }

    public void setFreightTips(String freightTips) {
        this.freightTips = freightTips;
    }

    public int getFreightIconShowStatus() {
        return freightIconShowStatus;
    }

    public void setFreightIconShowStatus(int freightIconShowStatus) {
        this.freightIconShowStatus = freightIconShowStatus;
    }

    public SpecialtyCouponTag getSpecialtyCouponTag() {
        return specialtyCouponTag;
    }

    public void setSpecialtyCouponTag(SpecialtyCouponTag specialtyCouponTag) {
        this.specialtyCouponTag = specialtyCouponTag;
    }

    public class LimitedTimeSupplement{

        private Double costPrice;
        private Long remainingTime;
        private Long responseLocalTime;
        private Double subTotal;

        public Double getSubTotal() {
            return subTotal;
        }

        public void setSubTotal(Double subTotal) {
            this.subTotal = subTotal;
        }

        public Long getResponseLocalTime() {
            return responseLocalTime;
        }

        public void setResponseLocalTime(Long responseLocalTime) {
            this.responseLocalTime = responseLocalTime;
        }

        public Double getCostPrice() {
            return costPrice;
        }

        public void setCostPrice(Double costPrice) {
            this.costPrice = costPrice;
        }

        public Long getRemainingTime() {
            return remainingTime;
        }

        public void setRemainingTime(Long remainingTime) {
            this.remainingTime = remainingTime;
        }
    }
}
