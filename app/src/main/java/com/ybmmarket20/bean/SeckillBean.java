package com.ybmmarket20.bean;

import androidx.annotation.Keep;

import java.util.List;

/*
 * 拼单
 * */
@Keep
public class SeckillBean {

    public int status;              // 秒杀状态:0-未开始 1-进行中 2-已结束
    public double skPrice;          //秒杀价格
    public int percentage;          //已售百分比
    public long currentTime;         //服务器当前时间，单位毫秒，时间戳
    public long startTime;           //秒杀活动开始时间，单位毫秒，时间戳
    public long endTime;             //秒杀活动结束时间，单位毫秒，时间戳
    //private long surplusTime;        //秒杀倒计时时间，包括预热倒计时和秒杀倒计时两种

    public long getSurplusTime() {
        if (status == 0) {
            return Math.max((startTime - currentTime), 0);
        } else if (status == 1) {
            return Math.max((endTime - currentTime), 0);
        } else {
            return -1;
        }
    }

    public long responseLocalTime = 0L;  //获取到秒杀信息的本地时间, 单位ms
}
