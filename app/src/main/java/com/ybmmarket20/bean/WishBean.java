package com.ybmmarket20.bean;

/**
 * Created by asus on 2016/3/21.
 */
public class WishBean {

    public WishBean(String showName) {
        this.showName = showName;
    }

    public int id;//商品id
    public String code;
    public String showName; //药品名称
    public String manufacturer;//厂家
    public String spec;//规格
    public String url;//
    public long createTime;//提交时间
    public long updateTime;
    public int status;//1:未完成，2:已完成
    public int isRead;//
    public String statusName;//状态说明
    public String creator;//
    public String updator;//
    public String remark = "";//
    public String realName = "";//
    public String productCode = "";//
    public ProductDetailBean product;//

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        WishBean wishBean = (WishBean) o;

        return id == wishBean.id;

    }

    @Override
    public int hashCode() {
        return id;
    }
}
