package com.ybmmarket20.bean;

import java.util.List;

/**
 * Created by ybm on 2017/6/22.
 */

public class ManufacturerBean extends BaseBean {

    private List<ManufacturerListBean> manufacturerList;

    public List<ManufacturerListBean> getManufacturerList() {
        return manufacturerList;
    }

    public void setManufacturerList(List<ManufacturerListBean> manufacturerList) {
        this.manufacturerList = manufacturerList;
    }

    public static class ManufacturerListBean {

        private String manufacturer;

        public String getManufacturer() {
            return manufacturer;
        }

        public void setManufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
        }
    }
}
