package com.ybmmarket20.bean.payment;


import com.ybmmarket20.bean.cart.CartItemBean;

import java.util.List;

public class PaymentSortedBean {

    private CartItemBean item;
    private int itemType; //item类型   3=套餐，其他不用判断
    private List<CartItemBean> subItemList;//套餐集合子item

    public CartItemBean getItem() {
        return item;
    }

    public void setItem(CartItemBean item) {
        this.item = item;
    }

    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public List<CartItemBean> getSubItemList() {
        return subItemList;
    }

    public void setSubItemList(List<CartItemBean> subItemList) {
        this.subItemList = subItemList;
    }

}
