package com.ybmmarket20.bean

/**
 * 发票修改记录数据模型
 * author: hcq
 * date: 2025/9/18
 */
data class InvoiceEditHistoryBean(
    val id: String = "",
    val orderId: String = "",
    val orderNo: String = "",
    val editTime: String = "",
    val editStatus: Int = 0, // 1-待审核 2-审核通过 3-审核拒绝
    val editStatusName: String = "",
    val operator: String = "",
    val remark: String = "",
    // 发票信息详情
    val invoiceType: String = "", // 发票类型
    val companyName: String = "", // 公司名称
    val taxNumber: String = "", // 纳税人识别号
    val address: String = "", // 地址
    val phone: String = "", // 电话
    val bankName: String = "", // 开户银行
    val bankAccount: String = "" // 银行账号
)
