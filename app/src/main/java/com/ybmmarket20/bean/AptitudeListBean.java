package com.ybmmarket20.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 资质
 */
public class AptitudeListBean implements Serializable {

    /**
     * licenseStatus : 2
     * retPage : {"currentPage":1,"limit":10,"offset":1,"pageCount":1,"rows":[{"audit1Status":1,"audit2Status":0,"code":"ZG20190912140209070","createTime":1568268129000,"deleteStatus":1,"firstLicenseType":2,"holder":"","id":38321,"licenseAuditLogs":[{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568286753000,"id":183491,"remark":"啊来得及拉到反反复复方法","status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568275024000,"creator":"管理员","creatorJob":"023","id":183489,"remark":"","status":1,"statusName":"审核通过","type":1,"typeName":"一审"},{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568268129000,"id":183488,"remark":"啊来得及拉到","status":0,"statusName":"提交","type":0,"typeName":"开始"}],"merchantId":32585,"recoveryStatus":0,"remark":"啊来得及拉到反反复复方法","remark1":"","statusCode":3,"statusName":"二审中","type":2,"updateTime":1568291089000},{"audit1Status":-1,"audit2Status":0,"code":"ZG20180605111021733","createTime":1528168222000,"deleteStatus":0,"holder":"CRM-DEV","id":2,"licenseAuditLogs":[{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1533172227000,"creator":"陈淑馨","creatorJob":"80080786","id":40641,"remark":"该店已建档，请勿重复上传","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1533140296000,"creator":"李金玉","creatorJob":"80080778","id":40623,"status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1530754031000,"creator":"陈淑馨","creatorJob":"80080786","id":11369,"remark":"该店已建档，请勿重复上传","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1530718369000,"creator":"贾智","creatorJob":"80080860","id":11329,"status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1529395651000,"creator":"王博","creatorJob":"8011","id":1118,"remark":"无","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1528168221000,"creator":"向崧源","creatorJob":"80080724","id":2,"status":0,"statusName":"提交","type":0,"typeName":"开始"}],"merchantId":32585,"recoveryStatus":0,"remark1":"该店已建档，请勿重复上传","statusCode":7,"statusName":"已作废","type":1,"updateTime":1564019690000}],"total":9}
     * url : /ybm/xyy_app_template_img/xyy_app_template_img.png
     */

    private int licenseStatus;
    //private String firstLicenseType;//首营资质类型（废弃 改用客户类型customerType）
    private String customerType;//新的客户类型字段 替代firstLicenseType
    private RetPageBean retPage;//废弃 接口不支持分页了
    private String url;
    private int isShow;//为1显示 非1不显示
    private String licenseSwitch;
    private int licenseDownStatus;//查看小药药资质 1有权限 0无权限
    private List<AptitudeBean> licenseAuditList;

    public List<AptitudeBean> getLicenseAuditList() {
        return licenseAuditList;
    }

    public void setLicenseAuditList(List<AptitudeBean> licenseAuditList) {
        this.licenseAuditList = licenseAuditList;
    }

    public int getLicenseDownStatus() {
        return licenseDownStatus;
    }

    public void setLicenseDownStatus(int licenseDownStatus) {
        this.licenseDownStatus = licenseDownStatus;
    }

    public String getLicenseSwitch() {
        return licenseSwitch;
    }

    public void setLicenseSwitch(String licenseSwitch) {
        this.licenseSwitch = licenseSwitch;
    }

//    public String getFirstLicenseType() {
//        return firstLicenseType;
//    }
//
//    public void setFirstLicenseType(String firstLicenseType) {
//        this.firstLicenseType = firstLicenseType;
//    }

    public int getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(int licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public RetPageBean getRetPage() {
        return retPage;
    }

    public void setRetPage(RetPageBean retPage) {
        this.retPage = retPage;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getIsShow() {
        return isShow;
    }

    public void setIsShow(int isShow) {
        this.isShow = isShow;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public static class RetPageBean {
        /**
         * currentPage : 1
         * limit : 10
         * offset : 1
         * pageCount : 1
         * rows : [{"audit1Status":1,"audit2Status":0,"code":"ZG20190912140209070","createTime":1568268129000,"deleteStatus":1,"firstLicenseType":2,"holder":"","id":38321,"licenseAuditLogs":[{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568286753000,"id":183491,"remark":"啊来得及拉到反反复复方法","status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568275024000,"creator":"管理员","creatorJob":"023","id":183489,"remark":"","status":1,"statusName":"审核通过","type":1,"typeName":"一审"},{"auditCode":"ZG20190912140209070","auditId":38321,"createTime":1568268129000,"id":183488,"remark":"啊来得及拉到","status":0,"statusName":"提交","type":0,"typeName":"开始"}],"merchantId":32585,"recoveryStatus":0,"remark":"啊来得及拉到反反复复方法","remark1":"","statusCode":3,"statusName":"二审中","type":2,"updateTime":1568291089000},{"audit1Status":-1,"audit2Status":0,"code":"ZG20180605111021733","createTime":1528168222000,"deleteStatus":0,"holder":"CRM-DEV","id":2,"licenseAuditLogs":[{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1533172227000,"creator":"陈淑馨","creatorJob":"80080786","id":40641,"remark":"该店已建档，请勿重复上传","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1533140296000,"creator":"李金玉","creatorJob":"80080778","id":40623,"status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1530754031000,"creator":"陈淑馨","creatorJob":"80080786","id":11369,"remark":"该店已建档，请勿重复上传","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1530718369000,"creator":"贾智","creatorJob":"80080860","id":11329,"status":0,"statusName":"提交","type":0,"typeName":"开始"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1529395651000,"creator":"王博","creatorJob":"8011","id":1118,"remark":"无","status":2,"statusName":"驳回","type":1,"typeName":"一审"},{"auditCode":"ZG20180605111021733","auditId":2,"createTime":1528168221000,"creator":"向崧源","creatorJob":"80080724","id":2,"status":0,"statusName":"提交","type":0,"typeName":"开始"}],"merchantId":32585,"recoveryStatus":0,"remark1":"该店已建档，请勿重复上传","statusCode":7,"statusName":"已作废","type":1,"updateTime":1564019690000}]
         * total : 9
         */

        private int currentPage;
        private int limit;
        private int offset;
        private int pageCount;
        private int total;
        private List<AptitudeBean> rows;

        public int getCurrentPage() {
            return currentPage;
        }

        public void setCurrentPage(int currentPage) {
            this.currentPage = currentPage;
        }

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public int getOffset() {
            return offset;
        }

        public void setOffset(int offset) {
            this.offset = offset;
        }

        public int getPageCount() {
            return pageCount;
        }

        public void setPageCount(int pageCount) {
            this.pageCount = pageCount;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public List<AptitudeBean> getRows() {
            return rows;
        }

        public void setRows(List<AptitudeBean> rows) {
            this.rows = rows;
        }

    }
}

