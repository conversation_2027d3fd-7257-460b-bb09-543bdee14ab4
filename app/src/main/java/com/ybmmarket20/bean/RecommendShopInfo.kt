package com.ybmmarket20.bean

/**
 * 人气好店
 */
data class RecommendShopInfoModule(
    var content: RecommendShopInfoContent?
)

data class RecommendShopInfoContent(
    var list: MutableList<RecommendShopInfoContentItem>?,
    var title: String?,
    var actionName: String?
)

data class RecommendShopInfoContentItem(
    var logo: String?,
    var name: String?,
    var shopCode: String?,
    var indexLink: String?,
    var shopPropertyCode: String?, // self:自营
    var activityInfo: RecommendShopActivityInfo?, //活动标签信息，最多一个。例：券满10减1元。
    var freightTips: String?, //起送包邮
    var shopPropTags: MutableList<TagBean>?, //店铺属性标签
    var sptype: String?,
    var spid: String?,
    var sid: String?
)

data class RecommendShopActivityInfo(
    var activityType: Int, //店铺活动类型，1券，2减，3折，4促
    var activityTypeDesc: String?, //店铺活动类型文字，券，减，折，促
    var activityContent: String //店铺活动文本内容
)