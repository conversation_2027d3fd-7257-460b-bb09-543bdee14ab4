package com.ybmmarket20.bean;

import android.text.TextUtils;

import java.util.List;

/**
 * 退款详情
 */
public class RefundDetail {
    public String refundFee = "";
    public String refundBalance = "";//申请退款余额/退款余额
    public String refundMoney = "";//申请退款金额
    public String applyRefundTime = "";
    public String auditRefundTime = "";
    public String auditStatusName;//退款状态文案
    public int auditState;//退款状态 0-待审核 1-退款通过 -1-退款拒绝
    public int refundChannel;//1:用户发起 2:客服介入
    public String refundReason = "";
    public String refundExplain = "";
    public String evidence1 = "";
    public String evidence2 = "";
    public String evidence3 = "";
    public String evidence4 = "";
    public String evidence5 = "";
    public List<String> imgList;
    public String refundOrderAdress = "";//退款地址
    public String refundOrderPhone = "";//退款电话
    public String refundOrderName = "";//退款名字
    public String refundRouteTips = "";//退款路线 说明 原路返回
    public String closeTime;//关闭时间
    public String closeReason;//关闭原因
    public String refundOrderExpressDelivery;//快递说明
    public int id;

    public int payType;//1:在线支付 2:货到付款 3:线下转账 4:银行授信支付 5自有账期
    public int refundMode;//退款单方式(退款类型)：0: 未知;1.有货退款;2.无货退款;3-退运费;4-小额赔偿，4的时候展示文字小额赔偿
    public OrderRefundBankDto orderRefundBankBusinessDto;//（收款账户银行信息）
    public OrderRefundExpressDto orderRefundExpressBusinessDto;//（快递单信息）

    public int showBankState;//编辑收款信息 1展示，0隐藏
    public int showExpressState;//运单信息  1，展示编辑  2，展示填写  0隐藏
    public int cancelRefundOrderState;//取消退款 1展示  0隐藏

    /*------------------渠道-------------------*/
    public String channelCode;//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱

    public int isThirdCompany;//是否是自营（0：是；1：否）

    public String refundPayEvidence;//退款打款凭证图片链接。不带文件服务器域名。返回为 null 或者空串不用展示
    public String invoiceDeliveryInfo;//发票寄出快递文字描述。返回为 null 或者空串不用展示
    public int billType;// 发票类型：1电子普通发票 2增值税专用发票 3纸质普通发票 4增值税电子专用发票
    public String virtualGold;
    public String cashPayAmount;
    public String kfContact; //客服电话
    public boolean canPlatformIn; //是否显示平台介入
    public String refundOrderNo;
    public String orderNo;

    public double freightAmount; //运费金额

    public int refundOrderType; // 为3时 不展示 查看退款商品按钮

    public String indemnityMoney;//额外赔偿金额，大于0时展示额外赔偿

    public boolean isIsThirdCompany() {
        return isThirdCompany == 0;
    }

    public boolean showRefundOrderBtn(){
        return refundOrderType != 3;
    }

    /**
     * 是否是小额赔偿
     * @return
     */
    public boolean isSmallPayment() {
        return refundMode == 4;
    }

    /**
     * 是否有额外打款
     * @return
     */
    public boolean isIndemnityMoney() {
        try {
            if (!TextUtils.isEmpty(indemnityMoney)) {
                double indemnity = Double.parseDouble(indemnityMoney);
                if (indemnity > 0 && refundMode != 4) return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
