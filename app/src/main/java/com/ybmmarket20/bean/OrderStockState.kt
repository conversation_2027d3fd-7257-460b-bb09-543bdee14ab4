package com.ybmmarket20.bean

data class OrderStockState(
        /**
         * 1. 全部有货
         * 2. 以下商品卖光啦，先将其他有货商品加入购物车？
         * 3. 该商品暂时无货，您可以去看看相似商品~/订单中的商品都卖光啦，再看看其他商品吧~
         * 4. 推荐相关活动品，请进详情查看
         */
        var stockCode:String? = "",
        var message:String? = "", // 弹窗文案
        var availableWareList:ArrayList<OrderBuyAgainProduct>? = arrayListOf(), //可购买商品列表
        var noAvailableWareList:ArrayList<OrderBuyAgainProduct>? = arrayListOf() //不可购买商品列表
)

data class OrderBuyAgainProduct(
        var skuId:String? = "",
        var imageUrl :String? = "",
        var productQuantity:Int? = 0
)
