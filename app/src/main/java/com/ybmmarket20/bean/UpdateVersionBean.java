package com.ybmmarket20.bean;

import java.io.Serializable;

/**
 * <AUTHOR> Brin
 * @date : 2019/6/4 - 15:26
 * @Description :
 */
public class UpdateVersionBean implements Serializable {

    public String packageName;          // 包名
    public int terminalType;            // 终端类型 1 android 2 苹果
    public int versionCode;          // 版本号
    public String versionName;          // 版本名称
    public String versionInfo;          // 升级信息
    public String versionTitle;         // 升级标题
    public String downloadUrl;          // 下载地址
    public String apkSize;              // 更新包大小
    public boolean forceUpdate;         // 是否强制更新
    public String md5Key;               // 更新包 md5 值

    /*{
        "apkSize":"108",
            "createTime":1559721756000,
            "downloadUrl":"http://upload.ybm100.com/ybm/download/ybm.apk?v=464",
            "forceUpdate":false,
            "id":14,
            "packageName":"com.ybmmarket20.debug",
            "status":2,
            "terminalType":1,
            "updateTime":1559721781000,
            "updator":"xuxiaozhen",
            "versionCode":511,
            "versionInfo":"1. 升级更新app\n1. 升级更新app\n1. 升级更新app\n1. 升级更新app\n1. 升级更新app\n",
            "versionMin":510,
            "versionName":"5.1.1",
            "versionTitle":"升级更新app"
    }*/


}
