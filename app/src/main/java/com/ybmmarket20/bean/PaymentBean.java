package com.ybmmarket20.bean;

/**
 * Created by asus on 2016/4/13.
 */
public class PaymentBean {
    public int retryInterval;//间隔时间
    public int retryTimes;//次数
    public int jumpType  =1;//跳转类型 1 跳支付 ，2 跳订单列表 ，不传默认跳支付

    public int callType; //1-是跳收银台，2-跳到我的订单列表，3-轮询到结果调支付接口



    //region  如果有比当前更合适的优惠券可用，做提示用的。 仅当 status = 40000 时候提示
    public String msg;
    public String title;
    public int status;
    //"msg": "您有1张优惠劵已生效",
    //"title": "优惠券生效提醒",
    //"status": "40000"
    //endregion

    private PaymentOrderBean order;

    public PaymentOrderBean getOrder() {
        return order;
    }

    public void setOrder(PaymentOrderBean order) {
        this.order = order;
    }
}
