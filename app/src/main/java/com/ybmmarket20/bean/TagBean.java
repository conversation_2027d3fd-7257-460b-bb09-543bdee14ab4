package com.ybmmarket20.bean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Brin
 * @date : 2020/12/1 - 14:23
 * @Description :  文字标签
 */
public class TagBean implements Serializable {
    /**
     *  1 : 可配置文字，背景，边框的颜色和透明度
     *  2 : 优惠券样式，只可配置文字样式，背景为 .9 图
     */
    public int uiStyle;
    public String text;
    public String textColor;
    public String bgColor;
    public String borderColor;
    public List<CSUDetailBean> csuList;
    public String description;
    public String appUrl;
    public String appIcon;
    public int giveSkuType;


    public void initCsuList() {
        if (csuList != null) {
            for (CSUDetailBean csuDetailBean : csuList) {
                csuDetailBean.setGiveSkuType(giveSkuType);
            }
        }
    }
}
