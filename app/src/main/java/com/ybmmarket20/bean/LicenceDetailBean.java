package com.ybmmarket20.bean;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.ybmmarket20.adapter.LicensePicListAdapter;
import com.ybmmarket20.utils.DateTimeUtil;

import java.util.ArrayList;
import java.util.List;


    public  class LicenceDetailBean implements Parcelable {
        public String isEdit;
        public List<Item> necessaryLicenceList;
        public List<Item> optionalLicenceList;

        public LicenseListBean.License licenseAudit;

        public String tempRemark;

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeTypedList(this.necessaryLicenceList);
            dest.writeTypedList(this.optionalLicenceList);
            dest.writeParcelable(this.licenseAudit, flags);
            dest.writeString(this.isEdit);
            dest.writeString(this.tempRemark);
        }

        public LicenceDetailBean() {
        }

        protected LicenceDetailBean(Parcel in) {
            this.necessaryLicenceList = in.createTypedArrayList(Item.CREATOR);
            this.optionalLicenceList = in.createTypedArrayList(Item.CREATOR);
            this.licenseAudit = in.readParcelable(LicenseListBean.License.class.getClassLoader());
            this.isEdit=in.readString();
            this.tempRemark=in.readString();
        }

        public static final Creator<LicenceDetailBean> CREATOR = new Creator<LicenceDetailBean>() {
            @Override
            public LicenceDetailBean createFromParcel(Parcel source) {
                return new LicenceDetailBean(source);
            }

            @Override
            public LicenceDetailBean[] newArray(int size) {
                return new LicenceDetailBean[size];
            }
        };


        public static class Item implements Parcelable {

        public String id;
        public String name;
        public String licenseCategoryCode;
        public String licenseImgUrls;

        public String xyyEntrusCode;//小药药委托书编号
        public long xyyEntrusValidateTime;//小药药委托书有效期

        public List<LicensePicListAdapter.ImageInfo> imageInfoList;

        //7.11资质优化资质变更列表  涉及到的新的变更字段 取新的字段
            public String credentialName;//条目名称 客户委托书
            public String licenseCode;//条目编号 FRSQ

            public List<LicensePicListAdapter.ImageInfo> getImageUrlList() {
            if (imageInfoList == null) {
                imageInfoList = new ArrayList<>();
                try {
                    String[] split = licenseImgUrls.split(",");
                    for (String s : split) {
                        LicensePicListAdapter.ImageInfo imageInfo = new LicensePicListAdapter.ImageInfo();
                        imageInfo.oldPath = s;
                        imageInfoList.add(imageInfo);
                    }
                    return imageInfoList;
                } catch (Exception exception) {
                    return imageInfoList = new ArrayList<>();
                }
            } else {
                return imageInfoList;
            }


        }

        public String getXyyEntrusCode() {
            if (TextUtils.isEmpty(xyyEntrusCode)) {
                return "未知";
            } else {
                return xyyEntrusCode;
            }
        }

        public String getXyyEntrusValidateTime() {

            if (xyyEntrusValidateTime <= 0) {

                return "未知";
            } else {
                return DateTimeUtil.getCouponDateTime(xyyEntrusValidateTime);

            }
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(this.id);
            dest.writeString(this.name);
            dest.writeString(this.licenseCategoryCode);
            dest.writeString(this.licenseImgUrls);
            dest.writeString(this.xyyEntrusCode);
            dest.writeLong(this.xyyEntrusValidateTime);

        }

        public Item() {
        }

        protected Item(Parcel in) {
            this.id = in.readString();
            this.name = in.readString();
            this.licenseCategoryCode = in.readString();
            this.licenseImgUrls = in.readString();
            this.xyyEntrusCode = in.readString();
            this.xyyEntrusValidateTime = in.readLong();

        }

        public static final Creator<Item> CREATOR = new Creator<Item>() {
            @Override
            public Item createFromParcel(Parcel source) {
                return new Item(source);
            }

            @Override
            public Item[] newArray(int size) {
                return new Item[size];
            }
        };
    }
    }

