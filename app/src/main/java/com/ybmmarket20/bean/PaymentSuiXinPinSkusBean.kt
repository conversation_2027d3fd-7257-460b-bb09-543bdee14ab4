package com.ybmmarket20.bean

import com.ybmmarket20.xyyreport.page.payment.IPaymentSuiXinPinGoods
import java.util.ArrayList

/**
 * 提单页随心拼返回数据
 */
data class PaymentSuiXinPinSkusBean(
//    var mainShopCode: String?="", //主店铺编码
//    var isThirdCompany: Int, //是否第三方
//    var mainOrgId: String?, //主机构id
    var totalAmount: Double, //商品总金额
    var totalNum: Int, //商品的总数量
    var varietyNum: Int, //商品品种
    var inValidMsg: String?, //商品无效文案
    var items: MutableList<PaymentSuiXinPinSkusItemBean>? //商品列表
)

/**
 * 提单页随心拼返回商品数据
 */
data class PaymentSuiXinPinSkusItemBean(
    var qty: Int, //购买数量
    var createTime: String? = "", //加入时间
    var imageUrl: String? = "", //图片
    var isSplit: Int, //是否可拆零（0:不可拆零；1:可拆零）
    var mediumPackageNum: Int, //mediumPackageNum
    var mediumPackageTitle: String? = "", //是否可拆零文案
    var name: String? = "", //商品名称
    var type: Int?,// 1 代表随心拼，2 代表顺手买
    var price: String? = "", //当前售价
    var purchasePrice: String? = "", //实付价
    var markerUrl: String? = "", //商品标签URL
    var nearEffectiveFlag: Int, //近效期标识(1:临期，2:近效)
    var productUnit: String? = "", //商品单位
    var suggestPrice: String? = "", //建议零售价
    var fob: String? = "", //原价
    var skuId: String? = "",
    var spec: String? = "", //规格
    var subtotal: String? = "", //小计
    var spid: String? = "",
    var sptype: String? = "",
    var sid: String? = "",
    var promoTag: String? = "", //已有xx人选择下单
    var sourceType: String? = "",
    //顺手买新加参数
    //商品名字
    var showName: String? = "",
    //原价
    var retailPrice: String? = "",
    var id: String? = "",
    //有效期
    var nearEffect: String? = "",
    var tagList: ArrayList<TagBean>? = null,
    var actPt: SkuStartBean? = null,
    var actPgby: SkuStartBean? = null
): IPaymentSuiXinPinGoods {

    val qtData: PaymentSuiXinPinQtData? = null
    override fun getScmId(): String? = qtData?.scmId

    override fun getExpId(): String? = qtData?.expId

    override fun getRank(): String? = qtData?.rank

    override fun getSuiXinPinQtListData(): String? = qtData?.qtListData

    override fun getQtSkuData(): String? = qtData?.qtSkuData

    override fun getProductSkuSkuId(): String? {
        return if (skuId == null) "$id"
        else skuId
    }

    override fun getLongSkuId(): Long {
        return try {
            if (getProductSkuSkuId().isNullOrEmpty()) 0L
            else {
                getProductSkuSkuId()!!.toLong()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            0L
        }
    }

    override fun getSkuName(): String? = showName
    override fun getSuiXinPinScmId(): String? {
        return qtData?.scmId
    }

}
