package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.activity.afterSales.adapter.REFUND_OR_AFTER_SALES_ITEM_TYPE_ORDER
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RefundOrAfterSalesBean
import com.ybmmarket20.bean.RefundOrAfterSalesCountBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.network.request.RefundOrAfterSalesRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import kotlinx.coroutines.launch

class RefundOrAfterSalesViewModel(appLike: Application) : BaseViewModel(appLike) {

    private val _refundOrAfterSalesLiveData = MutableLiveData<BaseBean<RefundOrAfterSalesBean>>()
    val refundOrAfterSalesLiveData = _refundOrAfterSalesLiveData

    private val _refundOrAfterSalesCountLiveData = MutableLiveData<BaseBean<RefundOrAfterSalesCountBean>>()
    val refundOrAfterSalesCountLiveData = _refundOrAfterSalesCountLiveData

    private val _buyAgainLiveData = MutableLiveData<Any>()
    val buyAgainLiveData: LiveData<Any> = _buyAgainLiveData
    private val _checkInterventionLiveData = MutableLiveData<BaseBean<String>>()
    val checkInterventionLiveData: LiveData<BaseBean<String>> = _checkInterventionLiveData

    /**
     * 获取退款售后列表
     */
    fun getRefundOrAfterSalesList(paramsMap: Map<String, String>, pageNo: Int) {
        viewModelScope.launch {
            val refundOrAfterSalesList =
                RefundOrAfterSalesRequest().getRefundOrAfterSalesList(paramsMap)
            if (refundOrAfterSalesList.data == null) {
                refundOrAfterSalesList.data = RefundOrAfterSalesBean()
                refundOrAfterSalesList.data.currentPage = pageNo
                refundOrAfterSalesList.data.rows = mutableListOf()
            }
            _refundOrAfterSalesLiveData.postValue(refundOrAfterSalesList)
        }
    }

    /**
     * 获取各tab未读数
     */
    fun getRefundOrAfterSalesCountNums(map : HashMap<String, String>) {
        viewModelScope.launch {
            val refundOrAfterSalesCountBean =
                RefundOrAfterSalesRequest().getRefundOrAfterSalesCountNums(map)
            if (refundOrAfterSalesCountBean.data == null) {
                refundOrAfterSalesCountBean.data = RefundOrAfterSalesCountBean()
            }
            _refundOrAfterSalesCountLiveData.postValue(refundOrAfterSalesCountBean)
        }
    }

    /**
     * 再次购买
     */
    fun buyAgain(orderId: String) {
        viewModelScope.launch {
            val sId = RefundOrAfterSalesRequest().getSId()
            addAnalysisRequestParams(
                RequestParams().apply {
                    put("merchantId", SpUtil.getMerchantid())
                    put("id", orderId)
                },
                BaseFlowData(
                    AnalysisConst.FlowDataChain.FLOWDATAPARAMS_SPTYPE_REBUY,
                    orderId,
                    sId.data.sid?: "",
                    null,
                    ""
                ),
                AnalysisConst.FlowDataChain.FLOWDATACHAIN_TAG_ADDCART_REBUY
            )
            val buyAgain = RefundOrAfterSalesRequest().buyAgain(
                mapOf(
                    "id" to orderId,
                    "merchantId" to SpUtil.getMerchantid()
                )
            )
            _buyAgainLiveData.postValue(buyAgain)
        }
    }

    /**
     * 更新倒计时
     */
    fun updateCountDown() {
        val refundOrAfterSalesLiveData = _refundOrAfterSalesLiveData.value?.data?: return
        refundOrAfterSalesLiveData.rows?.filter { it.itemType == REFUND_OR_AFTER_SALES_ITEM_TYPE_ORDER }
            ?.forEach {
                it.countDownNewTime -= 1
            }
    }

    /**
     * 平台介入前置校验
     */
    fun checkIntervention(refundOrderNo:String,orderNo:String) {
        if(refundOrderNo == null){
            return
        }
        viewModelScope.launch {
            val checkIntervention = RefundOrAfterSalesRequest().checkIntervention(refundOrderNo,orderNo)
            _checkInterventionLiveData.postValue(checkIntervention)
        }
    }

}