package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.ApplyInvoiceCheckStatusBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RemindProgressDetailData
import com.ybmmarket20.bean.RemindSubmitBean
import com.ybmmarket20.network.request.RemindSubmitRequest
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 14:39
 *    desc   :
 */
class RemindSubmitViewModel(val app: Application) : AndroidViewModel(app) {
    private val _onRemindProgressLiveData = MutableLiveData<BaseBean<RemindProgressDetailData>>()
    val onRemindProgressLiveData: LiveData<BaseBean<RemindProgressDetailData>> =
        _onRemindProgressLiveData
    private val _onRemindCancelLiveData = MutableLiveData<BaseBean<String>>()
    val onRemindCancelLiveData: LiveData<BaseBean<String>> = _onRemindCancelLiveData

    /**
     * 提醒发货
     */
    fun getRemindSubmit(
        orderNo: String
    ) : LiveData<BaseBean<RemindSubmitBean>>{
        val onRemindSubmitLiveData = MutableLiveData<BaseBean<RemindSubmitBean>>()
        viewModelScope.launch {
            val remindSubmit = RemindSubmitRequest().getRemindSubmit(orderNo)
            onRemindSubmitLiveData.postValue(remindSubmit)
        }
        return onRemindSubmitLiveData
    }

    /**
     * 提醒发货
     */
    fun getRemindProgress(
        orderNo: String
    ) {
        if (orderNo.isNullOrEmpty()) {
            return
        }
        viewModelScope.launch {
            val remindSubmit = RemindSubmitRequest().getRemindProgress(orderNo)
            _onRemindProgressLiveData.postValue(remindSubmit)
        }
    }

    /**
     * 提醒发货
     */
    fun getRemindCancel(
        orderNo: String
    ) {
        viewModelScope.launch {
            val remindSubmit = RemindSubmitRequest().getRemindCancel(orderNo)
            _onRemindCancelLiveData.postValue(remindSubmit)
        }
    }

    /**
     * 申请发票前置：查询发票售后数量
     */
    fun queryInvoiceAfterSalesCount(
        orderNo: String
    ) : LiveData<BaseBean<ApplyInvoiceCheckStatusBean>> {
        val liveData = MutableLiveData<BaseBean<ApplyInvoiceCheckStatusBean>>()
        viewModelScope.launch {
            val remindSubmit = RemindSubmitRequest().queryInvoiceAfterSalesCount(orderNo)
            liveData.postValue(remindSubmit)
        }
        return liveData
    }

}