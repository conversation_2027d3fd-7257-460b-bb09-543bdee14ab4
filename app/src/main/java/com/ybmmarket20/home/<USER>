package com.ybmmarket20.home;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.baidu.location.BDAbstractLocationListener;
import com.baidu.location.BDLocation;
import com.baidu.location.LocationClient;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.baidu.location.LocationClientOption;
import com.ybm.app.bean.NetError;
import com.ybm.app.utils.PermissionDialogUtil;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.LinkShopActivityKt;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.NewTrackListInfo;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.bean.SearchResultBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.PermissionUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * <AUTHOR>
 * @date 2020-05-15
 * @description 首页-常购清单
 */

public class HomeSteadyLayoutOftenBuyFragment extends LicenseStatusFragment implements RecyclerRefreshLayout.OnRefreshListener, GoodsListAdapter.OnListViewItemClickListener, CommonRecyclerView.Listener, GoodsListAdapter.OnDeleteItemClickCallback {

    @Bind(R.id.rv_discover_tab)
    CommonRecyclerView mRecyclerView;
    @Bind(R.id.ll_title)
    RelativeLayout ll_title;
    @Bind(R.id.tv_local)
    TextView tv_local;
    @Bind(R.id.empty_view)
    LinearLayout empty_view;

    private GoodListAdapterNew mAdapter;
    private List<RowsBean> data = new ArrayList<>();

    private final int PAGE_START_POSITION = 0;
    private final int PAGE_SIZE = 10;
    private int pageIndex = PAGE_START_POSITION;
    private boolean isCancleDelete = true;
    //是否已经接收到埋点参数
    private boolean isReceivedAnalysis = false;

    boolean gpsIsOpen = false;
    private String latitude = "";
    private String longitude = "";
    private boolean isLocaled = true;
    private boolean isFirstRefresh = true;

    @Override
    public void onResume() {
        super.onResume();
        if (!isLocaled) {
            requestLocationPermissionDialog();
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_discover_tab;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected void initData(String content) {

        mAdapter = new GoodListAdapterNew(R.layout.item_goods_new, data, false);
        mAdapter.setEmptyView(getContext(), R.layout.layout_empty_view, R.drawable.icon_empty, getResources().getString(R.string.no_data));
        mRecyclerView.setListener(this);
        mRecyclerView.setAdapter(mAdapter);
        if (!isCancleDelete) ll_title.setVisibility(View.VISIBLE);
        tv_local.setOnClickListener(v -> {
            isLocaled = false;
            openAppSettingDetail((BaseActivity) getContext());
        });
        requestLocationPermissionDialog();
    }

    /**
     * 打开应用设置详情页
     */
    private void openAppSettingDetail(Activity context) {
        Intent intent = new Intent();
        if (!gpsIsOpen) {
            intent.setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        } else {
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse("package:" + context.getPackageName()));
        }
        try {
            context.startActivityForResult(intent, LinkShopActivityKt.REQUEST_CODE_CHECK_PERMISSION);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void requestLocationPermissionDialog() {
        RxPermissions rxPermissions = new RxPermissions(getNotNullActivity());
        if (rxPermissions.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)
                && rxPermissions.isGranted(Manifest.permission.ACCESS_COARSE_LOCATION)) {
            requestLocationPermission();
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(getNotNullActivity(),
                    "药帮忙App需要申请定位权限，用于获取您当前所在地，为您展示当前所在地的商品数据",
                    () -> {
                        requestLocationPermission();
                    });
        }
    }


    @SuppressLint("CheckResult")
    private void requestLocationPermission() {
        gpsIsOpen = PermissionUtil.checkGPSIsOpen(getContext());
        RxPermissions rxPermissions = new RxPermissions(getNotNullActivity());
        rxPermissions.request(Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
        ).subscribe(granted -> {
            if (gpsIsOpen && granted) { //获取权限
                empty_view.setVisibility(View.GONE);
                showProgress();
                initLocationOption();
            } else { //未获取
                empty_view.setVisibility(View.VISIBLE);
            }
        });
    }

    /**
     * 初始化定位参数配置
     */
    private void initLocationOption() { //定位服务的客户端。宿主程序在客户端声明此类，并调用，目前只支持在主线程中启动
        LocationClient locationClient = new LocationClient(getContext().getApplicationContext());
        //声明LocationClient类实例并配置定位参数
        LocationClientOption locationOption = new LocationClientOption();
        MyLocationListener myLocationListener = new MyLocationListener();
        //注册监听函数
        locationClient.registerLocationListener(myLocationListener);
        //可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
        locationOption.setLocationMode(LocationClientOption.LocationMode.Hight_Accuracy);
        //可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
        locationOption.setCoorType("gcj02");
        //可选，默认0，即仅定位一次，设置发起连续定位请求的间隔需要大于等于1000ms才是有效的
        locationOption.setScanSpan(0);
        //可选，设置是否需要地址信息，默认不需要
        locationOption.setIsNeedAddress(true);
        //可选，设置是否需要地址描述
        locationOption.setIsNeedLocationDescribe(true);
        //可选，设置是否需要设备方向结果
        locationOption.setNeedDeviceDirect(false);
        //可选，默认false，设置是否当gps有效时按照1S 1次频率输出GPS结果
        locationOption.setLocationNotify(false);
        //可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
        locationOption.setIgnoreKillProcess(true);
        //可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
        locationOption.setIsNeedLocationDescribe(false);
        //可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
        locationOption.setIsNeedLocationPoiList(true);
        //可选，默认false，设置是否收集CRASH信息，默认收集
        locationOption.SetIgnoreCacheException(false);
        //可选，默认false，设置是否开启Gps定位
        locationOption.setOpenGps(true);
        //可选，默认false，设置定位时是否需要海拔信息，默认不需要，除基础定位版本都可用
        locationOption.setIsNeedAltitude(false);
        //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者，该模式下开发者无需再关心定位间隔是多少，定位SDK本身发现位置变化就会及时回调给开发者
        // locationOption.setOpenAutoNotifyMode()
        //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者
        //locationOption.setOpenAutoNotifyMode(3000, 1, LocationClientOption.LOC_SENSITIVITY_HIGHT)
        //需将配置好的LocationClientOption对象，通过setLocOption方法传递给LocationClient对象使用
        locationClient.setLocOption(locationOption);
        //开始定位
        locationClient.start();
    }

    private void requestData() {

        HttpManager.getInstance().post(isKaUser ? AppNetConfig.KA_HOME_HOME_OFTEN_BUY_LIST : AppNetConfig.HOME_OFTEN_BUY_LIST, getParams(), new BaseResponse<SearchResultBean>() {
            @Override
            public void onSuccess(String content, BaseBean<SearchResultBean> obj, SearchResultBean rowsListBean) {
                if (isDestroy) return;

                mRecyclerView.setRefreshing(false);
                dismissProgress();
                if (obj != null && rowsListBean != null) {
                    updateLicenseStatus(rowsListBean.licenseStatus, firstLoad ? getCurrentLicenseStatusListener() : null);
                    if (!isReceivedAnalysis) {
                        FlowDataAnalysisManagerKt.updateFlowData(mFlowData, rowsListBean.spType, rowsListBean.spId, rowsListBean.sid, rowsListBean.nsid);
                        FlowDataEventAnalysisKt.flowDataPageCommoditySearch(mFlowData);
                        // guanchong 常购清单
                        mAdapter.setFlowData(mFlowData);
                        isReceivedAnalysis = true;
                    }
                    updateSearchData(rowsListBean);
                }
                firstLoad = false;
                dismissProgress();
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                failureHandle(firstLoad);
            }
        });
    }

    private RequestParams searchMoreParams;

    /**
     * 请求搜索后的数据更新
     *
     * @param rowsBeans
     */
    private void updateSearchData(SearchResultBean rowsBeans) {
        AdapterUtils.INSTANCE.addLocalTimeForRows(rowsBeans.rows);
        searchMoreParams = rowsBeans.getRequestParams();
        if (firstLoad) {
            data.clear();
            mAdapter = new GoodListAdapterNew(R.layout.item_goods_new, data, false);
            mRecyclerView.setAdapter(mAdapter);
        }
        if (rowsBeans.rows != null && rowsBeans.rows.size() > 0) {
            data.addAll(rowsBeans.rows);
        }
        // 请求并更新折后价
        AdapterUtils.INSTANCE.getAfterDiscountPrice(rowsBeans.rows, mAdapter);
        if (rowsBeans.isEnd) {
            mAdapter.loadMoreEnd();
        } else {
            mAdapter.loadMoreComplete();
        }
    }

    private void failureHandle(boolean isRefresh) {
        if (isRefresh) {
            mRecyclerView.setRefreshing(false);
        } else {
            mAdapter.loadMoreFail();
        }
    }

    private boolean firstLoad = true;

    @Override
    protected RequestParams getParams() {
        if (!firstLoad) return searchMoreParams;
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());//商户编号  string
        params.put("longitude", longitude);
        params.put("latitude", latitude);
        params.put("spFrom", AnalysisConst.FlowDataChain.FLOWDATACHAIN_FROM_FIND_OFTEN_BUY_LIST);

        params.put("pageSource", IntentCanst.PAGE_SOURCE_OFTEN_BUY_LIST);
        params.put("nsid", "");
        params.put("listoffset", "");
        params.put("listdata", "");
        params.put("pageurl", "");
        if (isReceivedAnalysis) {
            FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
        }
        return params;
    }

    @Override
    protected String getUrl() {
        return null;
    }


    @Override
    public void onRefresh() {
        firstLoad = true;
        if (!isFirstRefresh) {
            showProgress();
            requestData();
        }
        isFirstRefresh = false;
    }

    @Override
    public void onLoadMore() {
        requestData();
    }


    @Override
    public void onItemClick(RowsBean rows) {
        if (rows != null) {
            openUrl("ybmpage://productdetail/" + rows.getId());
        }
    }

    @Override
    public void onDeleteItemClick(int position) {
        if (position >= 0 && position < data.size()) {
            deleteOftenBuyGoods(data.get(position), position);
        }
    }


    /**
     * 删除常购商品
     *
     * @param rowsBean
     */
    private void deleteOftenBuyGoods(RowsBean rowsBean, int position) {
        showProgress();
        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(rowsBean.getId()));
        HttpManager.getInstance().post(AppNetConfig.CANCEL_ORDER_SELL_NO, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                dismissProgress();
                if (null != obj && obj.isSuccess()) {
                    mAdapter.remove(position);
                    //DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, "删除成功");
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
            }
        });
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        firstLoad = true;
        requestData();
    }

    public void showTitle() {
        isCancleDelete = false;
    }

    class MyLocationListener extends BDAbstractLocationListener {

        @Override
        public void onReceiveLocation(BDLocation bdLocation) {
            if (!TextUtils.isEmpty(latitude)) return;
            //获取纬度信息
            latitude = "" + bdLocation.getLatitude();
            //获取经度信息
            longitude = "" + bdLocation.getLongitude();
            firstLoad = true;
            requestData();
        }
    }

}
