package com.ybmmarket20.home

import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.ybmmarket20.bean.cart.Level1ItemGoodsBeanAbs
import com.ybmmarket20.home.CartItemAdapter3.Companion.level1_activity_good
import com.ybmmarket20.home.CartItemAdapter3.Companion.level1_activity_good_end
import com.ybmmarket20.home.CartItemAdapter3.Companion.level1_activity_good_header
import com.ybmmarket20.home.CartItemAdapter3.Companion.level1_common_good
import com.ybmmarket20.home.CartItemAdapter3.Companion.level1_group_good
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.xyyreport.page.cart.CartReport
import com.ybmmarket20.xyyreport.page.cart.CartReportDataUtil

abstract class CartItemAnalysisAdapter3(data: MutableList<MultiItemEntity>?) :
    BaseMultiItemQuickAdapter<MultiItemEntity, BaseViewHolder>(data) {

    private val mCache = mutableSetOf<Long>()

    override fun convert(p0: BaseViewHolder, p1: MultiItemEntity?) {
        whenAllNotNull(p0, p1) { _, bean ->
            when(bean.itemType) {
                level1_common_good,
                level1_activity_good_header,
                level1_activity_good,
                level1_activity_good_end,
                level1_group_good -> {
                    try {
                        val baseGoodsBean = bean as Level1ItemGoodsBeanAbs
                        val prodInfo = CartReportDataUtil.getProdInfo(baseGoodsBean.getProdId()) ?: return@whenAllNotNull
                        if (!mCache.contains(prodInfo.getProductId())) {
                            mCache.add(prodInfo.getProductId())
                            CartReport.trackCartGoodsExposure(mContext, prodInfo.getProductId())
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    fun onClear() {
        mCache.clear()
    }
}