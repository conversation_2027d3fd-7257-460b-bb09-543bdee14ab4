package com.ybmmarket20.home;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import butterknife.Bind;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.adapter.HomeFeedAdapter;
import com.ybmmarket20.bean.*;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.LicenseStatusFragmentV2;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarketkotlin.adapter.GoodListAdapterNew;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class HomeSteadyFeedFragment extends LicenseStatusFragmentV2 implements RecyclerRefreshLayout.OnRefreshListener, GoodsListAdapter.OnListViewItemClickListener, CommonRecyclerView.Listener, GoodsListAdapter.OnDeleteItemClickCallback {

    @Bind(R.id.rv_discover_tab)
    CommonRecyclerView mRecyclerView;
    @Bind(R.id.ll_title)
    RelativeLayout ll_title;
    @Bind(R.id.tv_local)
    TextView tv_local;
    @Bind(R.id.empty_view)
    LinearLayout empty_view;

    private HomeFeedAdapter mAdapter;
    private List<HomeFeedRows> data = new ArrayList<>();

    private boolean isCancleDelete = true;
    private String tabName;
    private String tabId;
    private int tabIndex;
    private String sId;
    private Bundle savedInstanceState;
    private boolean isEnd;

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_home_steady_feed;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    public boolean resetIsCrash() {
        return true;
    }

    @Nullable
    @org.jetbrains.annotations.Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        if (savedInstanceState != null) {
            Log.i("Bundle1 = ", "重建");
            tabName = savedInstanceState.getString("tabName");
            tabId = savedInstanceState.getString("tabId");
            tabIndex = savedInstanceState.getInt("tabIndex");
            sId = savedInstanceState.getString("sId");
            this.savedInstanceState = savedInstanceState;
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    protected void initData(String content, Bundle savedInstanceState) {
        super.initData(content, savedInstanceState);
        mRecyclerView.setRefreshEnable(false);
        mAdapter = new HomeFeedAdapter(getContext(), data);
        mAdapter.setTabIndex(tabIndex);
        mAdapter.setEmptyView(getContext(), R.layout.layout_empty_view, R.drawable.icon_empty, "暂无商品");
        mRecyclerView.setListener(this);
        mRecyclerView.setAdapter(mAdapter);

        if (!isCancleDelete) ll_title.setVisibility(View.VISIBLE);
        Bundle bundle = getArguments();
        if (tabName == null && tabId == null) {
            if (bundle != null) {
                tabName = bundle.getString("tabName");
                tabId = bundle.getString("tabId");
                tabIndex = bundle.getInt("tabIndex");
                sId = bundle.getString("sId");
            }
            if (tabName == null) tabName = "";
            if (tabId == null) tabId = "";
        }
    }

    @Override
    public void loadData() {
        requestData();
    }

    @Override
    protected void initData(String content) {

    }

    @Override
    public void onSaveInstanceState(@NonNull @NotNull Bundle outState) {
        outState.putString("tabName", tabName);
        outState.putString("tabId", tabId);
        outState.putInt("tabIndex", tabIndex);
        outState.putString("sId", sId);
        super.onSaveInstanceState(outState);
    }


    private void requestData() {
        HttpManager.getInstance().post(AppNetConfig.HOME_FEED_LIST_WITH_SHOP, getParams(), new BaseResponse<HomeFeedBean>() {
            @Override
            public void onSuccess(String content, BaseBean<HomeFeedBean> obj, HomeFeedBean rowsListBean) {
                dismissProgress();
                if (obj != null && rowsListBean != null) {
                    FlowDataAnalysisManagerKt.updateFlowData(mFlowData, rowsListBean.getSptype(), rowsListBean.getSpid(), rowsListBean.getSid(), rowsListBean.getNsid());
                    mAdapter.setFlowData(mFlowData);
                    requestParams = rowsListBean.convert2RequestParams();
                    updateLicenseStatus(rowsListBean.getLicenseStatus(), firstLoad ? getCurrentLicenseStatusListener() : null);
                    updateSearchData(rowsListBean);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                failureHandle(firstLoad);
            }
        });
    }

    private RequestParams searchMoreParams;

    /**
     * 请求搜索后的数据更新
     *
     * @param rowsBeans
     */
    private void updateSearchData(HomeFeedBean rowsBeans) {
        if (firstLoad) {
            data.clear();
            firstLoad = false;
        }

        if (rowsBeans != null && rowsBeans.getRows() != null && rowsBeans.getRows().size() > 0) {
            data.addAll(rowsBeans.getRows());
        }
        try {
            if (rowsBeans != null)
            isEnd = Boolean.parseBoolean(rowsBeans.convert2RequestParams().getParamsMap().get("isEnd"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (rowsBeans.getRows() != null) {
            AdapterUtils.INSTANCE.addLocalTimeForRowsWithShop(rowsBeans.getRows());
        }
        mAdapter.notifyDataChangedAfterLoadMore(requestParams == null, !isEnd);
        // 请求并更新折后价
        AdapterUtils.INSTANCE.getAfterDiscountPriceWithShop(rowsBeans.getRows(), mAdapter);
        searchMoreParams = rowsBeans.convert2RequestParams();
    }

    private void failureHandle(boolean isRefresh) {
        try {
            if (isRefresh) {
                mRecyclerView.setRefreshing(false);
            } else {
                mAdapter.loadMoreFail();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean firstLoad = true;

    private RequestParams requestParams = null;

    @Override
    protected RequestParams getParams() {
        if (requestParams != null) return requestParams;
        RequestParams params = new RequestParams();
        params.put("exhibitionId", tabId);
        params.put("pageType", "");
        params.put("pageNum", "");
        params.put("pageSize", "");
        params.put("sid", sId);
        params.put("tabTitle", tabName);
        params.put("tabLocation", tabIndex + 1 + "");

        return params;
    }

    @Override
    protected String getUrl() {
        return null;
    }


    @Override
    public void onRefresh() {
    }

    @Override
    public void onLoadMore() {
        if (!isEnd) {
            requestData();
        } else {
            mRecyclerView.post(() -> mAdapter.notifyDataChangedAfterLoadMore(requestParams == null, false));
        }
    }


    @Override
    public void onItemClick(RowsBean rows) {
        if (rows != null) {
            openUrl("ybmpage://productdetail/" + rows.getId());
        }
    }

    @Override
    public void onDeleteItemClick(int position) {

    }

    @Override
    public boolean onLicenseStatusEnable() {
        return false;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        firstLoad = true;
        requestData();
    }

    public void showTitle() {
        isCancleDelete = false;
    }

    public String getSlideTitle() {
        return tabName == null ? "" : tabName;
    }

}
