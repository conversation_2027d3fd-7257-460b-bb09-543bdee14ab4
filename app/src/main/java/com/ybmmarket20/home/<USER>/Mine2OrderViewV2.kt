package com.ybmmarket20.home.mine

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.home.mine.bean.Mine2HeaderItemContainer
import com.ybmmarket20.home.mine.bean.Mine2OrderBean
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import kotlinx.android.synthetic.main.view_mine2_order.view.*

class Mine2OrderViewV2(context: Context, attr: AttributeSet?) :
    AbsMine2BaseView<List<Mine2OrderBean>>(context, attr) {

    var mItemList: MutableList<Mine2OrderBean> = mutableListOf()
    var mContainer: Mine2HeaderItemContainer<List<Mine2OrderBean>>? = null
    var mSkipOrderListPage: ((state: String) -> Unit)? = null
    var mOrderTypeItemAnalysisClickCallback: ((Mine2OrderBean)->Unit)? = null
    override fun getLayoutId(): Int = R.layout.view_mine2_order_v2

    override fun initialize() {
        super.initialize()
//        tvMyOrderEntry.setOnClickListener {
//            val intent = Intent(context, OrderListActivity::class.java)
//            intent.putExtra(IntentCanst.ORDER_STATE, "0")
//            context.startActivity(intent)
//            XyyIoUtil.track("action_Me_AllOrders")
//        }
    }

    override fun setData(container: Mine2HeaderItemContainer<List<Mine2OrderBean>>) {
        if (!container.entry.isNullOrEmpty()) {
            mContainer = container
            container.entry?.let(mItemList::addAll)
            rv_mine2_order.layoutManager = GridLayoutManager(context, 5)
            rv_mine2_order.adapter = Mine2OrderAdapter(mItemList)
        }
    }

    /**
     * 设置气泡数量
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setOrderBubbleCount(bubbleCountList: List<Int>?) {
        if (bubbleCountList.isNullOrEmpty() || bubbleCountList.size != mItemList.size) return
        val adapter = rv_mine2_order.adapter ?: return
        mItemList = mItemList.zip(bubbleCountList) { item, count ->
            item.bubbleCount = count
            item
        }.toMutableList()
        adapter.notifyDataSetChanged()
    }

    inner class Mine2OrderAdapter(list: List<Mine2OrderBean>) :
        YBMBaseAdapter<Mine2OrderBean>(R.layout.item_mine2_order, list) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: Mine2OrderBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val ivMine2OrderIcon = holder.getView<ImageView>(R.id.iv_mine2_order_icon)
                val tvMine2OrderDes = holder.getView<TextView>(R.id.tv_mine2_order_des)
                val tvMine2OrderBubble = holder.getView<TextView>(R.id.tv_mine2_order_bubble)
                ivMine2OrderIcon.setImageResource(bean.icon)
                tvMine2OrderDes.text = bean.des
                tvMine2OrderBubble.visibility = if (bean.bubbleCount > 0) View.VISIBLE else View.GONE
                tvMine2OrderBubble.text = "${bean.bubbleCount}"
                holder.itemView.setOnClickListener {
                    mOrderTypeItemAnalysisClickCallback?.invoke(bean)
                    if (bean.orderType == 90) {
                        RoutersUtils.open("ybmpage://refundoraftersales")
                    } else {
//                        val intent = Intent(context, OrderListActivity::class.java)
//                        intent.putExtra(IntentCanst.ORDER_STATE, "${bean.orderType}")
//                        context.startActivity(intent)
                        mSkipOrderListPage?.invoke("${bean.orderType}")
                    }
                    XyyIoUtil.track(bean.trackKey)
                }
            }
        }
    }

    fun setOnOrderTypeItemClickAnalysisCallback(callback: ((Mine2OrderBean)->Unit)?) {
        mOrderTypeItemAnalysisClickCallback = callback
    }
}