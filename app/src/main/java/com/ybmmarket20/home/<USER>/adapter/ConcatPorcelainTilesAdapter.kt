package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.databinding.ItemConcatPorcelainTilesBinding
import com.ybmmarket20.home.newpage.bean.RecommendCommodity
import com.ybmmarket20.home.newpage.bean.RecommendCommodityItemBean
import com.ybmmarket20.xyyreport.SpmLogUtil

/**
 * @class   ConcatPorcelainTilesAdapter
 * <AUTHOR>
 * @date  2024/4/15
 * @description
 */
class ConcatPorcelainTilesAdapter: HomeComponentAnalysisAdapter<ConcatPorcelainTilesAdapter.ConcatPorcelainTilesVH>() {

    var mData: RecommendCommodity? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var mOnItemClickListener: ((mData: RecommendCommodity?, itemBean: RecommendCommodityItemBean,type: Int, offset: Int, content: String,where:Int) -> Unit)? = null //瓷片Item点击
    var mOnProductItemClickListener: ((mData: RecommendCommodity?, itemBean: RecommendCommodityItemBean,rowBean:RowsBean,type: Int,offset:Int) -> Unit)? = null //瓷片Item中的商品Item的点击


    private lateinit var mContext: Context
    //      value:当时埋点的时间戳
    var navigation = ""
    companion object{
        private const val SPAN_COUNT = 2

    }

    override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
    ): ConcatPorcelainTilesVH {
        mContext = parent.context
        return ConcatPorcelainTilesVH(ItemConcatPorcelainTilesBinding.inflate(LayoutInflater.from(mContext), parent, false))
    }

    override fun getItemCount(): Int = mData?.recommendCommodityContent?.recommendCommodityList?.let { if (it.size > 1) 1 else 0 }
            ?: 0 //有2个内容才显示

    override fun onBindViewHolder(
            holder: ConcatPorcelainTilesVH,
            position: Int
    ) {
        onComponentExposure(mContext, mData?.trackData, position) {
            SpmLogUtil.print("首页-组件-推荐楼层曝光")
        }
        holder.mBinding.apply {

            rvConcatPorcelainTiles.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            rvConcatPorcelainTiles.adapter = PorcelainTilesAdapter().apply {
                this.mDataList = (mData?.recommendCommodityContent?.recommendCommodityList?: arrayListOf()) as ArrayList<RecommendCommodityItemBean>
                this.mSuperData = mData
                mOnItemClickListener = {mData: RecommendCommodity?, itemBean: RecommendCommodityItemBean,type: Int, offset: Int, content: String,where:Int ->
                    <EMAIL>?.invoke(mData, itemBean, type, offset, content, where)
                    SpmLogUtil.print("首页-组件-推荐楼层点击")
                    onSubcomponentClick(mContext, itemBean.trackData)
                }
                mOnProductItemClickListener = { recommendCommodity: RecommendCommodity?, itemBean: RecommendCommodityItemBean,RowsBean,type: Int,offset:Int->
                    <EMAIL>?.invoke(recommendCommodity, itemBean, RowsBean, type, offset)
                    SpmLogUtil.print("首页-组件-推荐楼层点击")
                    onSubcomponentClick(mContext, itemBean.trackData)
                }
            }
            if (rvConcatPorcelainTiles.itemDecorationCount == 0){
                rvConcatPorcelainTiles.addItemDecoration(PorcelainTilesAdapter.PorcelainTilesItemDecoration())
            }

        }
    }

    override fun onViewAttachedToWindow(holder: ConcatPorcelainTilesVH) {
        super.onViewAttachedToWindow(holder)
        val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
        if (lp is StaggeredGridLayoutManager.LayoutParams) {
            lp.isFullSpan = true
        }
    }

    class ConcatPorcelainTilesVH(val mBinding:ItemConcatPorcelainTilesBinding):RecyclerView.ViewHolder(mBinding.root)

}