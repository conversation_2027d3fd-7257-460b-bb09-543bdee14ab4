package com.ybmmarket20.common

import android.os.Bundle
import com.ybmmarket20.utils.AuditStatusSyncUtil

/**
 * <AUTHOR>
 * @date 2020-01-22
 * @description 需要对一审资质审核状态监听需实现该类
 */
abstract class LicenseStatusFragmentV2: LazyFragment(), ILicenseStatus{

    private val licenseStatusListener: AuditStatusSyncUtil.AuditStatusSyncListener? = createListener()

    /**
     * 开启状态下添加监听
     */
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        if(onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().addLicenseStatusListener(licenseStatusListener)
    }

    /**
     * 开启状态下移除监听
     */
    override fun onDestroy() {
        if(onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().removeLicenseStatusListener(licenseStatusListener)
        super.onDestroy()
    }

    /**
     * 创建监听器
     */
    private fun createListener(): AuditStatusSyncUtil.AuditStatusSyncListener? {
        return if(onLicenseStatusEnable()) AuditStatusSyncUtil.AuditStatusSyncListener (::handleLicenseStatusChange)
        else null
    }

    /**
     * 更新审核状态
     * @param status 当前状态
     */
    protected fun updateLicenseStatus(status: Int, currentListener: AuditStatusSyncUtil.AuditStatusSyncListener?) {
        if(currentListener!=null){
            AuditStatusSyncUtil.getInstance().updateLicenseStatus(status, currentListener,true)
        }
    }

    /**
     * 获取当前监听器
     */
    protected fun getCurrentLicenseStatusListener(): AuditStatusSyncUtil.AuditStatusSyncListener? = licenseStatusListener

}