package com.ybmmarket20.common;

import android.text.TextUtils;

import com.ybm.app.bean.OKHttpRequestParams;
import com.ybm.app.bean.TimeLog;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 网络请求参数
 */
public class RequestParams extends OKHttpRequestParams {

    private RequestParams(Builder builder) {
        this.url = builder.url;
        this.fileParams = builder.fileParams;
        this.urlParams = builder.urlParams;
        this.timeLog = builder.timeLog;
        this.headers = builder.headers;
        initContentType();
    }

    public RequestParams() {
        if (timeLog == null) {
            timeLog = new TimeLog();
        }
    }

    public boolean isUploadFile() {
        return fileParams != null && !fileParams.isEmpty();
    }

    public Map<String, String> getParamsMap() {
        return urlParams;
    }

    public void put(String key, String value) {
        if (urlParams == null) {
            urlParams = new ConcurrentHashMap<>();
        }
        if (key == null) {
            return;
        }
        try {
            // value中带有特殊字符时候，会替换成空格，因此需要urlencode一下
            urlParams.put(key, value == null ? "" : URLEncoder.encode(value,"utf-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    public void putWithoutEncode(String key, String value) {
        if (urlParams == null) {
            urlParams = new ConcurrentHashMap<>();
        }
        if (key == null) {
            return;
        }
        urlParams.put(key, value == null ? "" : value);
    }

    public void put(String key, File file) {
        if (fileParams == null) {
            fileParams = new ConcurrentHashMap<>();
        }
        if (key == null) {
            return;
        }
        fileParams.put(key, file);
    }

    public void addHeader(String key, String value) {
        if (headers == null) {
            headers = new HashMap<>();
        }
        if (key == null) {
            return;
        }
        headers.put(key, value);
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public static final class Builder {
        Map<String, String> headers;
        String url;
        String json;
        TimeLog timeLog;
        ConcurrentHashMap<String, String> urlParams;
        ConcurrentHashMap<String, File> fileParams;

        public Builder() {
            headers = new HashMap<>();
            timeLog = new TimeLog();
            urlParams = new ConcurrentHashMap<>();
            fileParams = new ConcurrentHashMap<>();
            timeLog = new TimeLog();
            json = "";
            url = "";
        }

        public Builder url(String url) {
            this.url = url;
            return this;
        }

        public Builder addParam(String key, String value) {
            if (!TextUtils.isEmpty(key) && value != null) {
                this.urlParams.put(key, value);
            }
            return this;
        }

        public Builder addParam(String key, File file) {
            if (!TextUtils.isEmpty(key) && file != null) {
                this.fileParams.put(key, file);
            }
            return this;
        }

        public Builder json(String json) {
            if (!TextUtils.isEmpty(json)) {
                this.json = json;
            }
            return this;
        }

        public Builder addHeader(String key, String value) {
            if (!TextUtils.isEmpty(key)) {
                this.headers.put(key, value);
            }
            return this;
        }

        public Builder addHeader(Map<String, String> headers) {
            if (headers != null && !headers.isEmpty()) {
                this.headers = headers;
            }
            return this;
        }

        public RequestParams build() {
            return new RequestParams(this);
        }
    }

    public static Builder newBuilder() {
        return new Builder();
    }


    public TimeLog getTimeLog() {
        return timeLog;
    }


    public String getUrl() {
        return url;
    }

    /**
     * 比较RequestParams 相同key的value是否相等
     * @param params 要比较的params
     * @param key 比较的key对应的value
     * @return true 相等
     */
    public boolean equalsParamValue(RequestParams params, String key) {
        if (params == null || key == null || getParamsMap() == null || params.getParamsMap() == null) return false;
        String curValue = getParamsMap().get(key);
        String value = params.getParamsMap().get(key);
        if (curValue != null) return curValue.equals(value);
        else return value == null;
    }

    /**
     * 比较RequestParams 相同key的value是否相等
     * @param params 要比较的params
     * @param keys key列表
     * @return true 全部相等。false 存在不相等
     */
    public boolean equalsParamValues(RequestParams params, String... keys) {
        for(String key: keys) {
            if (!equalsParamValue(params, key)) return false;
        }
        return true;
    }
}
