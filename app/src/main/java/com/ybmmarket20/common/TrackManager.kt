package com.ybmmarket20.common

import com.ybmmarket20.utils.analysis.XyyIoUtil

/**
 *  老的埋点管理类
 */
@Deprecated("以后不用这个，改用极光埋点")
interface TrackManager {


	companion object{

		/**
		 * 共用属性
		 */
		const val FIELD_OFFSET ="offset" //位置
		const val FIELD_PAGE_ID = "page_id"
		const val FIELD_CONTENT = "content"
		const val FIELD_TITLE = "title"
		const val FIELD_URL = "url"
		const val FIELD_MODE = "mode"
		const val FIELD_TYPE = "type"
		const val FIELD_MODULE = "module"
		const val FIELD_SKU_ID = "sku_id"
		const val FIELD_SKU_NAME = "sku_name"
		const val FIELD_SKU_NUM = "sku_num"
		const val FIELD_SKU_PRICE = "sku_price"
		const val FIELD_BUTTON = "button"


		/**
		 * 共用埋点
		 */

		//加购
		const val EVENT_ACTION_ADDLIST_CLICK = "action_addlist_click"

		/**
		 * 点击事件埋点
		 * @param eventName String
		 * @param contentMap HashMap<String, out Any>
		 */
		fun clickEventTrack(
				eventName: String,
				contentMap: HashMap<String, out Any>
		) {
			if (contentMap.isEmpty()){
				XyyIoUtil.track(eventName)
			}
			XyyIoUtil.track(eventName, contentMap)
		}

		/**
		 * 曝光事件埋点
		 * @param eventName String
		 * @param contentMap HashMap<String, out Any>
		 */
		fun exposureEventTrack(
				eventName: String,
				contentMap: HashMap<String, out Any> = hashMapOf()
		) {
			if (contentMap.isEmpty()){
				XyyIoUtil.track(eventName)
			}else{
				XyyIoUtil.track(eventName, contentMap)
			}
		}
	}


	//模块新手引导 页面 新手引导
	object TrackNewBieGuide {
		const val EVENT_NEXT_STEP_CLICK = "next_step_click" //下一步点击
		const val EVENT_BLANK_CLICK = "blank_click" //空白处点击
		const val EVENT_SKIP_CLICK = "skip_click"  //跳过点击
		const val EVENT_START_PURCHASING_CLICK = "start_purchasing_click" //开始采购点击

		const val EVENT_BEGINNER_GUIDANCE_EXPOSURE = "beginner_guidance_exposure" //新手引导页曝光

	}

	//首页
	object TrackHome{

		const val TRACK_HOME_PAGE_ID = 101
		const val TRACK_HOME_TAB_PAGE_ID = 102

		//这个Module定的有点坑 都不统一， 埋点直接埋具体值得了
		const val TRACK_HOME_MODULE_1 = "1" //推荐楼层
		const val TRACK_HOME_MODULE_2 = "2" //常购清单楼层
		const val TRACK_HOME_MODULE_3 = "3" //超值特惠楼层
		const val TRACK_HOME_MODULE_4 = "4" //商品feed流

		const val EVENT_ACTION_SEARCH_CLICK = "action_search_click" //搜索框点击
		const val EVENT_ACTION_SCAN_CLICK = "action_scan_click" //扫一扫点击
		const val EVENT_ACTION_VOICE_INPUT_CLICK = "action_voice_input_click" //语音输入点击
		const val EVENT_ACTION_MESSAGE_CLICK = "action_message_click" //消息框点击
		const val EVENT_ACTION_TAB_POSITION_CLICK = "action_tab_position_click" //tab位点击
		const val EVENT_ACTION_TAB_CLASSIFICATION_CLICK = "action_tab_classification_click" //tab分类点击
		const val EVENT_ACTION_HEAD_PICTURE_CLICK = "action_head_picture_click" //氛围头图点击
		const val EVENT_ACTION_CAROUSEL_BANNER_CLICK = "action_carousel_banner_click" //胶囊位点击
		const val EVENT_ACTION_ICON_CLICK = "action_icon_click" //icon点击
		const val EVENT_ACTION_TITLE_CLICK = "action_title_click" //标题点击
		const val EVENT_ACTION_BLANK_CLICK = "action_blank_click" //空白点击
		const val EVENT_ACTION_PRODUCT_CLICK = "action_product_click" //商品点击
		const val EVENT_ACTION_CAROUSEL_FEED_CLICK = "action_carousel_feed_click" //轮播feed点击
		const val EVENT_ACTION_FLOAT_CLICK = "action_float_click" //浮窗点击
		const val EVENT_ACTION_FLOAT_OFF_CLICK = "action_float_off_click" //浮窗关闭
		const val EVENT_ACTION_BOTTOM_ADVERTISING_CLICK = "action_bottom_advertising_click" //吸底广告点击
		const val EVENT_ACTION_BOTTOM_ADVERTISING_OFF_CLICK = "action_bottom_advertising_off_click" //吸底广告关闭
		const val EVENT_ACION_TOP_CLICK = "acion_top_click" //底部top点击
		const val EVENT_ACTION_SHOPLIST_CLICK = "action_shoplist_click" //店铺列表点击
		const val EVENT_ACTION_SHOPPING_CART_CLICK = "action_shopping_cart_click" //购物车点击
		const val EVENT_ACTION_ME_CLICK = "action_me_click" //我的点击


		const val EVENT_HOMEPAGE_EXPOSURE = "homepage_exposure" // 首页曝光
		const val EVENT_CAROUSEL_BANNER_EXPOSURE = "carousel_banner_exposure" //胶囊位曝光
		const val EVENT_ICON_EXPOSURE = "icon_exposure" //icon曝光
		const val EVENT_RECOMMENDED_FLOORS_EXPOSURE = "recommended_floors_exposure" //左右推荐楼层曝光
		const val EVENT_SPECIAL_FLOORS_EXPOSURE = "special_floors_exposure" //特惠楼层曝光
		const val EVENT_REGULAR_LIST_EXPOSURE = "regular_list_exposure" //常规清单楼层曝光
		const val EVENT_CAROUSEL_FEED_EXPOSURE = "carousel_feed_exposure" //轮播feed曝光
		const val EVENT_SKU_FEED_EXPOSURE = "sku_feed_exposure" //商品feed流
		const val EVENT_FLOAT_EXPOSURE = "float_exposure" //浮窗曝光
		const val EVENT_BOTTOM_ADVERTISING_EXPOSURE = "bottom_advertising_exposure" //吸底广告曝光
		const val EVENT_HEAD_PICTURE_EXPOSURE = "head_picture_exposure" //氛围头图曝光

	}

	//常购清单页
	object TrackFrequentPurchaseList{

		const val TRACK_FREQUENT_PURCHASE_PAGE_ID = 103
		const val TRACK_FREQUENT_PURCHASE_MODULE_6 = "6" //常购清单

		const val EVENT_ACTION_SHOPPING_CART_CLICK = "action_shopping_cart_click" //购物车点击
		const val EVENT_ACION_TOP_CLICK = "acion_top_click" //置顶点击
		const val EVENT_ACTION_PRODUCT_CLICK = "action_product_click" //商品点击

		const val EVENT_FREQUENT_PURCHASE_LIST_EXPOSURE = "frequent_purchase_list_exposure" //常购清单曝光

	}

}