package com.ybmmarket20.common

object LiveEventBusManager {

	interface HomeBus{

		companion object{

			//是否显示大转盘悬浮窗
			const val  BUS_SHOW_BIG_WHEEL = "bus_show_big_wheel"

		}

	}

	interface OrderBus{
		companion object{
			//待支付订单气泡
			const val  NO_PAY_ORDERS_BUBBLE = "NO_PAY_ORDERS_BUBBLE"
		}
	}

	interface GiftSelect{
		companion object{

			//选择赠品Tab标签状态

			const val  TAB_TAG_GIFT_SELECT = "tab_tag_gift_select"

		}
	}
}