package com.ybmmarket20.common;

import android.text.TextUtils;

import com.ybm.app.bean.TimeLog;
import com.ybm.app.utils.SpUtil;
import com.ybmmarket20.bean.DebugAPIBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.FormBody;
import okhttp3.Request;


/**
 * 开发者管理
 */
public class DebugManager {

    private static DebugManager mInstance = null;
    private List<DebugAPIBean> apis = new ArrayList<>();
    private Map<String,String> crashs = new HashMap<>();
    private boolean isSaveApi = false;
    private boolean isSaveCrash = false;
    public final static String SAVECRASH = "ISSAVECRASH";
    public final static String SAVEAPI = "ISSAVEAPI";
    private DebugManager() {
        isSaveApi = YBMAppLike.getApp().isDebug() && SpUtil.readBoolean(SAVEAPI,false);
        isSaveCrash =  SpUtil.readBoolean(SAVECRASH,false);
    }

    /**
     * 返回单例对象
     *
     * @return
     */
    public static DebugManager getInstance() {
        if (null == mInstance) {
            synchronized (DebugManager.class) {
                if (null == mInstance) {
                    mInstance = new DebugManager();
                }
            }
        }
        return mInstance;
    }

    public void addApis(String content, Request request,boolean success){
        if(request == null){
            return;
        }
        if(!isSaveApi()){
            return;
        }
        String url = request.url().toString();
        if(TextUtils.isEmpty(url)){
            return;
        }
        DebugAPIBean bean = new DebugAPIBean();
        bean.url = url;
        bean.succes = success;
        bean.response = content;
        if(request.body() !=null && request.body() instanceof FormBody) {
            FormBody formBody = (FormBody) request.body();
            StringBuffer sb = new StringBuffer();
            for(int a=0;a<100;a++){
                if(a ==0) {
                    sb.append("{");
                }else {
                    try {
                        sb.append(formBody.name(a)).append(":").append(formBody.value(a)).append(",");
                    }catch (IndexOutOfBoundsException e){
                        sb.append("}");
                        break;
                    }
                }
            }
            bean.params = sb.toString();
        }
       apis.add(0,bean);
    }

    public List<DebugAPIBean> getApis(){
        return apis;
    }

    public boolean isSaveApi(){
        return isSaveApi;
    }

    public void setSaveApi(boolean isSaveApi){
        if(YBMAppLike.getApp().isDebug() && isSaveApi){
            this.isSaveApi = true;
        }else {
            this.isSaveApi = false;
        }
        SpUtil.writeBoolean(SAVEAPI,this.isSaveApi);
    }


    public boolean isSaveCrash(){
        return isSaveCrash;
    }

    public void setSaveCrash(boolean isSaveCrash){
        this.isSaveCrash = isSaveCrash;
        SpUtil.writeBoolean(SAVECRASH,isSaveCrash);
    }
}
