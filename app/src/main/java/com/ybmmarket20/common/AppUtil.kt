package com.ybmmarket20.common

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.TypedValue
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybmmarket20.R
import com.ybmmarket20.utils.SpUtil
import java.text.SimpleDateFormat
import java.util.Date
import androidx.fragment.app.Fragment as Fragment1


/**
 * <AUTHOR>
 * @date 2024-04-07
 * @description App Kotlin拓展方法文件
 */

val Float.dp: Int
    get() =
        TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, this, Resources.getSystem().displayMetrics).toInt()

val Int.dp: Int
    get() = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, this.toFloat(), Resources.getSystem().displayMetrics).toInt()

//判断是否登录
fun isLogin():Boolean{
    val merchantId = SpUtil.getMerchantid()
    return !TextUtils.isEmpty(merchantId)
}

@ColorInt
fun View.getColorById(@ColorRes colorId: Int) = ContextCompat.getColor(this.context, colorId)
@ColorInt
fun Context.getColorById(@ColorRes colorId: Int) = ContextCompat.getColor(this, colorId)

@ColorInt
fun FragmentActivity.getColorById(@ColorRes colorId: Int) = ContextCompat.getColor(this, colorId)

@ColorInt
fun Fragment.getColorById(@ColorRes colorId: Int) = requireActivity().getColorById(colorId)


fun Context.glideLoadWithPlaceHolder(
        url: String,
        iv: ImageView,
        placeholderResourceId: Int = R.drawable.jiazaitu_min,
        errorResourceId: Int = R.drawable.jiazaitu_min,
        isCenterCrop: Boolean = false
) {
    if (isCenterCrop) {
        Glide.with(this).load(url).centerCrop().placeholder(placeholderResourceId).error(errorResourceId).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(iv)
    } else {
        Glide.with(this).load(url).placeholder(placeholderResourceId).error(errorResourceId).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(iv)
    }
}

/**
 * 这个方法只高亮第一个匹配的文案
 * @param originalText String
 * @param highlightText String
 * @param colorRes Int
 * @return SpannableStringBuilder
 */
fun highLightText(originalText:String,highlightText:String,@ColorInt colorRes: Int):SpannableStringBuilder {
    if (highlightText.isEmpty()){
        return SpannableStringBuilder(originalText)
    }

    val startIndex: Int = originalText.indexOf(highlightText)
    val endIndex: Int = startIndex + highlightText.length
    if (startIndex == -1){ //未匹配到
        return SpannableStringBuilder(originalText)
    }

    // 创建一个SpannableString对象
    val spannableString = SpannableStringBuilder(originalText)
    // 创建一个ForegroundColorSpan对象，设置高亮的颜色
    val colorSpan = ForegroundColorSpan(colorRes)
    // 将ForegroundColorSpan应用于特定的文本范围
    spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    return  spannableString
}

fun Context.glideLoad(
        url: String,
        iv: ImageView
) {
    Glide.with(this).load(url).into(iv)
}


fun ViewPager2.init(
        fragment: Fragment1,
        fragments: ArrayList<out Fragment>,
        isUserInputEnabled: Boolean = true,
        offscreenPageLimit: Int = 0,
): ViewPager2 {
    //是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    this.offscreenPageLimit = if (offscreenPageLimit == 0) fragments.size + 1 else offscreenPageLimit

    //设置适配器
    adapter = object : FragmentStateAdapter(fragment) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
        override fun getItemId(position: Int): Long {
            return fragments[position].hashCode().toLong()
        }

        override fun containsItem(itemId: Long): Boolean {
            return fragments.map { hashCode().toLong() }.contains(itemId)
        }

    }
    return this
}

@JvmOverloads
fun ViewPager2.init(
        activity: FragmentActivity,
        fragments: List<Fragment>,
        isUserInputEnabled: Boolean = true,
        offscreenPageLimit: Int = 0,
): ViewPager2 {
    //是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    this.offscreenPageLimit = if (offscreenPageLimit == 0) fragments.size + 1 else offscreenPageLimit
    //设置适配器
    adapter = object : FragmentStateAdapter(activity) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
        override fun getItemId(position: Int): Long {
            return (fragments[position].hashCode()).toLong()
        }

        override fun containsItem(itemId: Long): Boolean {
            return fragments.map { hashCode().toLong() }.contains(itemId)
        }

    }
    return this
}

//时间戳转换成日期
fun transform2Date(pattern:String = "yyyy-MM-dd HH:mm:ss"):String{
    try {
        // 获取系统当前时间戳
        val currentTimeMillis = System.currentTimeMillis()
        // 创建SimpleDateFormat对象，并设置日期时间格式
        val sdf = SimpleDateFormat(pattern)
        // 将系统时间戳转换为日期时间字符串
        return sdf.format(Date(currentTimeMillis))
    } catch (e: Exception) {
        e.printStackTrace()
        return ""
    }

}

fun Any.getFullClassName() = this::class.java.name?:""

/**
 * 字符串数组转换成逗号拼接的字符串
 * @receiver Array<String>
 * @return String
 */
fun ArrayList<String>.toCommaSeparatedString(): String {
    return this.filter { it.isNotBlank() } // 过滤掉空格和空字符串
            .joinToString(",") // 使用逗号作为分隔符连接字符串
}

/**
 * 手机号添加**敏感
 * @receiver String
 * @return String
 */
fun String.phoneSensitive():String{
    return try {
        this.replaceRange(3,7,"****")
    }catch (e:Exception){
        e.printStackTrace()
        this
    }

}
