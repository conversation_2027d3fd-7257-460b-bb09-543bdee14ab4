package com.videolive.im

import android.text.TextUtils
import com.apkfuns.logutils.LogUtils
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.ybmmarket20.bean.im.*
import com.ybmmarket20.utils.im.core.data.MemberInfo
import org.json.JSONObject

/**
 * <AUTHOR>
 * IM 业务功能封装
 */

const val VIDEO_lIVE_TYPE_LIVE_STATUS = "liveStatus"            //直播状态
const val VIDEO_lIVE_TYPE_LIVE_COUPON = "couponMsg"             //优惠券下发
const val VIDEO_lIVE_TYPE_LIVE_DISCOUNT_GOODS = "productMsg"    //优惠商品下发
const val VIDEO_lIVE_TYPE_LIVE_MEMBER_ENTER = "joinMember"      //直播状态
const val VIDEO_lIVE_TYPE_LIVE_MEMBER_LEAVE = "leaveMember"     //直播状态
const val VIDEO_lIVE_TYPE_LIVE_FAVOR = "memberLiked"            //直播状态

class VideoLiveMessageReceiver(private var videoLiveMessageCallback: IVideoLiveMessageReceiver?) {

    constructor() : this(null)

    fun setVideoLiveMessageReceiver(videoLiveMessageCallback: IVideoLiveMessageReceiver?) {
        this.videoLiveMessageCallback = videoLiveMessageCallback
    }

    /**
     * 接收系统消息
     */
    fun onReceiveCustomSystemMessage(groupId: String?, customData: ByteArray?) {
        if (!TextUtils.isEmpty(groupId) && customData != null) {
            val jsonStr = String(customData)
            LogUtils.tag("onReceiveCustomSystemMessage").d(jsonStr)
            val jobj = JSONObject(jsonStr)
            val messageType = jobj.get("MessageType") ?: jobj.get("messageType")

            when (messageType) {
                VIDEO_lIVE_TYPE_LIVE_STATUS -> {
                    val bean = Gson().fromJson(jsonStr, VideoLiveMessageStatusBean::class.java)
                    bean.groupId = groupId
                    videoLiveMessageCallback?.onVideoLiveStateChange(bean)
                }
                VIDEO_lIVE_TYPE_LIVE_COUPON -> {
                    val bean = Gson().fromJson(jsonStr, VideoLiveMessageCouponBean::class.java)
                    bean.groupId = groupId
                    videoLiveMessageCallback?.onVideoLiveDispatchCoupon(bean)
                }
                VIDEO_lIVE_TYPE_LIVE_DISCOUNT_GOODS -> {
                    val bean = Gson().fromJson(jsonStr, VideoLiveMessageGoodsBean::class.java)
                    bean.groupId = groupId
                    videoLiveMessageCallback?.onVideoLiveDispatchDiscountGoods(bean)
                }
            }

        }
    }

    /**
     * 接收群自定义消息
     */
    fun onReceiveCustomGroupMessage(msgID: String?, groupID: String?, sender: MemberInfo?, customData: ByteArray?) {
        if (!TextUtils.isEmpty(msgID) && !TextUtils.isEmpty(groupID) && sender != null && customData != null) {
//            val jsonStr = String(customData)
//            val t: VideoLiveMessageJoinMemberBean? = Gson().fromJson(jsonStr, VideoLiveMessageJoinMemberBean::class.java)
//            t?.also {
//                t.groupId = groupID
//                t.msgId = msgID
//                t.memberInfo = sender
//                when (t.messageType) {
//                    VIDEO_lIVE_TYPE_LIVE_MEMBER_ENTER -> videoLiveMessageCallback?.onVideoLiveMemberEnter(t)
//                    VIDEO_lIVE_TYPE_LIVE_MEMBER_LEAVE -> videoLiveMessageCallback?.onVideoLiveMemberLeave(t)
//                    VIDEO_lIVE_TYPE_LIVE_FAVOR -> videoLiveMessageCallback?.onVideoLiveFavor(t)
//                }
//            }
        }
    }

    /**
     * 接收群文本消息
     */
    fun onReceiveTextGroupMessage(msgID: String?, groupID: String?, sender: MemberInfo?, text: String?) {
        var messageType:String? =null
        LogUtils.i("im text= ${text}")
        try {
            val jsonObject = JSONObject(text)
            messageType = jsonObject.getString("MessageType")
        } catch (e: Exception) {
            LogUtils.i("im "+e.toString())
        }
        if (!TextUtils.isEmpty(messageType)){
            val t: VideoLiveMessageJoinMemberBean? = Gson().fromJson(text, VideoLiveMessageJoinMemberBean::class.java)
            t?.also {
                t.groupId = groupID
                t.msgId = msgID
                t.memberInfo = sender
                when (messageType) {
                    VIDEO_lIVE_TYPE_LIVE_MEMBER_ENTER -> videoLiveMessageCallback?.onVideoLiveMemberEnter(t)
                    VIDEO_lIVE_TYPE_LIVE_FAVOR -> videoLiveMessageCallback?.onVideoLiveFavor(t)
                }
            }
        }else{
            videoLiveMessageCallback?.onReceiveGroupTextMessage(msgID, groupID, sender, text)
        }
    }

}