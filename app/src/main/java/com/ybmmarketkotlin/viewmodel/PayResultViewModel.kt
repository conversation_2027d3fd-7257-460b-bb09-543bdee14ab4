package com.ybmmarketkotlin.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.BoothData
import com.ybmmarket20.bean.JDPWBean
import com.ybmmarket20.bean.OpenRedEnvelopData
import com.ybmmarket20.bean.PayResultBean
import com.ybmmarket20.bean.RefreshWrapperPagerBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.bean.payment.PayResultButtonBean
import com.ybmmarket20.bean.payment.PaymentConsumeRebateDetailBean
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.network.request.PayResultRequest
import com.ybmmarket20.network.request.QueryDeviceStatusOnPayRequest
import com.ybmmarket20.utils.FingerprintUtil
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarketkotlin.bean.RebateVoucherBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PayResultViewModel(val appLike: Application) : BaseViewModel(appLike) {

    val payResultViewModel = MutableLiveData<PayResultBean>()
    val recommendViewModel = MutableLiveData<RefreshWrapperPagerBean<RowsBean>>()
    val payListRecommendViewModel = MutableLiveData<SearchResultOPBean>()
    val rebateVoucherViewModel = MutableLiveData<RebateVoucherBean>()
    val boothDataViewModel = MutableLiveData<BoothData>()
    private val _openRedEnvelopeLiveData = MutableLiveData<BaseBean<OpenRedEnvelopData>>()
    val openRedEnvelopeLiveData: LiveData<BaseBean<OpenRedEnvelopData>> = _openRedEnvelopeLiveData
    private val _showFingerprintDialogLiveData = MutableLiveData<Boolean>()
    val showFingerprintDialogLiveData: LiveData<Boolean> = _showFingerprintDialogLiveData
    private val _submitVoucherLiveData = MutableLiveData<Boolean>()
    val submitVoucherLiveData: LiveData<Boolean> = _submitVoucherLiveData
    private val _rebateRatioLiveData = MutableLiveData<BaseBean<PaymentConsumeRebateDetailBean>>()
    val rebateRatioLiveData: LiveData<BaseBean<PaymentConsumeRebateDetailBean>> = _rebateRatioLiveData

    private val _payResultButtonLiveData = MutableLiveData<BaseBean<PayResultButtonBean>>()
    val payResultButtonLiveData: LiveData<BaseBean<PayResultButtonBean>> = _payResultButtonLiveData


    //是否设置支付密码
    private val _jDPWSettingLiveData = MutableLiveData<BaseBean<JDPWBean>>()
    val jDPWSettingLiveData: LiveData<BaseBean<JDPWBean>> = _jDPWSettingLiveData

    /**
     * 是否设置支付密码
     */
    fun queryPWSettingStatus() {
        viewModelScope.launch {
            val queryPWSettingStatus = JDPayRequest().queryPWSettingStatus()
            _jDPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    fun getPayResultInfo(params: Map<String, String>): Unit {
        viewModelScope.launch {
            val payResultBean = PayResultRequest().getPayResultInfo(params)
            payResultViewModel.postValue(payResultBean?.data)
        }
    }

    fun getRebateRatioData(orderNo:String) {
        viewModelScope.launch {
            val rebateRatioLiveInfo = PayResultRequest().getRebateRatioData(orderNo)
            _rebateRatioLiveData.postValue(rebateRatioLiveInfo)
        }
    }

    fun getRecommendGoodlist(params: Map<String, String>){
        viewModelScope.launch {
            val recommendData = PayResultRequest().getRecommendGoodlist(params)
            // 数据不为空再进行下一步操作
            recommendData?.data?.let { data ->
                recommendViewModel.postValue(data)
            }
        }
    }

    fun getPayListRecommendGoodlist(params: Map<String, String>){
        viewModelScope.launch {
            val recommendData = PayResultRequest().getPayListRecommendGoodlist(params)
            // 数据不为空再进行下一步操作
            recommendData?.data?.let { data ->
                payListRecommendViewModel.postValue(data)
            }
        }
    }

    fun getRebateVoucherList(params: Map<String, String>){
        viewModelScope.launch {
            val rebateVoucher = PayResultRequest().getRebateVoucher(params)
            // 数据不为空再进行下一步操作
            rebateVoucher?.data?.let { data ->
                rebateVoucherViewModel.postValue(data)
            }
        }
    }

    fun getBoothData(params: Map<String, String>){
        viewModelScope.launch {
            val rebateVoucher = PayResultRequest().getBoothData(params)
            // 数据不为空再进行下一步操作
            rebateVoucher?.data?.let { data ->
                boothDataViewModel.postValue(data)
            }
        }
    }

    fun submitVoucher(orderId:String,merchantId:String,evidenceImages: ArrayList<String>){
        viewModelScope.launch {
            showLoading()
            val rebateVoucher = PayResultRequest().submitVoucher(orderId,merchantId, evidenceImages)
            _submitVoucherLiveData.postValue(rebateVoucher?.isSuccess)
            dismissLoading()
        }
    }

    /**
     * 通过拦截获取活动优惠券弹框
     */
    fun getActivityDialog(orderNo: String, merchantId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            PayResultRequest().getActivityDialog(orderNo, merchantId)
        }
    }

    /**
     * 领取红包
     */
    fun openRedEnvelope(map: Map<String, String>, position: Int) {
        viewModelScope.launch {
            val openRedEnvelopeData = PayResultRequest().openRedEnvelope(map)
            _openRedEnvelopeLiveData.postValue(openRedEnvelopeData)
            val data = openRedEnvelopeData.data
            rebateVoucherViewModel.value?.list?.let {
                val item = it[position]
                if (item.rightsType == 2 &&openRedEnvelopeData.isSuccess && data != null) {
                    //红包
                    if (data.result?.isFinished == 0) {
                        //立即使用
                        item.activityState = 3
                        item.redPacketOriginMoney = data.result?.redPacket?.originMoney?: ""
                    } else {
                        //已抢光
                        item.activityState = 5
                    }
                    rebateVoucherViewModel.postValue(rebateVoucherViewModel.value)
                }
            }
        }
    }

    fun showFingerprintDialog(payCode: String) {
        viewModelScope.launch {
            if (payCode != "jdCardPay") return@launch
            val deviceStatusOnPay = QueryDeviceStatusOnPayRequest().queryDeviceStatusOnPay()
            if (deviceStatusOnPay.isSuccess) {
                //后端是否配置显示开启或关闭指纹选项
                val isOpenFingerprintSwitch = deviceStatusOnPay.data.switchStatus == 1
                if (!isOpenFingerprintSwitch) return@launch
                //是否已经设置指纹支付
                val isOpenFingerprintForPay = deviceStatusOnPay.data.deviceStatus == 1
                if (isOpenFingerprintForPay) return@launch
                //设备是否支持指纹支付
                val isDeviceSupportedFingerPrint = FingerprintUtil.checkFingerprintSupported(appLike)
                if (!isDeviceSupportedFingerPrint) return@launch
                _showFingerprintDialogLiveData.postValue(true)
            }
        }
    }
}