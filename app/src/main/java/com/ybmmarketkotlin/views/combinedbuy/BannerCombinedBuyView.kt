package com.ybmmarketkotlin.views.combinedbuy

import android.content.Context
import android.util.AttributeSet
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.MarginPageTransformer
import com.xyy.canary.utils.DensityUtil
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.GroupPurchaseInfoCopy
import com.ybmmarket20.common.YBMAppLike
import com.youth.banner.Banner
import com.youth.banner.listener.OnPageChangeListener

/**
 * <AUTHOR>
 * @desc    组合购bannerView
 */
class BannerCombinedBuyView @JvmOverloads constructor(context: Context, attr: AttributeSet? = null) :
    Banner<GroupPurchaseInfo, CombinedBannerAdapter>(context, attr) {
    var mListener: CombinedBuyListener? = null
    var adapter: CombinedBannerAdapter? = null

    fun updateData(purchaseInfo: GroupPurchaseInfo){
        adapter?.updateData(purchaseInfo)
    }

    /**
     * 是否自动滚动
     * 是否可滑动
     */
    fun setData(purchaseInfo: GroupPurchaseInfoCopy,listener:CombinedBuyListener?,curPageIndex:Int = 0){
        adapter = CombinedBannerAdapter(context, purchaseInfo.groupPurchaseInfos)
        adapter!!.banner = this
        adapter!!.curPageIndex = curPageIndex
        adapter!!.mListener = listener
        mListener = listener
        if(context is LifecycleOwner){
            addBannerLifecycleObserver(context as LifecycleOwner)
        }
        setAdapter(adapter,true)
        if(purchaseInfo.groupPurchaseInfos[0].carouselTime == null || purchaseInfo.groupPurchaseInfos[0].carouselTime <= 4){
            setLoopTime(4000L)
        }else{
            setLoopTime((purchaseInfo.groupPurchaseInfos[0].carouselTime?:4) * 1000L)
        }
        addOnPageChangeListener(object : OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            }

            override fun onPageSelected(position: Int) {
                adapter!!.curPageIndex = position
                requestDetail(purchaseInfo, position,false)
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
        setCurrentItem(curPageIndex + 1,false)
        setBannerTransformer()
    }

    /**
     * 设置一屏多显
     */
    private fun setBannerTransformer() {
        viewPager2.apply {
            clipChildren = false
            (getChildAt(0) as RecyclerView).apply {
                clipChildren = false
                clipToPadding = false
                setPadding(DensityUtil.dip2px(YBMAppLike.getAppContext(), 14f), 0, DensityUtil.dip2px(YBMAppLike.getAppContext(), 14f), 0)
            }
            setPageTransformer(MarginPageTransformer(DensityUtil.dip2px(YBMAppLike.getAppContext(), 4f)))
        }
    }

    /**
     * 请求详细信息
     */
    fun requestDetail(purchaseInfo: GroupPurchaseInfoCopy, position: Int,firstFlag:Boolean = false) {
        preRequest(purchaseInfo,position,-1,firstFlag)
        preRequest(purchaseInfo,position+1,position,firstFlag)
        if(purchaseInfo.groupPurchaseInfos.size > 2){
            preRequest(purchaseInfo,position-1,position,firstFlag)
        }
        mListener?.pageSeleted(adapter!!.curPageIndex, purchaseInfo.groupPurchaseInfos[adapter!!.curPageIndex])
    }

    /**
     * position：数据加载下标
     * prePos: 之前的下标
     */
    fun preRequest(purchaseInfo: GroupPurchaseInfoCopy, position: Int,prePos:Int = -1,firstFlag:Boolean = false){
        val newPos = if(position > purchaseInfo.groupPurchaseInfos.size - 1){
            0
        }else if(position < 0){
            purchaseInfo.groupPurchaseInfos.size - 1
        }else{
            position
        }
        // 未请求过
        if (!purchaseInfo.groupPurchaseInfos[newPos].requestFlag) {
            val prePosition = if(prePos == -1){
                if (newPos - 1 < 0) purchaseInfo.groupPurchaseInfos.size - 1 else newPos - 1
            }else{
                prePos
            }
            mListener?.refresh(
                purchaseInfo.groupPurchaseInfos[prePosition].subProducts[0],
                prePosition,
                purchaseInfo.groupPurchaseInfos[newPos].subProducts[0],
                newPos,
                firstFlag
            )
        }
    }
}