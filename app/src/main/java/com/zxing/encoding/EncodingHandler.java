package com.zxing.encoding;

import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;

import java.util.Hashtable;

import retrofit2.http.HEAD;

/**
 * <AUTHOR>
 *
 */
public final class EncodingHandler {
	private static final int BLACK = 0xff000000;
	private static final int WIDTH = 0xffffffff;
	
	public static Bitmap createQRCode(String str,int widthAndHeight , Bitmap ico) throws WriterException {
		Hashtable<EncodeHintType, String> hints = new Hashtable<EncodeHintType, String>();  
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8"); 
		BitMatrix matrix = new MultiFormatWriter().encode(str,
				BarcodeFormat.QR_CODE, widthAndHeight, widthAndHeight,hints);		
		
		int width = matrix.getWidth();
		int height = matrix.getHeight();
		int[] pixels = new int[width * height];
		
		for (int y = 0; y < height; y++) {
			for (int x = 0; x < width; x++) {
				if (matrix.get(x, y)) {
					pixels[y * width + x] = BLACK;
				}else {
					pixels[y * width + x] = WIDTH;
				}
			}
		}
		Bitmap bitmap = Bitmap.createBitmap(width, height,
				Config.ARGB_8888);
		
		bitmap.setPixels(pixels, 0, width, 0, 0, width, height);
		
		return createBitmap(bitmap,ico);
		
		//return bitmap;
	}
	
	private static Bitmap createBitmap(Bitmap src,Bitmap watermark) {
	     if( src == null ) {
	         return null;
	     }
	     int w = src.getWidth();
	     int h = src.getHeight();
	     int ww = watermark.getWidth();
	     int wh = watermark.getHeight();
	     //create the new blank bitmap
	     Bitmap newb = Bitmap.createBitmap( w, h, Config.ARGB_8888 );//创建一个新的和SRC长度宽度一样的位图
	     android.graphics.Canvas cv = new android.graphics.Canvas( newb );
	     //draw src into
	     cv.drawBitmap( src, 0, 0, null );//在 0，0坐标开始画入src
	     //draw watermark into
	     cv.drawBitmap( watermark, (w - ww)/2, (h - wh)/2, null );//在src画入水印
	     //save all clip   
	     cv.save();//保存android.graphics.Canvas.ALL_SAVE_FLAG
	     //store
	     cv.restore();//存储
	     return newb;
	}
}
